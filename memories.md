# Core Protocol and Behavior
- Answer in Chinese-Simplified, using RIPER-5 + O1 THINKING + AGENT EXECUTION PROTOCOL (OPTIMIZED).
- Apply Systems Thinking, Dialectical Thinking, Innovative Thinking, and Critical Thinking in all modes.
- 用户偏好简洁直接的解决方案，不要过于复杂的代码实现或不必要的修改。
- Use constant resource URL prefix 'https://z.fee.red/nebula' consistently.

# Code and Development Standards
- Use appropriate code block structure with contextual comments, showing only necessary modifications.
- Abstract common repository methods into a base class. Prefer official Python SDKs and asynchronous HTTP requests.
- 用户偏好在processor中不处理输入验证逻辑，如空文本检查应在其他层处理。
- 用户偏好在开发环境不压缩代码，但在生产构建时需要压缩。
- User prefers using Vite's hot reload feature instead of restarting Docker containers during development.
- User prefers to ignore MP4 files in git commits while committing other changes.
- 用户偏好使用action.submit方法而不是直接调用API。

# Frontend Architecture
- User is replacing frontend-web with trimmed-down vue-vben-admin, preferring web-antd (Ant Design Vue) over web-ele.
- 用户需要将@frontend-web/js/app.js中的逻辑(包括App.ui.notify和getAuthHeaders)迁移到新代码库。
- User prefers using module.ts files instead of module/index.ts directory structure in web-nebula codebase.
- User prefers organizing storage functionality into permanent and temporary storage menu items.
- User prefers implementing second-level menus through parameters rather than separate component files.
- User prefers hiding page switching buttons and filtering information based on parameters.
- The 'Sync Data' button in the top right corner should trigger data synchronization functionality, not API validation.
- User prefers placing account settings in a drawer that can be opened via a 'Set Account' button positioned next to the 'Sync Data' button in the top right corner.
- 用户偏好在前端实现数据持久化功能。

# UI Components and Styling
- User prefers placing file upload components in drawers opened via buttons rather than displaying directly on page.
- 用户偏好抽屉组件从上往下拉出，而不是从侧面滑出。
- 用户偏好抽屉组件在移动端从顶端展开，在PC端从右侧展开。
- 用户偏好抽屉组件在storage部分也根据设备类型自动调整位置：移动端从顶部展开，PC端从右侧展开。
- User prefers using 'open' prop instead of 'visible' prop in Ant Design Vue Modal components.
- User prefers placing DNS record addition forms in drawer components rather than modal dialogs.
- 用户偏好在查看图片文件时使用图片预览功能，带灰色透明背景笼罩，图片居中显示。
- 用户偏好按钮在界面中占满整行宽度，而不是接近但未满100%宽度的设计。
- 用户偏好移动端抽屉组件高度更高，尽量避免内容需要滚动。
- 用户偏好在表单内容上显示loading状态，而不是在按钮上。
- 用户偏好在表单提交时显示loading状态并使确定按钮变为不可用状态。
- 用户偏好将所有通知(notify)显示在右下角。
- 用户偏好通知框列表内容设计为：1.消息内容包含icon/message/time 2.时间显示格式使用'刚刚/前n分钟/昨天'
- 用户偏好通知组件中的单条消息记录更紧凑，使用小图标（如success/error）并且不显示标题。
- 用户偏好通知组件中的单条消息记录更和谐的样式，认为当前message内容展示不够协调。
- 用户偏好通知组件中的单条消息记录使用更小的字体。
- 用户偏好在通知列表中保留一些分隔元素，而不是完全移除。
- 用户偏好在表格中使用整行loading状态，而不是每个单元格单独显示loading图标。
- 用户偏好在表格中只禁用表单字段而不是整行记录。
- 用户偏好表格中的内容列宽度更窄，不要过宽。
- 用户偏好表格中的名称列宽度更窄，不要过宽。
- 用户偏好在数据同步操作时将loading状态应用于整个主数据表，而不是子表。
- 用户偏好验证API完成后不要自动关闭抽屉。
- 用户偏好子表不使用边框(border)，并且认为子表左侧边距过宽需要调整。
- 用户偏好子表在移动端能够完整显示所有列，并且不希望页面底部出现多余的分页，同时认为图片在页面上显示的宽度过大。
- 用户偏好子表可拖动且操作列固定，而父表不需要特殊设置。
- 用户偏好在操作成功后(如Cloudflare DNS添加)向通知组件添加消息，实现操作反馈。
- 用户偏好通知徽标显示未读消息数量而不是简单的红点提示。
- 用户偏好将notificationService改名为'通知盒子'，认为这个中文名称更贴切。
- 用户偏好通知组件中能够清晰区分未读和已读消息的设计。

# Video and Media Display
- Prefer waterfall-style video lists with HTML5 video tags. Max 4 columns, smaller items, original aspect ratios.
- Videos should not autoplay when opening details dialog. Use TDesign components for styling.
- 用户希望界面工具栏增加分享文本输入框和抓取按钮，用于分析并下载抖音/YouTube分享的视频内容。
- In video dialog, use 'cover_url' for video cover and display video player after extraction.
- User prefers that clicking 'Save Video' button should close dialog and show submission notification.
- 用户偏好将上传到YouTube的视频设置为公开状态而非私有。

# API and Data Handling
- Repository's save operation should maintain date type for updated_at. Use .dict() instead of .to_dict().
- Rename MongoDB field 'uptime' to 'updated_at' in video collection. Sort videos by updated_at descending by default.
- 用户偏好将API endpoint配置为http://192.168.16.160/api，保留原有配置除非明确指示更改。
- User prefers adapting frontend API to work with backend login endpoint at /api/auth.
- User prefers in frontend handling storage API's pagination logic, rather than in backend API layer implementing pagination and counting function.

# WebSocket Implementation
- 用户偏好将WebSocket endpoint配置为ws://192.168.16.161:8092/ws，接口格式为{endpoint}/{user}/{token}。
- User prefers using existing useWebSocket from @vueuse/core rather than implementing custom wrapper.
- 用户偏好在WebSocket连接成功后自动订阅GENERAL主题，保留60秒间隔的心跳机制。
- 用户偏好WebSocket服务层只负责底层连接维护，业务逻辑应由上层调用其提供的接口来实现。
- User identified issues with singleton implementation in websocket.ts and prefers proper singleton pattern.

# Integration Services
- Integrate Gemini AI model with true streaming, configured in @nebula/api/config.yaml.
- Integrate Playwright with base class library for HTML extraction, using Docker image 'mcr.microsoft.com/playwright:v1.51.1-jammy'.
- User prefers using proxy 192.168.32.12:1070 for Playwright browser operations.
- User prefers dedicated API endpoint for saving videos from extracted share links.