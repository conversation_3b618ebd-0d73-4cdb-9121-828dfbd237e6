user www-data;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
        worker_connections 2048;
        # multi_accept on;
}

http {

        ##
        # Basic Settings
        ##

        sendfile on;
        tcp_nopush on;
        types_hash_max_size 2048;
        # server_tokens off;

        # server_names_hash_bucket_size 64;
        # server_name_in_redirect off;

        include /etc/nginx/mime.types;
        # default_type application/octet-stream;

        ##
        # SSL Settings
        ##

        ssl_protocols TLSv1 TLSv1.1 TLSv1.2 TLSv1.3; # Dropping SSLv3, ref: POODLE
        ssl_prefer_server_ciphers on;

        ##
        # Logging Settings
        # log_format json '{"time_local": "$time_local", '
        #             '"remote_addr": "$remote_addr", '
        #             '"request": "$request", '
        #             '"status": "$status", '
        #             '"body_bytes_sent": "$body_bytes_sent", '
        #             '"http_referer": "$http_referer", '
        #             '"http_user_agent": "$http_user_agent", '
        #             '"request_time": "$request_time"}';
        ##
        log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

        access_log  /var/log/nginx/access.log  main;

        #access_log /dev/stdout json;
        error_log /dev/stderr;

        map $http_upgrade $connection_upgrade {
                default upgrade;
                ''      "";
        }

        ##
        # Gzip Settings
        ##

        gzip on;

        client_max_body_size 3096m;
        client_body_buffer_size 3096m;

        # gzip_vary on;
        # gzip_proxied any;
        # gzip_comp_level 6;
        # gzip_buffers 16 8k;
        # gzip_http_version 1.1;
        # gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

        ##
        # Virtual Host Configs
        ##

        include /etc/nginx/conf.d/*.conf;
        # include /etc/nginx/sites-enabled/*;
}