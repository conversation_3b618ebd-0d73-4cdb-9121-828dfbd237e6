server {
    listen 80 default_server;
    server_name _;

    root /usr/share/nginx/html/www;
    index index.html index.htm;

    # 为该站点单独分配日志
    access_log /var/log/nginx/nebula_access.log;
    error_log /var/log/nginx/nebula_error.log;

    # location / {
    #     return 200 '<h1>Welcome to Nebula!</h1>';
    # }

    location /healthcheck {
        return 200 'OK';
    }

    location /favicon.ico { # 禁止访问 favicon.ico
        return 200;
    }

    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}







