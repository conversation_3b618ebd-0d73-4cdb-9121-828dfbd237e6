# 第三方组件或服务的代理 (无法修改其安全策略iframe/content-src等原因)


server {
    listen 443 ssl;
    server_name router.nebula.fee.red;
    ssl_certificate cert/fee.red/fullchain.cer;
    ssl_certificate_key cert/fee.red/fee.red.key;

    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
    ssl_prefer_server_ciphers on;

    proxy_set_header Host $host:$server_port;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header REMOTE-HOST $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    proxy_hide_header X-Frame-Options;
    proxy_hide_header Content-Security-Policy;

    location / {
        proxy_pass https://op.fee.red/;
    }
}

server {
    listen 443 ssl;
    server_name pan.router.nebula.fee.red;
    ssl_certificate cert/fee.red/fullchain.cer;
    ssl_certificate_key cert/fee.red/fee.red.key;

    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
    ssl_prefer_server_ciphers on;

    proxy_set_header Host $host:$server_port;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header REMOTE-HOST $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    proxy_hide_header X-Frame-Options;
    proxy_hide_header Content-Security-Policy;

    location / {
        proxy_pass https://*************/;
    }
}

server {
    listen 443 ssl;
    server_name home-assistant.nebula.fee.red;
    ssl_certificate cert/fee.red/fullchain.cer;
    ssl_certificate_key cert/fee.red/fee.red.key;

    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
    ssl_prefer_server_ciphers on;

    proxy_set_header Host $host:$server_port;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header REMOTE-HOST $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    proxy_hide_header X-Frame-Options;
    proxy_hide_header Content-Security-Policy;

    location / {
        proxy_pass http://nas.fee.red:8123/;

        # 添加 WebSocket 支持
        proxy_read_timeout 60s;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'Upgrade';
    }
}

server {
    listen 443 ssl;
    server_name rabbit.nebula.fee.red;
    ssl_certificate cert/fee.red/fullchain.cer;
    ssl_certificate_key cert/fee.red/fee.red.key;

    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
    ssl_prefer_server_ciphers on;

    proxy_set_header Host $host:$server_port;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header REMOTE-HOST $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    location / {
        proxy_pass http://rabbit.fee.red:15672/;

        # 添加 WebSocket 支持
        proxy_read_timeout 60s;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'Upgrade';
    }
}

server {
    listen 443 ssl;
    server_name minio.nebula.fee.red;
    ssl_certificate cert/fee.red/fullchain.cer;
    ssl_certificate_key cert/fee.red/fee.red.key;

    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
    ssl_prefer_server_ciphers on;

    proxy_set_header Host $host:$server_port;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header REMOTE-HOST $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    proxy_hide_header X-Frame-Options;
    proxy_hide_header Content-Security-Policy;

    location / {
        proxy_pass https://s3.fee.red:9091/;
    }
}

server {
    listen 443 ssl;
    server_name glances.nebula.fee.red;
    ssl_certificate cert/fee.red/fullchain.cer;
    ssl_certificate_key cert/fee.red/fee.red.key;

    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
    ssl_prefer_server_ciphers on;

    proxy_set_header Host $host:$server_port;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header REMOTE-HOST $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    proxy_hide_header X-Frame-Options;
    proxy_hide_header Content-Security-Policy;

    location / {
        proxy_pass http://glances.lan:61208/;
    }
}
