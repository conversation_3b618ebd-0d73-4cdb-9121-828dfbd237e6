server {
    # 给内网使用
    listen 443 ssl;
    server_name n.fee.red;
    ssl_certificate cert/fee.red/fullchain.cer;
    ssl_certificate_key cert/fee.red/fee.red.key;

    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
    ssl_prefer_server_ciphers on;

    proxy_set_header Host $host:$server_port;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header REMOTE-HOST $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    location / {
        proxy_pass http://**************/;

        # 添加 WebSocket 支持
        proxy_read_timeout 60s;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'Upgrade';
    }

    location /x {
      alias /usr/share/nginx/html/public/x;
      try_files $uri $uri/ =404;
    }
}

