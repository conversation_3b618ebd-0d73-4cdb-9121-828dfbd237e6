#user  nobody;
worker_processes 1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;

events {
  worker_connections 1024;
}

http {
  include mime.types;
  default_type text/html;

  sendfile on;
  # tcp_nopush     on;

  #keepalive_timeout  0;
  keepalive_timeout 65;

  # 启用gzip压缩
  gzip on;
  gzip_buffers 32 16k;
  gzip_comp_level 6;
  gzip_min_length 1k;
  gzip_types text/plain text/css application/javascript application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript;
  gzip_vary on;

  server {
    listen 8080;
    server_name _;

    # Cloud Run 健康检查
    location /healthz {
      add_header Content-Type text/plain;
      return 200 'OK';
    }

    # 所有请求转发到 http://sd.fee.red:2000/
    location / {
      proxy_pass http://sd.fee.red:2000/;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_connect_timeout 5s;
      proxy_read_timeout 60s;
      proxy_send_timeout 60s;

      # 修复内容类型问题
      proxy_hide_header Content-Type;
      add_header Content-Type text/html;
    }

    # 错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
      root /usr/share/nginx/html;
    }
  }
}
