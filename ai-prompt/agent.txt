


- 阅读本项目所有代码
- 深度理解系统架构,
- 后端核心独立库 @core
- 后端API @api
- 任务消费者 @consumer 
- 前端 @frontend-web
- 以 cloudflare功能模块为例贯穿在core,api,consumer,frondend的各个节点的编码流程,为扩展功能输出一套完整的开发流程的文档放在/documents下


@frontend-web 仔细阅读理解所有前端代码和结构,特别是 @app.js @CloudflareView.js @MemoView.js 演示了如何在基于 @app.js 核心前端框架下拓展业务的过程 ,请将这些前端的开发流程和细节结合项目规则更新到 @开发流程指南.md 的前端开发章节和 /frontend-web/README.md(已过时)


------------
- 阅读本项目所有代码
- 深度理解系统架构
- 核心独立库 @core   (pip install -e .)
- 后端API @api  
- 任务消费 @consumer  
- 前端@app.js @MemoView.js 
- 请遵 @特别约定.md
- 以 Memo功能模块为例贯穿在core,api,consumer,frondend的各个节点的编码流程

** 为扩展业务逻辑和功能生成一套完整的开发文档说明书,参考图片 **
- 特别是开发流程的文档要做到只需要根据开发流程说明书按照步骤即可扩展新业务模块
- 注意编写示例Module名字可以用Example
- 请按顺序输出一下文档
    > 01-architecture-overview.md
    > 02-core-development.md
    > 03-api-development.md
    > 04-consumer-development.md
    > 05-frontend-development.md
    > 06-memo-case-study.md
    > 07-best-practices.md
    > README.md
- 存放在/documents/dev-guide下
- 请勿输出测试章节  
- 说明书的内容必须紧跟实际的代码,拒绝凭空猜想与实际代码不符的内容
- 审查文档是否符合实际代码的逻辑
** 生成这些文档的最终目的是为AI能阅读这些文件后能以照文档内容按流程生成新业务模块 **
------------
审查 @03-api-development.md  是否符合实际代码逻辑 , 修正
------------





## 前端提示词

前端代码请遵循 @frontend.mdc  @## 3. 前端开发规范与流程  





## 优化打开的文件的提示词

请对当前文件进行代码重构，遵循以下严格要求
1.仅重构当前打开的文件，不要修改或引用任何其他文件
2.重构后的代码必须保持与原始代码完全相同的功能和行为
3.保持所有现有的公共API、函数签名和接口不变
不要更改文件的导入/导出结构4.
5.重构重点应放在:
提高代码可读性
优化性能(如果可能)
消除代码异味和重复
应用适当的设计模式
增强错误处理
-改进变量和函数命名
6.为关键部分添加或改进注释，但保持注释简洁明了
7.请保持与项目现有的代码风格一致
完成重构后，简要说明您所做的主要更改及其好处





