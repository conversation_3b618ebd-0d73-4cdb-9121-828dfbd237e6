# 项目任务系统迁移：从自定义消息队列到Taskiq

## 迁移目标
将项目中现有的自定义消息队列系统**完全替换**为Taskiq任务系统，不考虑向后兼容性,不考虑平滑迁移，请实现全面替代全面迁移。

## 核心需求
1. 设计一个抽象任务调度接口作为系统核心，确保API与实现解耦
2. 基于Taskiq框架实现新的任务调度器，完全替代现有系统
3. 利用Taskiq的信号机制实现任务状态通知功能（成功/失败事件处理）
4. 配置Taskiq的重试机制，确保与现有重试策略保持一致
5. 将所有现有任务（包括但不限于Cloudflare、Memo、Video、Telegram）重构为Taskiq任务
6. 确保迁移后所有业务功能保持不变，无功能损失
7. 迁移完成后彻底清理所有冗余代码并更新相关引用

## 架构约束
1. **严格分离关注点**：
   - API层：仅使用TaskiqClient发送任务，不初始化完整Broker
   - Worker层：运行完整TaskiqBroker并执行任务处理

2. **配置管理**：
   - 将Taskiq相关配置添加到现有配置类中，不创建新配置文件
   - 配置项应包括TASKIQ_BROKER_URL和TASKIQ_RESULT_BACKEND_URL等必要参数

3. ** broker与result_backend **
   - broker 使用rabbitmq
   - result_backend 使用redis
   - 

4. **代码组织**：
   - 将Taskiq相关代码从infrastructure目录移至更合适位置
   - 建立清晰的目录结构：nebula/core/tasks/和nebula/worker/

## 技术实现细节
1. **抽象任务调度**：
   - 在nebula.core.tasks.interface.py中定义AbstractTaskScheduler接口
   - 在nebula.core.tasks.scheduler.py中实现TaskiqScheduler，封装AsyncTaskiqClient

2. **任务定义与发现**：
   - 在nebula.core.tasks.definitions/目录下按模块组织任务定义
   - 每个任务使用@broker.task装饰器，从broker_config.py导入broker实例

3. **信号处理**：
   - 在nebula.core.tasks.signals.py中定义任务事件处理函数
   - 通过broker.add_event_handler()在Worker启动时注册事件处理器

4. **重试策略**：
   - 使用@broker.task装饰器的retry_on_exc、max_retries、retry_delay参数配置任务重试

5. **Worker入口**：
   - 在nebula/worker/main.py中配置Worker启动逻辑
   - 确保所有任务定义和信号处理器被正确加载

6. 模块尽量采用延迟加载

7. 当有多个可行方案供选择时,不考虑兼容性请选择最激进的方案.

8. 等待任务结果时禁止使用轮询方式

## 参考资源
- Taskiq官方文档：https://taskiq-python.github.io/guide/getting-started.html
- Taskiq核心组件：Broker、Task定义、Client、Signals、重试策略、Worker CLI