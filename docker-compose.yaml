version: '3'

services:
  api:
    build:
      context: ./nebula/api
      dockerfile: Dockerfile
    image: nebula-api:latest
    container_name: nebula-api
    restart: always
    expose:
      - 80
    volumes:
      - ./:/nebula
      - ./nebula/api:/app
      - ./nebula/api/supervisor.conf:/etc/supervisor/conf.d/app.conf
    env_file:
      - .env
    networks:
      xbridge:
        ipv4_address: **************
    command: ["supervisord","-n"]

  websocket:
    build:
      context: ./nebula/websocket
      dockerfile: Dockerfile
    image: nebula-websocket:latest
    container_name: nebula-websocket
    restart: always
    expose:
      - 8092
    volumes:
      - ./:/nebula
      - ./nebula/websocket:/app
      - ./nebula/websocket/supervisor.conf:/etc/supervisor/conf.d/app.conf
    environment:
      - NEBULA_DEV_MODE=true
      - PYTHONPATH=/nebula
    env_file:
      - ./.env
    networks:
      xbridge:
        ipv4_address: **************
    command: ["supervisord","-n"]


  # test:
  #   build:
  #     context: ./
  #     dockerfile: test.dockerfile
  #   image: nebula-test:latest
  #   container_name: nebula-test
  #   restart: always
  #   volumes:
  #     - /home/<USER>/vue-vben-admin:/app
  #   networks:
  #     - xbridge
  #   command: ["tail", "-f", "/dev/null"]

  worker:
    build:
      context: ./nebula/worker
      dockerfile: Dockerfile
    image: nebula-worker:latest
    container_name: nebula-worker
    restart: always
    volumes:
      - ./:/nebula
      - ./nebula/worker:/app
      - ./nebula/worker/supervisor.conf:/etc/supervisor/conf.d/app.conf
      - /tmp:/tmp
      - /var/run/docker.sock:/var/run/docker.sock
      - /bin/docker:/bin/docker
    env_file:
      - ./.env
    networks:
      - xbridge
    command: ["supervisord","-n"]

  nginx:
    image: nginx:stable
    container_name: nebula-nginx
    restart: always
    volumes:
      - ./frontend:/usr/share/nginx/html
      - ./nginx/conf:/etc/nginx/conf.d
      - ./nginx/cert:/etc/nginx/cert
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./frontend/apps/web-nebula/dist:/usr/share/nginx/html/nebula   #  编译后的文件目录
      - ./frontend/apps/web-nebula/public:/usr/share/nginx/html/public # 源代码的public文件目录
    networks:
      xbridge:
        ipv4_address: **************

  fluentd:
    build:
      context: ./fluentd
      dockerfile: Dockerfile
    container_name: nebula-fluentd
    restart: always
    volumes:
      - ./fluentd/fluent.conf:/fluentd/etc/fluent.conf
      - /tmp:/tmp
    environment:
      - FLUENTD_CONF=fluent.conf
      - FLUENT_LOG_LEVEL=info
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - xbridge

  fluent-bit:
    image: cr.fluentbit.io/fluent/fluent-bit:latest
    container_name: nebula-fluent-bit
    restart: always
    volumes:
      - ./fluent-bit/fluent-bit.conf:/fluent-bit/etc/fluent-bit.conf
      - ./fluent-bit/parsers.conf:/fluent-bit/etc/parsers.conf
      - /tmp:/tmp
    environment:
      - FLB_LOG_LEVEL=info
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - xbridge

  admin:
    image: node:20-slim
    container_name: nebula-admin
    restart: always
    ports:
      - "9022:80"
    volumes:
      - ./:/nebula
      - ./frontend:/app
    working_dir: /app
    environment:
      - NODE_ENV=development
      - PNPM_HOME=/root/.local/share/pnpm
    networks:
      xbridge:
        ipv4_address: **************
    command: ["tail", "-f", "/dev/null"]
    # command: "pnpm dev"

  playwright:
    image: mcr.microsoft.com/playwright:v1.51.1-jammy
    container_name: nebula-playwright
    restart: always
    ports:
      - "3333:3000"
    volumes:
      - ./:/nebula
    environment:
      - PLAYWRIGHT_SERVER_PORT=3333
      - PLAYWRIGHT_SERVER_HOST=0.0.0.0
    ipc: host
    networks:
      xbridge:
        ipv4_address: **************
    command: "npx -y playwright@1.51.1 run-server --port 3333 --host 0.0.0.0"
  # 保持容器一直运行

  # mcp:
  #   build:
  #     context: ./
  #     dockerfile: mcp.dockerfile
  #   image: nebula-mcp:latest
  #   container_name: nebula-mcp
  #   restart: always
  #   volumes:
  #     - ./vue-vben-admin:/workspaces/vue-vben-admin
  #   env_file:
  #     - ./.env
  #   networks:
  #     xbridge:
  #       ipv4_address: **************
  #   command: ["tail", "-f", "/dev/null"]

networks:
  xbridge:
    external: true