# Nebula 示例配置文件
# 重命名为 config.yaml 使用
# 或者创建 config.development.yaml, config.production.yaml 等环境特定配置

# 日志配置
logging:
  level: INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  file: logs/nebula.log
  max_size_mb: 10
  backup_count: 5
  format: text  # json 或 text
  text_format: "%(asctime)s [%(levelname)s] %(name)s - %(message)s"
  date_format: "%Y-%m-%d %H:%M:%S"
  stacktrace_file: logs/nebula_error.log
  console_show_exc: true

# 数据库配置
mongodb:
  host: mongo.fee.red
  port: 28000
  username: web
  password: 112233
  database: nebula
  min_pool_size: 1
  max_pool_size: 20

# Redis配置
redis:
  host: nas.fee.red
  port: 6379
  password: yi3K_6UyCZDh-2*Bu7.v
  db: 0
  pool_size: 10
  socket_timeout: 5
  socket_connect_timeout: 5

taskiq:
  # RabbitMQ连接URL，格式：amqp://[username]:[password]@[host]:[port]/[vhost]
  broker_url: "*************************************/"
  # Redis结果后端URL，格式：redis://[username]:[password]@[host]:[port]/[db]
  result_backend_url: "redis://:yi3K_6UyCZDh-2*<EMAIL>:6379/0"
  # 任务执行配置
  max_retry_count: 3
  retry_intervals: [60, 300, 1800]  # 重试间隔：1分钟、5分钟、30分钟
  worker_concurrency: 4
  task_timeout: 3600  # 任务超时时间（秒）
  result_ttl: 86400  # 结果保存时间（秒）


# RabbitMQ配置
rabbitmq:
  host: *************
  port: 5672
  username: admin
  password: admin
  vhost: /
  connection_attempts: 3
  retry_delay: 5
  heartbeat: 60

  # 延迟消息配置 - 使用rabbitmq_delayed_message_exchange插件
  delayed_exchange: nebula.delayed  # 延迟交换机名称
  delayed_exchange_type: x-delayed-message  # 延迟交换机类型
  max_retry_count: 5  # 最大重试次数
  retry_intervals: [60, 300, 1800, 3600, 14400]  # 重试间隔：1分钟、5分钟、30分钟、1小时、4小时

# 存储配置
storage:
  type: s3
  s3:
    endpoint: https://s3.fee.red:9090
    region: cn-shenzhen
    access_key: null  # 通过环境变量设置
    secret_key: null  # 通过环境变量设置
    bucket: nebula
    enabled_sub_domain: false
    frontend: z.fee.red


# 特性开关
features:
  enable_websocket: true
  enable_message_queue: true
  enable_storage: true
  enable_cache: true


genai:
  bucket: x-nebula-storage
  credentials: "./google.apikey.json"
  project: "southern-camera-453003-b4"
  location: "us-central1"
  model: "gemini-2.0-flash-exp"
  temperature: 1
  top_p: 0.95
  max_output_tokens: 8192
  response_modalities:
    - "TEXT"
  safety_settings:
    - category: "HARM_CATEGORY_HATE_SPEECH"
      threshold: "OFF"
    - category: "HARM_CATEGORY_DANGEROUS_CONTENT"
      threshold: "OFF"
    - category: "HARM_CATEGORY_SEXUALLY_EXPLICIT"
      threshold: "OFF"
    - category: "HARM_CATEGORY_HARASSMENT"
      threshold: "OFF"

browser:
  playwright:
    browser_type: chromium
    endpoint: ws://192.168.16.162:3333

# Telegram配置
telegram:
  token: "**********************************************"  # Telegram机器人令牌
  webhook_url: "https://bot.fee.red:8443"  # Webhook基础URL
  webhook_path: "/api/telegram/webhook"  # Webhook路径
  allowed_user_ids: [5058924821]  # 允许使用机器人的用户ID列表


video:
  bucket: video
  cover_path: "images/{unique_id}{ext}"
  video_path: "{unique_id}{ext}"
  youtube_proxy: http://x.fee.red:8809/youtube?id={id}
  twitter_proxy: http://x.fee.red:8809/twitter?url={url}
  download_timeout: 1800


memo:
  bucket: memo
  file_path: "{unique_id}{ext}"
