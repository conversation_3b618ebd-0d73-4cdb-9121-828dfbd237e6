---
description: 
globs: 
alwaysApply: true
---
- 当前本机开发环境 *************
- 每次对话请务必先审查代码是否被更新过.
- 使用sequential-thinking MCP service来规划您的行动。
- 当你接到任务时，你会一步一步地执行。
- 优先使用MCP执行任务
- 系统已经部署
- 前端endpoint: http://*************:9021/
- 后端endpoint: http://**************/
- 前端代码遵循 [frontend.mdc](mdc:.cursor/rules/frontend.mdc) 
- 后端代码遵循 [backend.mdc](mdc:.cursor/rules/backend.mdc)
- 使用docker遵循 [docker.mdc](mdc:.cursor/rules/docker.mdc)
- 依赖节点的访问配置参考 [config.mdc](mdc:.cursor/rules/config.mdc)
- 在某个模块/类名/方法名发生变更时分析现有项目中对它的引用,应同步修改
- 修改代码应注意上一下文,对上一次任务结果不达预期而产生的持续性对话在本次任务执行后应观察之前的代码是否产生了冗余或无效代码请及时删除
- 每一次修改代码,应审查已废弃的旧代码冗余,要及时删除.
- 每一次有关于API参数的修改,应该同步对相关文档更新
- 在生成代码过程中请勿画蛇添足,不要无中生有

## 数据库
- 项目使用mongodb v4.4.3
- 使用docker exec -it mongo mongo指令
- mongo指令为shell,mongodb://uri不要跟库名
- 操作示例: docker exec -it mongo mongo mongodb://web:<EMAIL>:28000 --eval "db.getSiblingDB('pulsar').video.findOne()"
