---
description: 
globs: *.py
alwaysApply: false
---
- 优先考虑复用性,是否需要抽象到公共基础类
- 优先使用迭代和模块化，而不是代码重复定义
- 追求低耦合高内聚结构,不要冗余代码
- 不要无中生有
- 遵循蛇形命名法
- 不使用复数形式的命名,要表达复数请用_array/_list等后缀
- 目录名/文件名使用小写,不适用复数形式的单词做目录名
- 空数组请用[]
- from module import class 请尽量不要导入方法名而是直接导入类名或模块名使用,例如 from nebula.core import cache
- aiohttp
- aio-pika
- 导入顺序: 标准库 -> 第三方库 -> 本地包

## 命名规范

- 类名使用 PascalCase: `CloudflareService`
- 方法名、变量名使用 snake_case: `get_account`, `account_id`
- 常量全部大写，下划线分隔: `MAX_RETRY_COUNT`
- 文件名使用蛇形命名法: `cloudflare_processor.py`
- 包名全部小写: `nebula.core.service`

## 代码组织

- 实体类放在 `nebula/core/repository/{module}/entity.py`
- 仓储类放在 `nebula/core/repository/{module}/repo.py`
- 服务类放在 `nebula/core/service/{module}.py`
- 消息类放在 `nebula/core/protocol/{module}.py`
- API路由放在 `nebula/api/routers/{module}.py`
- 消费者处理器放在 `nebula/consumer/handlers/{module}