---
description: 
globs: *.conf,*.yml
alwaysApply: false
---
# 本项目的相关配置

- api
    - endpoint: http://**************/
- MongoDB:
    - host: mongo.fee.red
    - port: 28000
    - user: web
    - password: 112233
    - db: pulsar

- rabbitmq:
    - host: *************
    - port: 5672
    - user: admin
    - password: admin

- redis:
    - host: nas.fee.red
    - port: 6379,
    - pwd: yi3K#6UyCZDh@#2Bu7#v
    - db: 4

- minio:
    - access: deploy
    - secret: X3QFDVmGA9
    - egion: cn-shenzhen
    - endpoint: https://s3.fee.red:9090
    - frontend: z.fee.red