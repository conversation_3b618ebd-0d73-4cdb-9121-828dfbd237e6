---
description: 
globs: *.js,*.html
alwaysApply: false
---
- 理解 @Vue Next 
- 本项目前端基于tdesign+vue-next(vue3.0), 仅限使用版本v1.11.5
- 本项目前端浏览地址 http://*************:9021/
- 测试浏览页面请使用 playwright MCP工具
- 布局请遵循TDesign设计,正确的Grid系统和组件
- 优先使用迭代和模块化，而不是代码重复定义
- http异步请求时请勿使用回调结构,统一使用await方式
- 尽可能使用tdesign组件,请勿使用内联样式(如有必要可以使用css)
- 保持交互风格的一致性,通用组件的调用应该打包成公共库 @ui-utils.js 来使用.例如表单验证风格,通知等需要统一风格
- 表单验证请用:rules属性,提交使用submit
- 对于业务后端提交,所有response均为json对象,如果存在_task_id则表示该业务时一个异步业务,websocket收到推送事件才表示业务处理有结果了.注意业务后端提交是否属于队列任务需要从后端api里查看具体的routers模块返回的reponse对象来确定,如果非队列业务无需注册推送事件
- 简单条件语句使用简洁的单行语法（如 if (condition) doSomething()）
- 禁止三元表达式嵌套，使用阅读性更好的条件语句
- if-else 过多时优化为 map 设计      
- 遵循蛇形命名法
- 不适用复数形式的命名,要表达复数请用_array/_list等后缀
- 请求统一使用axios库


## 文件组织
- 目录名/文件名使用小写
- 视图组件放在 `frontend-web/views/` 目录
- 可重用组件放在 `frontend-web/components/` 目录
- 样式文件放在 `frontend-web/css/` 目录
- JavaScript工具放在 `frontend-web/js/` 目录
- 静态资源放在 `frontend-web/assets/` 目录

## 界面设计
- 使用TDesign组件库
- 表单必须有验证规则
- 异步操作显示加载状态
- 操作结果显示成功/失败通知
- 保持界面风格一致性

