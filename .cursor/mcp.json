{"mcpServers": {"cloudflare": {"command": "npx", "args": ["-y", "@cloudflare/mcp-server-cloudflare", "run", "<EMAIL>"]}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}}, "desktop-commander": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@wonderwhy-er/desktop-commander", "--config", "{}"]}, "mongo-mcp": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "mongo-mcp", "--config", "{\"mongoUri\":\"****************************************\"}"]}, "server-sequential-thinking": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@smithery-ai/server-sequential-thinking", "--config", "{}"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAE1OT2_dbXzxDbBut4HGlaXNeffPS"}}, "playwright-mcp-server": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"]}}}