FROM node:20-slim

# 设置工作目录
WORKDIR /app

# 设置时区
ENV TZ=Asia/Shanghai

# 使用国内镜像源加速
RUN npm config set registry https://registry.npmmirror.com && npm install -g pnpm

# 预安装常用的MCP工具包
# RUN npm install -g \
#     @smithery/cli@latest \
#     @modelcontextprotocol/server-brave-search \
#     @executeautomation/playwright-mcp-server \
#     @cloudflare/mcp-server-cloudflare
# RUN npm install -g mcp-mongo-server

RUN mkdir -p /root/.pip && \
    echo "[global]" > /root/.pip/pip.conf && \
    echo "index-url = https://mirrors.aliyun.com/pypi/simple/" >> /root/.pip/pip.conf && \
    sed -i "<EMAIL>@mirrors.aliyun.com@g" /etc/apt/sources.list.d/debian.sources && apt update && \
    apt install -y supervisor

# 创建存储配置和数据的目录
RUN mkdir -p /app/config /app/data

# 启动命令（可以在docker-compose中覆盖）
CMD ["tail", "-f", "/dev/null"]