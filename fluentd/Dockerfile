FROM fluent/fluentd:v1.16-debian

USER root

# 设置 RubyGems 镜像源为清华镜像，加速
RUN gem sources --clear-all && \
    gem sources --add https://mirrors.tuna.tsinghua.edu.cn/rubygems/ && \
    gem sources -l

# 安装构建插件所需依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    make gcc g++ libc-dev && \
    gem install fluent-plugin-mongo && \
    apt-get remove -y make gcc g++ libc-dev && \
    apt-get autoremove -y && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /usr/local/bundle/cache/*.gem

USER fluent