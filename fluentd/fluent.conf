# 接收来自 Fluent Bit 的日志
<source>
  @type forward
  port 24224
  bind 0.0.0.0
</source>

# 直接从文件收集日志（已禁用，由 Fluent Bit 负责收集）
# <source>
#   @type tail
#   path /tmp/worker.log
#   pos_file /tmp/worker.log.pos
#   tag nebula.worker
#   read_from_head true
#   refresh_interval 5
#   <parse>
#     @type regexp
#     expression /\[(?<time>[^\]]*)\] \(task_id=(?<task_id>[^,]*), retry_index=(?<retry_index>[^,]*), step=(?<step>[^\)]*)\) (?<level>[^ ]*) - (?<message>.*)/
#     time_format %Y-%m-%d %H:%M:%S
#     types retry_index:integer
#   </parse>
# </source>

# 过滤出任务日志
<filter nebula.worker>
  @type grep
  <regexp>
    key task_id
    pattern .+
  </regexp>
</filter>

<match nebula.worker>
  @type mongo
  host mongo.fee.red
  port 28000
  database nebula
  collection task_log
  user web
  password 112233
  auth_source admin

  # 添加时间戳字段
  time_key timestamp
  time_key_format %Y-%m-%dT%H:%M:%S%z

  # 设置重试机制
  <secondary>
    @type file
    path /tmp/fluentd_mongo_error
    compress gzip
  </secondary>

  # 优化缓冲区配置
  <buffer>
    @type memory
    flush_interval 2s
    flush_thread_count 4
    retry_max_interval 30
    retry_forever true
    overflow_action block
  </buffer>
</match>

# 添加监控日志
<label @FLUENT_LOG>
  <match fluent.*>
    @type stdout
  </match>
</label>
