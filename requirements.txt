# API框架
fastapi>=0.103.1
uvicorn>=0.23.2
starlette==0.27.0
python-multipart>=0.0.6

# 数据库
pymongo>=4.5.0
motor>=3.3.1

# 存储
minio==7.1.17

# 工具库
python-dotenv==1.0.0
pydantic>=2.3.0
pydantic-settings>=2.0.0
requests==2.31.0
python-jose>=3.3.0
passlib[bcrypt]==1.7.4
qrcode==7.4.2
Pillow==10.1.0
pyyaml>=6.0.1

# 开发工具
black==23.10.1
flake8==6.1.0
pytest==7.4.3
pytest-cov==4.1.0

# 新增依赖
aio-pika>=9.3.0
redis>=5.3.0
aioredis>=2.0.1
websockets>=11.0.3

# Taskiq依赖
taskiq>=0.10.0
taskiq-aio-pika>=0.4.0
taskiq-redis>=0.4.0

-e /nebula