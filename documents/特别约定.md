# 特别约定

### 部署环境
- api
    - docker exec -it nebula-nginx
    - endpoint: http://192.168.16.160/api
- frontend
    - endpoint: http://192.168.32.15:9021/
### Repository
- 实体需继承BaseEntity
- 实体类名直接使用{名词}不要加后缀Entity

### API router
- 业务API请求与数据仓库实体是通用的pydantic.BaseModel, 不要再单独定义BaseModel的请求体(数据仓库如果没有实体,可以直接使用参数)
- 对请求参数来源强约束并附上缺省值
- 对API功能添加描述


### frontend
- Vue 3.0 (CDN)
- TDsign Vue Next v1.11.5
- 尽可能使用tdesign组件,请勿使用内联样式(如有必要可以使用css)
- websocket已封装共app.js内调用,view请勿直接注册ws事件
- 在前端开发中，应该使用App.action.submit方法来统一处理API调用，而不是直接调用API方法
- 对于长时间运行的异步任务（如AI分析），应在任务提交时就显示通知，而不是等待任务完成后才通知用户
- 异步任务通知应使用App.action.submit方法的showSubmitNotify参数，而不是手动调用App.ui.notify方法
