import type { UserConfig } from 'vite';

async function getCommonConfig(): Promise<UserConfig> {
  const isDev = process.env.NODE_ENV === 'development';

  return {
    build: {
      chunkSizeWarningLimit: 2000,
      reportCompressedSize: false,
      // 开发环境启用 sourcemap，生产环境禁用
      sourcemap: isDev,
      // 开发环境不压缩，生产环境使用默认压缩
      minify: isDev ? false : 'esbuild',
      // 只在开发环境下禁用压缩和混淆
      ...(isDev && {
        terserOptions: {
          compress: false,
          mangle: false,
          format: {
            beautify: true,
            comments: 'all'
          }
        }
      }),
    },
  };
}

export { getCommonConfig };
