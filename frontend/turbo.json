{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["pnpm-lock.yaml", "**/.env.*local", "**/tsconfig*.json", "internal/node-utils/*.json", "internal/node-utils/src/**/*.ts", "internal/tailwind-config/src/**/*.ts", "internal/vite-config/*.json", "internal/vite-config/src/**/*.ts", "scripts/*/src/**/*.ts", "scripts/*/src/**/*.json"], "globalEnv": ["NODE_ENV"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "dist.zip", ".vitepress/dist.zip", ".vitepress/dist/**"]}, "preview": {"dependsOn": ["^build"], "outputs": ["dist/**"]}, "build:analyze": {"dependsOn": ["^build"], "outputs": ["dist/**"]}, "@vben/backend-mock#build": {"dependsOn": ["^build"], "outputs": [".nitro/**", ".output/**"]}, "test:e2e": {}, "dev": {"dependsOn": [], "outputs": [], "cache": false, "persistent": true}, "typecheck": {"outputs": []}}}