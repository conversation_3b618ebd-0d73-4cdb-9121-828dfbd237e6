import { defineConfig } from '@vben/vite-config';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      server: {
        allowedHosts: true,
        proxy: {
          '/api': {
            changeOrigin: true,
            // 代理到实际API服务
            target: 'http://**************'
          },
          // 禁用 WebSocket 代理
          '/socket.io': {
            changeOrigin: true,
            target: 'ws://**************:8092',
            ws: true,
          },
        },
      },
      // 开发环境配置
      build: {
        // 开发环境不压缩，生产环境使用默认压缩
        minify: process.env.NODE_ENV === 'development' ? false : 'esbuild',
      },
    },
  };
});
