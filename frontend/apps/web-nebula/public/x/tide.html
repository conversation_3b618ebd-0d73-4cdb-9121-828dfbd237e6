<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>东江博罗段潮汐表</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background-color: #f8f9fa;
            background-image: linear-gradient(to bottom, #e3f2fd, #f8f9fa);
            background-attachment: fixed;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 10px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
            padding: 12px;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .card:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
        }

        .header-card {
            padding: 10px 12px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        header {
            text-align: left;
            margin-bottom: 0;
            flex: 1;
            min-width: 250px;
        }

        header h1 {
            font-size: 1.8rem;
            margin-bottom: 2px;
            color: #1565c0;
            letter-spacing: 0.5px;
            font-weight: 600;
            line-height: 1.2;
        }

        header p {
            font-size: 0.9rem;
            color: #546e7a;
            margin: 0;
            line-height: 1.3;
        }

        .date-selector {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            flex-wrap: wrap;
            gap: 8px;
            flex: 1;
            min-width: 280px;
        }

        .date-selector label {
            font-size: 0.85rem;
            font-weight: 500;
            color: #455a64;
        }

        .date-selector input {
            padding: 6px 10px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 0.9rem;
            color: #37474f;
            background-color: #fff;
            transition: all 0.2s;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .date-selector input:focus {
            border-color: #1976d2;
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
            outline: none;
        }

        .date-selector button {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .date-selector button:active {
            transform: translateY(1px);
        }

        #search-btn {
            background-color: #1976d2;
            color: white;
        }

        #search-btn:hover {
            background-color: #1565c0;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        #reset-btn {
            background-color: #f5f5f5;
            color: #455a64;
            border: 1px solid #e0e0e0;
        }

        #reset-btn:hover {
            background-color: #eeeeee;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 30px;
            position: relative;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            margin: 0 auto 15px;
            border: 4px solid rgba(25, 118, 210, 0.1);
            border-radius: 50%;
            border-top-color: #1976d2;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 1rem;
            color: #546e7a;
            font-weight: 500;
        }

        .error {
            display: none;
            text-align: center;
            padding: 20px;
            background-color: #ffebee;
            border-radius: 8px;
            border-left: 4px solid #f44336;
            color: #d32f2f;
            font-size: 1rem;
            margin: 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        /* 表格样式 */
        .tide-table-container {
            overflow-x: auto;
            border-radius: 8px;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            margin: 0;
            scrollbar-width: thin;
            scrollbar-color: #bbdefb #e3f2fd;
        }

        .tide-table-container::-webkit-scrollbar {
            height: 6px;
        }

        .tide-table-container::-webkit-scrollbar-track {
            background: #e3f2fd;
            border-radius: 3px;
        }

        .tide-table-container::-webkit-scrollbar-thumb {
            background-color: #bbdefb;
            border-radius: 3px;
            border: 1px solid #e3f2fd;
        }

        .tide-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 0.85rem;
        }

        .tide-table th {
            background-color: #1976d2;
            color: white;
            padding: 8px 10px;
            text-align: center;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
            border-bottom: 1px solid #1565c0;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .tide-table th:first-child {
            border-top-left-radius: 8px;
        }

        .tide-table th:last-child {
            border-top-right-radius: 8px;
        }

        .tide-table td {
            padding: 6px 8px;
            border-bottom: 1px solid #e0e0e0;
            vertical-align: middle;
            transition: background-color 0.15s;
            text-align: center;
        }

        .tide-table tr:last-child td {
            border-bottom: none;
        }

        .tide-table tr:last-child td:first-child {
            border-bottom-left-radius: 12px;
        }

        .tide-table tr:last-child td:last-child {
            border-bottom-right-radius: 12px;
        }

        .tide-table tr:hover td {
            background-color: #f5f9ff;
        }

        .tide-table .date-cell {
            font-weight: 600;
            color: #1976d2;
            border-right: 1px solid #e0e0e0;
            padding: 6px 8px;
            width: auto;
            text-align: center;
        }

        .tide-table .date-primary {
            font-size: 1rem;
            font-weight: 700;
            color: #1565c0;
            margin-bottom: 2px;
            padding-bottom: 2px;
        }

        .tide-table .today-mark {
            color: #1976d2;
            font-size: 0.75rem;
            margin-left: 3px;
            font-weight: 500;
        }

        .tide-table .date-weekday {
            color: #546e7a;
            font-size: 0.8rem;
            font-weight: 500;
            margin-bottom: 3px;
            font-style: italic;
        }

        .tide-table .type-cell {
            border-radius: 4px;
            padding: 2px 6px;
            display: inline-block;
            font-size: 0.85rem;
            font-weight: 600;
            letter-spacing: 0.5px;
            margin-top: 2px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        /* 不同潮汐类型的颜色 - 应用到日期单元格 */
        .tide-table .date-cell.大潮 {
            background-color: rgba(227, 242, 253, 0.3);
            border-left: 3px solid #1565c0;
        }

        .tide-table .date-cell.小潮 {
            background-color: rgba(232, 245, 233, 0.3);
            border-left: 3px solid #4caf50;
        }

        .tide-table .date-cell.中潮 {
            background-color: rgba(255, 248, 225, 0.3);
            border-left: 3px solid #ffc107;
        }

        /* 潮汐类型标签样式简化 */
        .tide-table .type-cell {
            font-weight: 700;
            font-size: 0.85rem;
            background: none;
            border: none;
            box-shadow: none;
            padding: 0;
            margin-top: 2px;
        }

        /* 为潮汐类型文本设置与border-left相同的颜色 */
        .tide-table .date-cell.大潮 .type-cell {
            color: #1565c0;
            text-shadow: 0 0 1px rgba(21, 101, 192, 0.2);
            letter-spacing: 0.5px;
        }

        .tide-table .date-cell.小潮 .type-cell {
            color: #4caf50;
            text-shadow: 0 0 1px rgba(76, 175, 80, 0.2);
            letter-spacing: 0.5px;
        }

        .tide-table .date-cell.中潮 .type-cell {
            color: #ffc107;
            text-shadow: 0 0 1px rgba(255, 193, 7, 0.3);
            letter-spacing: 0.5px;
        }

        .tide-table .rising {
            border-left: 4px solid #4CAF50;
        }

        .tide-table .falling {
            border-left: 4px solid #f44336;
        }

        .tide-table .direction-cell {
            font-weight: 500;
            white-space: nowrap;
        }

        .tide-table .rising .direction-cell {
            color: #2e7d32;
        }

        .tide-table .falling .direction-cell {
            color: #c62828;
        }

        .tide-table .time-cell {
            white-space: nowrap;
            font-family: monospace;
            font-size: 0.85rem;
            letter-spacing: 0.3px;
            color: #37474f;
        }

        .tide-table .level-cell {
            color: #455a64;
            white-space: nowrap;
            font-weight: 500;
            font-size: 0.85rem;
        }

        /* 数据标记 */
        .tide-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .tide-badge.rising {
            background-color: #e8f5e9;
            color: #2e7d32;
            border-left: 3px solid #4CAF50;
        }

        .tide-badge.falling {
            background-color: #ffebee;
            color: #c62828;
            border-left: 3px solid #f44336;
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
                gap: 16px;
            }

            .header-content {
                flex-direction: column;
                align-items: stretch;
            }

            header {
                text-align: center;
                margin-bottom: 10px;
            }

            header h1 {
                font-size: 1.8rem;
            }

            header p {
                font-size: 1rem;
            }

            .card {
                padding: 16px;
                border-radius: 10px;
            }

            .date-selector {
                justify-content: center;
                gap: 10px;
            }

            .date-selector label {
                margin-right: 0;
            }

            .date-selector input {
                flex: 1;
                min-width: 0;
            }

            .date-selector button {
                padding: 8px 12px;
                font-size: 0.9rem;
            }

            .tide-table th {
                padding: 10px 12px;
                font-size: 0.85rem;
                background-color: #1976d2;
                text-align: center;
            }

            .tide-table td {
                padding: 10px 12px;
                font-size: 0.85rem;
                text-align: center;
            }

            .tide-table .type-cell {
                font-size: 0.8rem;
                padding: 4px 8px;
            }

            /* 在移动端优化表格显示 */
            .tide-table .date-cell {
                position: sticky;
                left: 0;
                background-color: white;
                z-index: 5;
                width: auto;
                padding: 12px 8px;
                text-align: center;
            }

            .tide-table .date-primary {
                font-size: 0.95rem;
            }

            .tide-table .date-weekday {
                font-size: 0.8rem;
                margin-bottom: 4px;
            }

            .tide-table .type-cell {
                font-size: 0.9rem;
                margin-top: 5px;
                letter-spacing: 0.5px;
            }

            /* 移动端不需要特殊背景色 */

            .tide-table tr:hover .date-cell {
                background-color: #f5f9ff;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 12px;
                gap: 12px;
            }

            header h1 {
                font-size: 1.6rem;
                margin-bottom: 5px;
            }

            header p {
                font-size: 0.9rem;
                margin-bottom: 0;
            }

            .card {
                padding: 12px;
            }

            /* 在手机端隐藏查询工具栏 */
            .date-selector {
                display: none;
            }

            /* 调整标题样式，使其在查询工具栏隐藏后仍然美观 */
            .header-content {
                justify-content: center;
            }

            header {
                margin-bottom: 0;
                text-align: center;
            }

            .tide-table th {
                padding: 8px 10px;
                font-size: 0.8rem;
                background-color: #1976d2;
                text-align: center;
            }

            .tide-table td {
                padding: 8px 10px;
                font-size: 0.8rem;
                text-align: center;
            }

            .tide-table .date-cell {
                width: auto;
                padding: 10px 6px;
                text-align: center;
            }

            .tide-table .date-primary {
                font-size: 0.9rem;
                margin-bottom: 2px;
            }

            .tide-table .date-weekday {
                margin-bottom: 3px;
            }

            .tide-table .type-cell {
                font-size: 0.85rem !important;
                margin-top: 4px;
                letter-spacing: 0.5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card header-card">
            <div class="header-content">
                <header>
                    <h1>东江博罗段潮汐表-钓鱼佬专用</h1>
                    <p style="color: red;">注意:博罗段全长100多公里,潮水流经沿途的钓点时间有先后顺序,误差大概范围30-60分钟</p>
                </header>

                <div class="date-selector">
                    <input type="date" id="date-input">
                    <button id="search-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="11" cy="11" r="8"></circle>
                            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                        </svg>
                        查询
                    </button>
                    <button id="reset-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
                            <path d="M3 3v5h5"></path>
                        </svg>
                        重置
                    </button>
                </div>
            </div>
        </div>

        <div class="loading card" id="loading">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在获取潮汐数据...</div>
        </div>

        <div class="error card" id="error"></div>

        <div class="tide-table-container" id="tide-container"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.getElementById('date-input');
            const searchBtn = document.getElementById('search-btn');
            const resetBtn = document.getElementById('reset-btn');
            const loadingEl = document.getElementById('loading');
            const errorEl = document.getElementById('error');
            const tideContainerEl = document.getElementById('tide-container');

            // 设置日期输入框的默认值为今天
            const today = new Date();
            const todayStr = today.toISOString().split('T')[0];
            dateInput.value = todayStr;

            // 页面加载时获取潮汐数据
            fetchTideData();

            // 监听窗口大小变化，以便在调整窗口大小时更新日期格式（添加防抖功能）
            let resizeTimer;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(function() {
                    // 如果表格已经渲染，则重新获取数据以更新日期格式
                    if (tideContainerEl.innerHTML !== '') {
                        fetchTideData(dateInput.value);
                    }
                }, 300); // 300毫秒的防抖延迟
            });

            // 绑定按钮点击事件
            searchBtn.addEventListener('click', function() {
                fetchTideData(dateInput.value);
            });

            // 回车键触发查询
            dateInput.addEventListener('keyup', function(event) {
                if (event.key === 'Enter') {
                    fetchTideData(dateInput.value);
                }
            });

            resetBtn.addEventListener('click', function() {
                dateInput.value = todayStr;
                fetchTideData();
            });



            // 获取潮汐数据
            function fetchTideData(date = null) {
                // 显示加载中
                loadingEl.style.display = 'block';
                errorEl.style.display = 'none';
                tideContainerEl.innerHTML = '';

                // 构建API URL
                let url = '/api/tide';
                if (date) {
                    url += `?date=${date}`;
                }

                // 添加加载动画
                const startTime = Date.now();
                const minLoadingTime = 600; // 最小显示加载动画的时间（毫秒）

                // 发送请求
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            const errorMessages = {
                                400: '请求参数错误，请检查日期格式',
                                404: '没有找到潮汐数据',
                                500: '服务器内部错误，请稍后再试',
                                503: '服务暂时不可用，请稍后再试'
                            };

                            const message = errorMessages[response.status] || `请求失败 (${response.status})`;
                            throw new Error(message);
                        }
                        return response.json();
                    })
                    .then(data => {
                        const loadingTime = Date.now() - startTime;

                        // 确保加载动画至少显示最小时间
                        if (loadingTime < minLoadingTime) {
                            setTimeout(() => {
                                processData(data);
                            }, minLoadingTime - loadingTime);
                        } else {
                            processData(data);
                        }
                    })
                    .catch(error => {
                        const loadingTime = Date.now() - startTime;

                        // 确保加载动画至少显示最小时间
                        if (loadingTime < minLoadingTime) {
                            setTimeout(() => {
                                handleError(error);
                            }, minLoadingTime - loadingTime);
                        } else {
                            handleError(error);
                        }
                    });
            }

            // 处理获取到的数据
            function processData(data) {
                // 隐藏加载中
                loadingEl.style.display = 'none';

                // 检查数据
                if (!data || !data.data || data.data.length === 0) {
                    showError('没有找到潮汐数据');
                    return;
                }

                // 渲染潮汐数据
                renderTideTable(data.data);


            }

            // 处理错误
            function handleError(error) {
                // 隐藏加载中，显示错误
                loadingEl.style.display = 'none';
                showError(`获取潮汐数据失败: ${error.message}`);
            }

            // 显示错误信息
            function showError(message) {
                errorEl.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 8px;">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="12"></line>
                        <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                    ${message}
                `;
                errorEl.style.display = 'flex';
                errorEl.style.alignItems = 'center';
                errorEl.style.justifyContent = 'center';
            }

            // 渲染潮汐数据表格
            function renderTideTable(tideDataList) {
                // 创建表格
                const table = document.createElement('table');
                table.className = 'tide-table';

                // 创建表头
                const thead = document.createElement('thead');
                const headerRow = document.createElement('tr');

                const headers = ['日期', '方向', '时间段', '水位'];
                headers.forEach(headerText => {
                    const th = document.createElement('th');
                    th.textContent = headerText;
                    headerRow.appendChild(th);
                });

                thead.appendChild(headerRow);
                table.appendChild(thead);

                // 创建表体
                const tbody = document.createElement('tbody');

                // 处理每天的潮汐数据
                tideDataList.forEach(tideData => {
                    // 格式化日期
                    const date = new Date(tideData.date);
                    const dateStr = formatDateForDisplay(date);

                    // 检查是否为今天
                    const isToday = isDateToday(date);

                    // 检查是否有潮汐时间数据
                    if (!tideData.tf || tideData.tf.length === 0) {
                        // 如果没有时间数据，创建一行显示无数据
                        const row = document.createElement('tr');

                        const dateCell = document.createElement('td');
                        dateCell.className = 'date-cell';

                        // 根据潮汐类型添加类名到日期单元格
                        if (tideData.tide_type) {
                            dateCell.classList.add(tideData.tide_type);
                        }

                        // 合并日期、星期和潮汐类型
                        let dateContent = `<div class="date-primary">${dateStr}`;
                        if (isToday) {
                            dateContent += ' <span class="today-mark">(今天)</span>';
                        }
                        dateContent += '</div>';

                        // 添加星期信息
                        const weekday = parseWeekday(tideData.dayofweek);
                        dateContent += `<div class="date-weekday">${weekday}</div>`;

                        // 添加潮汐类型标签
                        if (tideData.tide_type) {
                            dateContent += `<div class="type-cell">${tideData.tide_type}</div>`;
                        }

                        dateCell.innerHTML = dateContent;

                        const noDataCell = document.createElement('td');
                        noDataCell.colSpan = 3;
                        noDataCell.textContent = '没有潮汐时间数据';
                        noDataCell.style.textAlign = 'center';
                        noDataCell.style.color = '#9e9e9e';
                        noDataCell.style.fontStyle = 'italic';

                        row.appendChild(dateCell);
                        row.appendChild(noDataCell);

                        tbody.appendChild(row);
                    } else {
                        // 如果有时间数据，为每个时间段创建一行
                        const rowCount = tideData.tf.length;

                        tideData.tf.forEach((tf, index) => {
                            const row = document.createElement('tr');
                            row.className = tf.direction === '涨潮' ? 'rising' : 'falling';

                            // 检查当前时间是否在这个时间段内
                            const now = new Date();
                            const btTime = new Date(tf.bt);
                            const etTime = new Date(tf.et);
                            const isCurrentTimeframe = now >= btTime && now <= etTime && isToday;





                            // 只在第一行显示日期
                            if (index === 0) {
                                const dateCell = document.createElement('td');
                                dateCell.className = 'date-cell';

                                // 根据潮汐类型添加类名到日期单元格
                                if (tideData.tide_type) {
                                    dateCell.classList.add(tideData.tide_type);
                                }

                                // 合并日期、星期和潮汐类型
                                let dateContent = `<div class="date-primary">${dateStr}`;
                                if (isToday) {
                                    dateContent = '<div class="date-primary">今天';
                                }
                                dateContent += '</div>';

                                // 添加星期信息
                                const weekday = parseWeekday(tideData.dayofweek);
                                dateContent += `<div class="date-weekday">${weekday}</div>`;

                                // 添加潮汐类型标签
                                if (tideData.tide_type) {
                                    dateContent += `<div class="type-cell">${tideData.tide_type}</div>`;
                                }

                                dateCell.innerHTML = dateContent;
                                dateCell.rowSpan = rowCount;

                                row.appendChild(dateCell);
                            }

                            // 添加方向、时间和水位
                            const directionCell = document.createElement('td');
                            directionCell.className = 'direction-cell';

                            // 使用带样式的方向标记
                            const directionBadge = document.createElement('span');
                            directionBadge.className = `tide-badge ${tf.direction === '涨潮' ? 'rising' : 'falling'}`;
                            directionBadge.textContent = tf.direction;
                            directionCell.appendChild(directionBadge);

                            const timeCell = document.createElement('td');
                            timeCell.className = 'time-cell';

                            // 格式化时间
                            const btStr = `${padZero(btTime.getHours())}:${padZero(btTime.getMinutes())}`;
                            const etStr = `${padZero(etTime.getHours())}:${padZero(etTime.getMinutes())}`;

                            // 如果是当前时间段，添加标记
                            if (isCurrentTimeframe) {
                                timeCell.innerHTML = `<strong>${btStr} - ${etStr}</strong> <span style="color: #1976d2; font-size: 0.8rem; margin-left: 5px;">(当前)</span>`;
                            } else {
                                timeCell.textContent = `${btStr} - ${etStr}`;
                            }

                            const levelCell = document.createElement('td');
                            levelCell.className = 'level-cell';

                            // 格式化水位显示
                            let levelValue = tf.wlevel;

                            // 处理异常值
                            if (levelValue <= -99) {
                                levelCell.innerHTML = ``;  // 留空
                            } else {
                                // 保留一位小数
                                levelValue = levelValue.toFixed(1);
                                levelCell.innerHTML = `<strong>${levelValue}</strong> 米`;
                            }

                            row.appendChild(directionCell);
                            row.appendChild(timeCell);
                            row.appendChild(levelCell);

                            tbody.appendChild(row);
                        });
                    }
                });

                table.appendChild(tbody);
                tideContainerEl.appendChild(table);

            }

            // 检查日期是否为今天
            function isDateToday(date) {
                const today = new Date();
                return date.getDate() === today.getDate() &&
                       date.getMonth() === today.getMonth() &&
                       date.getFullYear() === today.getFullYear();
            }

            // 解析星期几字符串
            function parseWeekday(weekdayStr) {
                // 检查是否是对象字符串格式 {'1':'一','2':'二',...}[2]
                if (weekdayStr && typeof weekdayStr === 'string') {
                    // 尝试提取中括号中的数字
                    const match = weekdayStr.match(/\[(\d+)\]$/);
                    if (match && match[1]) {
                        const weekdayMap = {
                            '0': '日',
                            '1': '一',
                            '2': '二',
                            '3': '三',
                            '4': '四',
                            '5': '五',
                            '6': '六'
                        };
                        return `星期${weekdayMap[match[1]] || match[1]}`;
                    }
                }
                return weekdayStr; // 如果无法解析，返回原始字符串
            }

            // 补零函数
            function padZero(num) {
                return num.toString().padStart(2, '0');
            }

            // 根据屏幕宽度格式化日期显示
            function formatDateForDisplay(date) {
                // 检查是否为移动端（屏幕宽度 <= 480px）
                const isMobile = window.innerWidth <= 480;

                if (isMobile) {
                    // 移动端只显示月-日
                    return `${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`;
                } else {
                    // 非移动端显示完整年-月-日
                    return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`;
                }
            }
        });
    </script>
</body>
</html>