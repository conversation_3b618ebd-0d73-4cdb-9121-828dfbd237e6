<template>
  <div class="websocket-status" @click="handleClick">
    <a-tooltip :title="statusText">
      <div class="status-icon" :class="statusClass">
        <LinkIcon v-if="status === 'OPEN'" />
        <LinkOffIcon v-else-if="status === 'CLOSED'" />
        <Spin v-else class="text-[14px]" />
      </div>
    </a-tooltip>
  </div>
</template>

<script lang="ts" setup>
import { computed, onUnmounted, ref, watch } from 'vue';
import { message, Spin } from 'ant-design-vue';
import { useWebSocketService } from '../services/websocket';
import { LinkIcon, LinkOffIcon } from '@vben/icons';

// 获取 WebSocket 服务
const wsService = useWebSocketService();
const status = ref(wsService.status.value);

// 监听状态变化
const unwatch = watch(() => wsService.status.value, (newStatus, oldStatus) => {
  status.value = newStatus;

  // 当状态变化时显示通知
  if (oldStatus && newStatus !== oldStatus) {
    if (newStatus === 'OPEN' && oldStatus === 'CONNECTING') {
      message.success({
        content: 'WebSocket 连接成功',
        duration: 2
      });
    } else if (newStatus === 'CLOSED' && oldStatus === 'OPEN') {
      message.error({
        content: 'WebSocket 连接断开',
        duration: 3
      });
    }
  }
});

// 计算状态文本
const statusText = computed(() => {
  switch (status.value) {
    case 'OPEN':
      return '已连接';
    case 'CONNECTING':
      return '连接中...';
    case 'CLOSED':
      return '已断开';
    default:
      return '未知状态';
  }
});

// 计算状态类名
const statusClass = computed(() => {
  return {
    'status-connected': status.value === 'OPEN',
    'status-connecting': status.value === 'CONNECTING',
    'status-disconnected': status.value === 'CLOSED'
  };
});

// 点击处理
const handleClick = () => {
  if (status.value === 'CLOSED') {
    message.loading({
      content: '正在尝试重新连接...',
      duration: 2
    });
    wsService.open();

    // 设置超时检查
    setTimeout(() => {
      if (status.value !== 'OPEN') {
        message.error('连接失败，请检查网络或服务器状态');
      }
    }, 5000);
  } else if (status.value === 'OPEN') {
    message.success({
      content: 'WebSocket 连接正常',
      duration: 2
    });
  } else {
    message.info({
      content: 'WebSocket 正在连接中...',
      duration: 2
    });
  }
};

// 组件卸载时取消监听
onUnmounted(() => {
  unwatch();
});
</script>

<style scoped>
.websocket-status {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 8px;
  height: 100%;
}

.status-icon {
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.status-icon:hover {
  transform: scale(1.1);
}

.status-connected {
  color: #52c41a; /* 绿色 - 已连接 */
}

.status-connecting {
  color: #faad14; /* 黄色 - 连接中 */
}

.status-disconnected {
  color: #f5222d; /* 红色 - 已断开 */
}
</style>
