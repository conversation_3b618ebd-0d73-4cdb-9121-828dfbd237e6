<template>
  <div class="view-body">
    <slot></slot>
  </div>
</template>

<script lang="ts" setup>
// 保留 props 定义，以便外部组件可以传入最小高度等参数
const props = defineProps({
  extraPadding: {
    type: Number,
    default: 0
  },
  minHeight: {
    type: Number,
    default: 300
  }
});
</script>

<style lang="less" scoped>
.view-body {
  flex: 1;
  overflow-y: auto;
  position: relative;
  display: flex;
  padding: 16px;
  flex-direction: column;
  min-height: v-bind('props.minHeight + "px"');
  // height: calc(100vh - 180px); /* 设置一个基础高度，减去头部和底部的大致高度 */ // 180px = 16px(padding) + 16px(header) + 16px(footer) + 16px(padding)
  background: #fafafa; /* 浅色模式下使用略微不同的背景色 */
  box-shadow: inset 0 2px 3px rgba(0, 0, 0, 0.02); /* 添加微妙的内部阴影 */

  html.dark & {
    background: hsl(var(--background)); /* 使用比header更深的背景色 */
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15); /* 深色模式下增强阴影效果 */
  }

  /* 确保内容填充整个区域 */
  & > :deep(*) {
    flex: 1;
    min-height: 0; /* 重要：允许内容区域收缩 */
  }
}
</style>
