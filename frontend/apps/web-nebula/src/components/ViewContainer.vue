<template>
  <div class="view-container-wrapper" :class="`variant-${variant}`">
    <div class="view-container" ref="viewContainerRef">
      <slot name="header"></slot>
      <slot name="body"></slot>
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';

// 定义组件属性
const props = defineProps({
  variant: {
    type: String,
    default: 'page',
    validator: (value: string) => ['page', 'panel'].includes(value)
  }
});

const viewContainerRef = ref<HTMLElement | null>(null);

// 暴露给父组件的引用
defineExpose({
  viewContainerRef
});
</script>

<style lang="less" scoped>
// 切换风格可注释.view-container-wrapper
.view-container-wrapper {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 88px); /* 设置容器高度为视口高度减去上下内边距 */
  &.variant-panel {
    padding: 16px 16px 0px 16px;
    
    // height: calc(100vh - 32px); /* 设置容器高度为视口高度减去上下内边距 */
    html.dark & {
      padding:0px;
    }
  }
}
.view-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;
  background: white;
  color: var(--card-foreground);
  border-radius: v-bind('variant === "page" ? "0px" : "8px"');
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  /* 确保子元素正确布局 */
  & > :deep(*) {
    &:nth-child(1) {
      /* 头部 */
      flex-shrink: 0;
    }

    &:nth-child(2) {
      /* 内容区域 */
      flex: 1;
      overflow: auto;
      min-height: 200px;
    }

    &:nth-child(3) {
      /* 底部 */
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
      z-index: 10;
    }
  }
}
</style>
