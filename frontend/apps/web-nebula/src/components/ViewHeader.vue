<template>
    <div class="view-header">
      <div class="header-title" v-if="$slots.title">
        <slot name="title"></slot>
      </div>
      <div class="header-toolbar" v-if="$slots.toolbar">
        <slot name="toolbar"></slot>
      </div>
      <div class="header-actions" v-if="$slots.actions">
        <slot name="actions"></slot>
      </div>
    </div>
</template>

<script lang="ts" setup>
// 组件属性
defineProps({
  title: {
    type: String,
    default: ''
  }
});
</script>

<style lang="less" scoped>
.view-header {
  padding: 8px 24px 8px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.09); /* 增强边框可见度 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
  flex-shrink: 0;
  background: white; /* 确保背景色为白色 */
  position: relative; /* 为添加阴影做准备 */

  /* 添加微妙的底部阴影，增强分隔效果 */
  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 4px;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.02), transparent);
    pointer-events: none;
  }

  html.dark & {
    padding: 12px 24px 12px 24px;
    border-bottom: 1px solid hsl(240 5% 26%); /* 增强深色模式下的边框可见度 */
    background: hsl(var(--background-deep)); /* 确保深色模式下使用正确的背景色 */

    /* 深色模式下的阴影效果 */
    &::after {
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.03), transparent);
    }
  }

  .header-title {
    font-size: 16px;
    font-weight: 500;
    flex: 1;
    min-width: 120px;
  }

  .header-toolbar {
    flex: 2;
    display: flex;
    justify-content: center;
  }

  .header-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .view-header {
    flex-direction: column;
    align-items: stretch;

    .header-title, .header-toolbar, .header-actions {
      width: 100%;
      justify-content: flex-start;
    }

    .header-actions {
      justify-content: flex-end;
    }
  }
}
</style>
