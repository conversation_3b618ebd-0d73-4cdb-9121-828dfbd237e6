<template>
  <div class="view-footer">
    <div class="left-content">
      <slot name="left"></slot>
    </div>
    <div class="right-content">
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
// 不需要任何 JavaScript 逻辑
</script>

<style lang="less" scoped>
.view-footer {
  position: sticky;
  bottom: 0;
  z-index: 10;
  padding: 0px 24px 0px 24px;
  height: 50px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  background-color: var(--card);
}

.left-content {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
}

.right-content {
  display: flex;
  align-items: center;
}
</style>
