import type { RouteRecordRaw } from 'vue-router';

import { IFrameView } from '#/layouts';
import { $t } from '#/locales';


const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:more-horizontal',
      order: 70,
      title: '其它',
    },
    name: 'Others',
    path: '/others',
    children: [
      {
        name: 'TideTable',
        path: '/others/tide',
        component: IFrameView,
        meta: {
          keepAlive: true,
          icon: 'lucide:waves',
          iframeSrc: '/x/tide.html',
          title: '潮汐表',
        },
      },
    ],
  },
];

export default routes;
