import type { RouteRecordRaw } from 'vue-router';

import { IFrameView } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:server',
      order: 60,
      title: '系统管理',
    },
    name: 'SystemManagement',
    path: '/system',
    children: [
      {
        name: 'Glances',
        path: '/system/glances',
        component: IFrameView,
        meta: {
          keepAlive: false,
          icon: 'lucide:activity',
          iframeSrc: 'https://glances.nebula.fee.red/',
          title: 'Glances',
        },
      },
      {
        name: 'Router',
        path: '/system/router',
        meta: {
          icon: 'lucide:router',
          title: 'Router',
        },
        children: [
          {
            name: 'MainRouter',
            path: '/system/router/main',
            component: IFrameView,
            meta: {
              keepAlive: true,
              icon: 'lucide:router',
              iframeSrc: 'https://router.nebula.fee.red/',
              title: '主路由',
            },
          },
          {
            name: 'SecondaryRouter',
            path: '/system/router/secondary',
            component: IFrameView,
            meta: {
              keepAlive: true,
              icon: 'lucide:router',
              iframeSrc: 'https://pan.router.nebula.fee.red/',
              title: '旁路由',
            },
          },
        ],
      },
      {
        name: 'Aruba',
        path: '/system/aruba',
        redirect: '/system/aruba/instant',
        meta: {
          icon: 'lucide:wifi',
          title: 'Aruba',
          link: 'https://instant.arubanetworks.com:4343/'
        }
      },
      {
        name: 'NAS',
        path: '/system/nas',
        component: IFrameView,
        meta: {
          keepAlive: true,
          icon: 'lucide:database',
          iframeSrc: 'https://nas.fee.red:1000/',
          title: 'NAS',
        },
      },
      {
        name: 'Portainer',
        path: '/system/portainer',
        component: IFrameView,
        meta: {
          icon: 'lucide:ship',
          keepAlive: true,
          iframeSrc: 'https://docker.fee.red:9443',
          title: 'Portainer.io',
        },
      },
      {
        name: 'RabbitMQ',
        path: '/system/rabbitmq',
        component: IFrameView,
        meta: {
          icon: 'lucide:rabbit',
          keepAlive: true,
          iframeSrc: 'https://rabbit.nebula.fee.red',
          title: 'RabbitMQ',
        },
      },
      {
        name: 'Minio',
        path: '/system/minio',
        component: IFrameView,
        meta: {
          keepAlive: true,
          icon: 'lucide:box',
          iframeSrc: 'https://minio.nebula.fee.red/',
          title: 'Minio',
        },
      },
      {
        name: 'UniFi',
        path: '/system/unifi',
        component: IFrameView,
        meta: {
          icon: 'lucide:wifi',
          iframeSrc: 'https://nuc.fee.red:6443/',
          title: 'UniFi',
        },
      },
      {
        name: 'HomeAssistant',
        path: '/system/home-assistant',
        component: IFrameView,
        meta: {
          icon: 'lucide:home',
          iframeSrc: 'https://home-assistant.nebula.fee.red/',
          title: 'Home-Assistant',
        },
      },
    ],
  },
];

export default routes;
