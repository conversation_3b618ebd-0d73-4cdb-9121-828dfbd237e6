import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

// 这些路由会自动嵌套在BasicLayout中
const routes: RouteRecordRaw[] = [
  {
    name: 'Video',
    path: '/video',
    meta: {
      icon: 'lucide:cloud',
      order: 10,
      title: '视频管理',
    },
    children: [
      {
        name: 'VideoList',
        path: '',
        component: () => import('#/views/nebula/video/index.vue'),
        meta: {
          title: '视频管理',
          hideInMenu: true,
          hideChildrenInMenu: true,
        }
      },
      {
        name: 'VideoDetail',
        path: ':id',
        component: () => import('#/views/nebula/video/VideoDetail.vue'),
        meta: {
          title: '视频详情',
          hideInMenu: true,
          hideMenu: true, // 隐藏菜单，不展开父级菜单
          ignoreKeepAlive: true, // 不缓存该页面
          dynamicTitle: true, // 标记为动态标题
        }
      }
    ]
  },
  {
    meta: {
      icon: 'lucide:file-text',
      order: 20,
      title: '备忘录',
    },
    name: 'Memo',
    path: '/memo',
    component: () => import('#/views/nebula/memo/index.vue')
  },
  {
    meta: {
      icon: 'hugeicons:ai-chat-02',
      order: 30,
      title: 'Gemini',
      keepAlive: true, // 添加这一行以启用缓存
    },
    name: 'Gemini',
    path: '/gemini',
    component: () => import('#/views/nebula/gemini/index.vue'),
  },
  {
    meta: {
      icon: 'lucide:hard-drive',
      order: 40,
      title: '存储管理',
    },
    name: 'Storage',
    path: '/storage',
    redirect: '/storage/permanent',
    children: [
      {
        meta: {
          title: '永久存储',
        },
        name: 'PermanentStorage',
        path: 'permanent',
        component: () => import('#/views/nebula/storage/index.vue'),
      },
      {
        meta: {
          title: '临时存储',
        },
        name: 'TemporaryStorage',
        path: 'temporary',
        component: () => import('#/views/nebula/storage/index.vue'),
      },
    ],
  },
  {
    meta: {
      icon: 'lucide:cloud-lightning',
      order: 50,
      title: 'Cloudflare',
    },
    name: 'Cloudflare',
    path: '/cloudflare',
    component: () => import('#/views/nebula/cloudflare/index.vue'),
  },
  {
    name: 'Task',
    path: '/task',
    meta: {
      icon: 'lucide:list-todo',
      order: 55,
      title: '任务监控',
    },
    children: [
      {
        name: 'TaskList',
        path: '',
        component: () => import('#/views/nebula/task/index.vue'),
        meta: {
          title: '任务监控',
          keepAlive: true, // 添加这一行以启用缓存
          hideInMenu: true,
          hideChildrenInMenu: true,
        }
      },
      {
        name: 'TaskDetail',
        path: ':id',
        component: () => import('#/views/nebula/task/TaskDetail.vue'),
        meta: {
          title: '任务详情',
          hideInMenu: true,
          hideMenu: true, // 隐藏菜单，不展开父级菜单
          ignoreKeepAlive: true, // 不缓存该页面
          dynamicTitle: true, // 标记为动态标题
        }
      }
    ]
  },
];

export default routes;
