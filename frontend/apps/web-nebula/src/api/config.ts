/**
 * API配置文件
 * 提供统一的API基础URL和其他共享配置
 */

// 直接从环境变量获取API基础URL
const API_BASE_URL = import.meta.env.VITE_API_URL;

// 导出API基础URL，供所有API调用使用
export { API_BASE_URL };

// 导出API版本
export const API_VERSION = 'v1';

// 导出默认请求头
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
};

// 导出请求超时时间（毫秒）
export const REQUEST_TIMEOUT = 30000;

// 导出响应码
export const RESPONSE_CODES = {
  SUCCESS: 0,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
};
