import { baseRequestClient, requestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    username?: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    errcode: number;
    msg: string;
    token: string;
    token_type: string;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  return requestClient.post<AuthApi.LoginResult>('/auth', data);
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
  // TODO: 后端接口实现后可以取消注释使用真实接口
  // return baseRequestClient.post<AuthApi.RefreshTokenResult>('/auth/refresh', {
  //   withCredentials: true,
  // });

  // 模拟返回刷新的令牌
  return {
    data: 'mock_refreshed_token_' + Date.now(),
    status: 200
  };
}

/**
 * 退出登录
 */
export async function logoutApi() {
  // TODO: 后端接口实现后可以取消注释使用真实接口
  // return baseRequestClient.post('/auth/logout', {
  //   withCredentials: true,
  // });

  // 模拟成功响应
  return { errcode: 0, msg: 'ok' };
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi() {
  // TODO: 后端接口实现后可以取消注释使用真实接口
  // return requestClient.get<string[]>('/auth/codes');

  // 返回模拟的权限列表，包含所有权限
  return [
    'dashboard',
    'dashboard:view',
    'dashboard:edit',
    'video',
    'video:view',
    'video:add',
    'video:edit',
    'video:delete',
    'memo',
    'memo:view',
    'memo:add',
    'memo:edit',
    'memo:delete',
    'gemini',
    'gemini:view',
    'gemini:generate',
    'storage',
    'storage:view',
    'storage:upload',
    'storage:download',
    'storage:delete',
    'admin'
  ];
}
