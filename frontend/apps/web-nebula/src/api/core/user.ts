import type { UserInfo } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * 获取用户信息
 */
export async function getUserInfoApi() {
  // TODO: 后端接口实现后可以取消注释使用真实接口
  // const response = await requestClient.get<any>('/profile');
  //
  // // 将后端响应转换为前端需要的UserInfo格式
  // return {
  //   userId: '1',
  //   username: response.username || 'admin',
  //   realName: response.username || 'Admin',
  //   avatar: '',
  //   desc: 'manager',
  //   password: '',
  //   token: '',
  //   homePath: '/video',
  //   roles: [{
  //     roleName: 'Super Admin',
  //     value: 'super',
  //   }],
  // } as UserInfo;

  // 模拟用户信息
  return {
    userId: '1',
    username: 'xiatian',
    realName: 'Admin',
    avatar: '',
    desc: 'Nebula系统管理员',
    password: '',
    token: '',
    homePath: '/video',
    roles: [{
      roleName: 'Super Admin',
      value: 'super',
    }],
  } as UserInfo;
}
