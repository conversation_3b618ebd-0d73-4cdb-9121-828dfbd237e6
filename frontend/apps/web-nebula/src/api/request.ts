/**
 * 该文件可自行根据业务逻辑进行调整
 */
import type { RequestClientOptions } from '@vben/request';

import { preferences } from '@vben/preferences';
import {
  defaultResponseInterceptor,
  errorMessageResponseInterceptor,
  RequestClient,
} from '@vben/request';
import { useAccessStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { useAuthStore } from '#/store';
import { API_BASE_URL, RESPONSE_CODES } from '#/api/config';

import { refreshTokenApi } from './core';

function createRequestClient(baseURL: string, options?: RequestClientOptions) {
  const client = new RequestClient({
    ...options,
    baseURL,
  });

  /**
   * 重新认证逻辑
   */
  async function doReAuthenticate() {
    console.warn('Access token or refresh token is invalid or expired. ');
    const accessStore = useAccessStore();
    const authStore = useAuthStore();
    accessStore.setAccessToken(null);
    if (
      preferences.app.loginExpiredMode === 'modal' &&
      accessStore.isAccessChecked
    ) {
      accessStore.setLoginExpired(true);
    } else {
      await authStore.logout();
    }
  }

  /**
   * 刷新token逻辑
   */
  async function doRefreshToken() {
    const accessStore = useAccessStore();
    const resp = await refreshTokenApi();
    const newToken = resp.data;
    accessStore.setAccessToken(newToken);
    return newToken;
  }

  function formatToken(token: null | string) {
    return token ? `Bearer ${token}` : null;
  }

  // 请求头处理
  client.addRequestInterceptor({
    fulfilled: async (config) => {
      const accessStore = useAccessStore();

      config.headers.Authorization = formatToken(accessStore.accessToken);
      config.headers['Accept-Language'] = preferences.app.locale;
      return config;
    },
  });

  // 处理返回的响应数据格式
  client.addResponseInterceptor(
    defaultResponseInterceptor({
      codeField: 'errcode',
      dataField: (response) => response, // 直接返回整个响应体
      successCode: RESPONSE_CODES.SUCCESS,
    }),
  );

  // token过期的处理
  client.addResponseInterceptor({
    rejected: async (error) => {
      // 如果是401错误，判断是登录失败还是token过期
      if (error?.response?.status === RESPONSE_CODES.UNAUTHORIZED) {
        // 检查是否是登录请求
        const isLoginRequest = error?.config?.url?.includes('/auth');

        if (isLoginRequest) {
          // 登录失败，直接抛出错误，不进行重新认证
          // 标记该错误已经被处理，避免重复提示
          error.isHandled = true;
          const responseData = error?.response?.data;
          const errorMessage = responseData?.msg || '用户名或密码错误';
          message.error(errorMessage);
          return Promise.reject(error);
        } else {
          // token过期，进行重新认证
          await doReAuthenticate();
          return Promise.reject(error);
        }
      }

      return Promise.reject(error);
    }
  });

  // 通用的错误处理,如果没有进入上面的错误处理逻辑，就会进入这里
  client.addResponseInterceptor(
    errorMessageResponseInterceptor((msg: string, error) => {
      // 检查错误是否已经被处理，如果已经被处理则不再显示错误信息
      if (error.isHandled) {
        return;
      }

      // 这里可以根据业务进行定制,你可以拿到 error 内的信息进行定制化处理，根据不同的 code 做不同的提示，而不是直接使用 message.error 提示 msg
      // 适配后端的错误响应格式 {errcode: xxx, msg: "xxx"}
      const responseData = error?.response?.data ?? {};

      // 先检查是否有 errcode 和 msg
      if (responseData.errcode && responseData.msg) {
        message.error(responseData.msg);
        return;
      }

      // 兼容其他错误格式
      const errorMessage = responseData?.error ?? responseData?.message ?? '';
      // 如果没有错误信息，则会根据状态码进行提示
      message.error(errorMessage || msg);
    }),
  );

  return client;
}

export const requestClient = createRequestClient(API_BASE_URL, {
  responseReturn: 'data',
});

export const baseRequestClient = new RequestClient({ baseURL: API_BASE_URL });
