import { requestClient } from '#/api/request';

export namespace TaskApi {
  /** 任务状态枚举 */
  export enum TaskStatus {
    PENDING = 'pending',
    RUNNING = 'running',
    SUCCESS = 'success',
    FAILED = 'failed',
    RETRYING = 'retrying',
    WAITING_RETRY = 'waitretry',  // 等待重试 (后端定义为 WAITRETRY)
    CANCELLED = 'cancelled'
  }

  /** 日志级别枚举 */
  export type LogLevel = 'DEBUG' | 'INFO' | 'WARNING' | 'ERROR';

  /** 任务尝试记录接口 */
  export interface TaskAttempt {
    retry_index: number;
    started_at: string;
    finished_at?: string;
    status: TaskStatus;
    elapsed_ms?: number;
    error_message?: string;
  }

  /** 任务日志接口 */
  export interface TaskLog {
    id: string;
    task_id: string;
    retry_index: number;
    order_no: number; // 日志顺序号，用于排序
    time: string; // 原来的timestamp字段
    level: LogLevel;
    name?: string; // 日志名称
    message: string;
    step?: string;
    extra?: Record<string, any>;

    // 兼容旧版API可能返回的timestamp字段
    timestamp?: string;
  }

  /** 任务类型枚举 */
  export enum TaskType {
    SCHEDULER = 'scheduler',  // 定时任务
    WORKER = 'worker'         // 常规任务
  }

  /** 任务信息接口 */
  export interface TaskInfo {
    id: string;
    task_id: string;
    task_name: string;
    description?: string;
    task_type?: TaskType;     // 任务类型
    status: TaskStatus;
    created_at: string;
    updated_at?: string;
    started_at?: string;
    finished_at?: string;
    next_retry_at?: string;  // 下次重试时间
    retry_count: number;
    error_message?: string;
    result?: Record<string, any>;
    params?: Record<string, any>;
    labels?: Record<string, any>;
    attempts: TaskAttempt[];
  }

  /** 任务信息（包含日志）接口 */
  export interface TaskInfoWithLogs extends TaskInfo {
    logs: TaskLog[];
  }

  /** 任务列表请求参数 */
  export interface TaskListParams {
    status?: TaskStatus;
    task_type?: TaskType;
    task_name?: string;
    keywords?: string;
    skip?: number;
    limit?: number;
  }

  /** 任务列表响应接口 */
  export interface TaskListResponse {
    items: TaskInfo[];
    total: number;
  }

  /** 任务日志列表请求参数 */
  export interface TaskLogListParams {
    task_id: string;
    skip?: number;
    limit?: number;
  }

  /** 任务日志列表响应接口 */
  export interface TaskLogListResponse {
    items: TaskLog[];
    total: number;
  }
}

/**
 * 获取任务列表
 */
export async function getTaskListApi(params?: TaskApi.TaskListParams) {
  return requestClient.get<TaskApi.TaskListResponse>('/tasks', { params });
}

/**
 * 获取任务详情
 */
export async function getTaskInfoApi(taskId: string, includeLogs: boolean = false) {
  return requestClient.get<TaskApi.TaskInfo | TaskApi.TaskInfoWithLogs>(`/tasks/${taskId}`, {
    params: { include_logs: includeLogs }
  });
}

/**
 * 获取任务日志
 */
export async function getTaskLogsApi(params: TaskApi.TaskLogListParams) {
  return requestClient.get<TaskApi.TaskLogListResponse>(`/tasks/${params.task_id}/logs`, {
    params: { skip: params.skip, limit: params.limit }
  });
}

/**
 * 获取任务状态
 */
export async function getTaskStatusApi(taskId: string) {
  return requestClient.get<{ status: TaskApi.TaskStatus }>(`/tasks/${taskId}/status`);
}
