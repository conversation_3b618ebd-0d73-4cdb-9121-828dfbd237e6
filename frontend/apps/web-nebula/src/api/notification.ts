import { requestClient } from '#/api/request';

export namespace NotificationApi {
  /** 通知项接口 */
  export interface NotificationItem {
    id: string;
    title: string;  // 添加 title 字段
    message: string;
    from_source: string;
    event: string;
    type: string;
    is_read: boolean;
    created_at: string;
    updated_at?: string;
    metadata?: any;
    task_id?: string;
  }

  /** 通知列表响应接口 */
  export interface NotificationListResponse {
    items: NotificationItem[];
    total: number;
    unread: number;
  }

  /** 通用响应接口 */
  export interface CommonResponse {
    errcode: number;
    msg: string;
    success: boolean;
    count?: number;
  }
}

/**
 * 获取通知列表
 */
export async function getNotificationsApi(params?: {
  skip?: number;
  limit?: number;
  include_read?: boolean;
}) {
  return requestClient.get<NotificationApi.NotificationListResponse>('/notifications', { params });
}

/**
 * 标记通知为已读
 */
export async function markAsReadApi(id: string) {
  return requestClient.post<NotificationApi.CommonResponse>(`/notifications/${id}/read`);
}

/**
 * 标记所有通知为已读
 */
export async function markAllAsReadApi() {
  return requestClient.post<NotificationApi.CommonResponse>('/notifications/read-all');
}

/**
 * 删除通知
 */
export async function deleteNotificationApi(id: string) {
  return requestClient.delete<NotificationApi.CommonResponse>(`/notifications/${id}`);
}

/**
 * 清空所有通知
 */
export async function clearAllNotificationsApi() {
  return requestClient.delete<NotificationApi.CommonResponse>('/notifications');
}
