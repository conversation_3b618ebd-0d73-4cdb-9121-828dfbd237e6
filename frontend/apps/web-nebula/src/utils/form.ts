/**
 * 表单处理器
 */
export const formUtils = {
  /**
   * 通用表单验证
   * @param formRef 表单引用
   * @returns 是否验证通过
   */
  async validate(formRef: any): Promise<boolean> {
    if (!formRef) return false;
    try {
      const result = await formRef.validate();
      return result === true;
    } catch (error) {
      return false;
    }
  },

  /**
   * 重置表单
   * @param formData 表单数据
   * @param initialData 初始数据
   */
  reset(formData: Record<string, any>, initialData: Record<string, any> = {}): void {
    Object.keys(formData).forEach(key => {
      if (key in initialData) {
        formData[key] = initialData[key];
      } else {
        // 根据类型设置默认值
        if (Array.isArray(formData[key])) {
          formData[key] = [];
        } else if (typeof formData[key] === 'object' && formData[key] !== null) {
          formData[key] = {};
        } else {
          formData[key] = '';
        }
      }
    });
  }
};
