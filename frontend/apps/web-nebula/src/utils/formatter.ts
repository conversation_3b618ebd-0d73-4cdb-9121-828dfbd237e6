/**
 * 格式化工具
 */
export const formatter = {
  /**
   * 日期时间格式化
   * @param date 日期时间
   * @param options 格式化选项
   * @returns 格式化后的日期时间字符串
   */
  datetime(date: string | number | Date, options: Intl.DateTimeFormatOptions = {}) {
    if (!date) return '';
    return new Date(date).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      ...options
    });
  },

  /**
   * 相对时间格式化
   * @param date 日期时间
   * @returns 格式化后的相对时间字符串（如：刚刚、5分钟前、2小时前等）
   */
  relativeTime(date: string | number | Date) {
    if (!date) return '未知时间';

    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '未知时间';

    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    const diffMonth = Math.floor(diffDay / 30);
    const diffYear = Math.floor(diffMonth / 12);

    if (diffSec < 60) {
      return '刚刚';
    } else if (diffMin < 60) {
      return `${diffMin}分钟前`;
    } else if (diffHour < 24) {
      return `${diffHour}小时前`;
    } else if (diffDay < 30) {
      return `${diffDay}天前`;
    } else if (diffMonth < 12) {
      return `${diffMonth}个月前`;
    } else {
      return `${diffYear}年前`;
    }
  },

  /**
   * 文件大小格式化
   * @param size 文件大小（字节）
   * @returns 格式化后的文件大小字符串
   */
  fileSize(size: number | string) {
    if (!size) return '0 B';
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let value = parseInt(size as string);
    let unitIndex = 0;
    while (value >= 1024 && unitIndex < units.length - 1) {
      value /= 1024;
      unitIndex++;
    }
    return `${value.toFixed(2)} ${units[unitIndex]}`;
  },

  /**
   * URL格式化
   * @param url URL字符串
   * @returns 格式化后的URL字符串
   */
  url(url: string) {
    if (!url) return '';
    try {
      const urlObj = new URL(url);
      return urlObj.hostname + (urlObj.pathname !== '/' ? urlObj.pathname : '');
    } catch (e) {
      return url;
    }
  },

  /**
   * 文本截断
   * @param text 文本内容
   * @param maxLength 最大长度
   * @returns 截断后的文本
   */
  truncate(text: string, maxLength: number) {
    if (!text || text.length <= maxLength) return text;
    return text.slice(0, maxLength) + '...';
  }
};
