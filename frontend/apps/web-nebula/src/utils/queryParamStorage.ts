/**
 * QueryParamStorage - 使用window.name作为存储介质的查询参数传递工具类
 *
 * 用途: 在页面刷新或重定向后保留查询参数，同时保持URL的简洁性
 *
 * 注意事项:
 * 1. window.name在现代浏览器中有一些安全限制，当页面导航到不同域时会被重置
 * 2. window.name将所有值转换为字符串
 * 3. window.name的存储大小约为2MB
 * 4. 同一个命名空间下只有一份存储，即使创建多个实例也会共享同一份数据
 *
 * 使用方式:
 * 1. 实例化: const storage = new QueryParamStorage('my-namespace')
 * 2. 设置值: storage.set('key', 'value')
 * 3. 获取值: storage.get('key')
 * 4. 删除值: storage.remove('key')
 * 5. 清空: storage.clear()
 * 6. 获取URL查询字符串: storage.getQueryString()
 *
 * 静态方法(向后兼容):
 * 1. QueryParamStorage.set('key', 'value') - 使用默认命名空间
 * 2. QueryParamStorage.get('key') - 使用默认命名空间
 * 3. QueryParamStorage.remove('key') - 使用默认命名空间
 * 4. QueryParamStorage.clear() - 清空所有命名空间
 * 5. QueryParamStorage.getQueryString() - 获取默认命名空间的URL查询字符串
 *
 * 示例代码:
 * ```typescript
 * // 创建与标签页相关的存储实例
 * const tabStorage = new QueryParamStorage('tab-video');
 *
 * // 设置参数
 * tabStorage.set('page', 1);
 * tabStorage.set('keyword', 'search');
 *
 * // 获取参数
 * const page = tabStorage.get('page');
 * const keyword = tabStorage.get('keyword');
 *
 * // 获取URL查询字符串
 * const queryString = tabStorage.getQueryString();
 * console.log(queryString); // 输出: tab-video?page=1&keyword=search
 *
 * // 清空当前命名空间的数据
 * tabStorage.clear();
 *
 * // 即使创建多个相同命名空间的实例，它们也会共享同一份数据
 * const tabStorage2 = new QueryParamStorage('tab-video');
 * console.log(tabStorage2.get('page')); // 输出: 1
 * ```
 */

// 存储数据类型
type StorageData = Record<string, any>;
// 全局存储类型，以命名空间为键
type GlobalStorageData = Record<string, StorageData>;

export class QueryParamStorage {
  // 默认命名空间
  private static readonly DEFAULT_NAMESPACE = '_default';
  // 存储所有实例的映射
  private static _instances: Map<string, QueryParamStorage> = new Map();
  // 全局解析的数据
  private static _globalData: GlobalStorageData | null = null;
  // 当前实例的命名空间
  private readonly _namespace!: string;

  /**
   * 构造函数
   * @param namespace 命名空间，用于区分不同的存储实例
   * @returns 如果已存在相同命名空间的实例，则返回已有实例；否则创建新实例
   */
  constructor(namespace: string = QueryParamStorage.DEFAULT_NAMESPACE) {
    if (!namespace || typeof namespace !== 'string') {
      console.error('QueryParamStorage: 命名空间必须是非空字符串');
      namespace = QueryParamStorage.DEFAULT_NAMESPACE;
    }

    // 检查是否已存在相同命名空间的实例
    const existingInstance = QueryParamStorage._instances.get(namespace);
    if (existingInstance) {
      return existingInstance;
    }

    this._namespace = namespace;
    // 将新创建的实例添加到实例缓存中
    QueryParamStorage._instances.set(namespace, this);
  }

  /**
   * 获取指定命名空间的实例，如果不存在则创建
   * @param namespace 命名空间
   * @returns QueryParamStorage实例
   */
  public static getInstance(namespace: string = QueryParamStorage.DEFAULT_NAMESPACE): QueryParamStorage {
    if (!QueryParamStorage._instances.has(namespace)) {
      QueryParamStorage._instances.set(namespace, new QueryParamStorage(namespace));
    }
    return QueryParamStorage._instances.get(namespace)!;
  }

  /**
   * 获取全局存储数据
   * @returns 全局存储数据
   */
  private static getGlobalData(): GlobalStorageData {
    if (typeof window === 'undefined') return {};

    try {
      if (this._globalData) return this._globalData;
      if (window.name && window.name.trim() !== '') {
        const parsed = JSON.parse(window.name);
        window.name = '';
        if (parsed && typeof parsed === 'object' && !Array.isArray(parsed)) {
          this._globalData = parsed as GlobalStorageData;
          return this._globalData;
        }
      }
    } catch (e) {
      console.warn('QueryParamStorage: 解析window.name失败', e);
    }

    this._globalData = {};
    return this._globalData;
  }

  /**
   * 保存全局存储数据
   * @param data 全局存储数据
   */
  private static saveGlobalData(data: GlobalStorageData): void {
    if (typeof window === 'undefined') return;

    try {
      const jsonString = JSON.stringify(data);
      if (jsonString.length > 2000000) {
        console.warn('QueryParamStorage: 数据过大，可能超出浏览器限制');
      }
      window.name = jsonString;
      this._globalData = data;
    } catch (e) {
      console.error('QueryParamStorage: 保存数据失败', e);
    }
  }

  /**
   * 获取当前命名空间的数据
   * @returns 当前命名空间的数据
   */
  private getData(): StorageData {
    const globalData = QueryParamStorage.getGlobalData();
    if (!globalData[this._namespace]) {
      globalData[this._namespace] = {};
    }
    return globalData[this._namespace] as StorageData;
  }

  /**
   * 保存当前命名空间的数据
   * @param data 当前命名空间的数据
   */
  public saveData(data: StorageData): void {
    const globalData = QueryParamStorage.getGlobalData();
    globalData[this._namespace] = data;
    QueryParamStorage.saveGlobalData(globalData);
  }

  /**
   * 设置值
   * @param name 键名
   * @param value 值
   */
  public set(name: string, value: any): void {
    if (!name || typeof name !== 'string') {
      console.error('QueryParamStorage: 名称必须是非空字符串');
      return;
    }

    const data = this.getData();
    data[name] = value;
    this.saveData(data);
  }

  /**
   * 获取值
   * @param name 键名
   * @param forceArray 是否强制返回数组类型
   * @returns 值，如果不存在则返回null
   */
  public get(name: string, forceArray?: boolean): any {
    if (!name || typeof name !== 'string') {
      console.error('QueryParamStorage: 名称必须是非空字符串');
      return null;
    }

    const data = this.getData();
    let value = data[name] ?? null;

    // 如果需要强制返回数组类型
    if (forceArray && value !== null) {
      value = Array.isArray(value) ? value : [value];
    }

    return value;
  }

  /**
   * 删除值
   * @param name 键名
   * @returns 是否成功删除
   */
  public remove(name: string): boolean {
    if (!name || typeof name !== 'string') {
      console.error('QueryParamStorage: 名称必须是非空字符串');
      return false;
    }

    const data = this.getData();
    if (name in data) {
      delete data[name];
      this.saveData(data);
      return true;
    }
    return false;
  }

  /**
   * 清空当前命名空间的所有数据
   */
  public clear(): void {
    const globalData = QueryParamStorage.getGlobalData();
    delete globalData[this._namespace];
    QueryParamStorage.saveGlobalData(globalData);
  }

  /**
   * 检查键是否存在
   * @param name 键名
   * @returns 是否存在
   */
  public has(name: string): boolean {
    if (!name || typeof name !== 'string') return false;
    return name in this.getData();
  }

  /**
   * 获取所有键名
   * @returns 键名数组
   */
  public keys(): string[] {
    return Object.keys(this.getData());
  }

  /**
   * 获取当前命名空间的数据大小（字节数）
   * @returns 数据大小（字节数）
   */
  public size(): number {
    return JSON.stringify(this.getData()).length * 2;
  }

  /**
   * 获取当前命名空间
   * @returns 命名空间
   */
  public getNamespace(): string {
    return this._namespace;
  }

  /**
   * 获取所有命名空间
   * @returns 命名空间数组
   */
  public static getNamespaces(): string[] {
    return Object.keys(this.getGlobalData());
  }

  /**
   * 检查命名空间是否存在
   * @param namespace 命名空间
   * @returns 是否存在
   */
  public static hasNamespace(namespace: string): boolean {
    return namespace in this.getGlobalData();
  }

  /**
   * 检查命名空间是否已被使用
   * @param namespace 命名空间
   * @returns 如果命名空间已被使用且包含数据，则返回true；否则返回false
   */
  public static isNamespaceInUse(namespace: string): boolean {
    const globalData = this.getGlobalData();
    return namespace in globalData &&
           globalData[namespace] !== undefined &&
           Object.keys(globalData[namespace] as StorageData).length > 0;
  }

  /**
   * 清空指定命名空间的数据
   * @param namespace 命名空间
   */
  public static clearNamespace(namespace: string): void {
    const globalData = this.getGlobalData();
    delete globalData[namespace];
    this.saveGlobalData(globalData);
  }

  /**
   * 清空所有数据
   */
  public static clearAll(): void {
    if (typeof window !== 'undefined') {
      window.name = '';
      this._globalData = null;
      console.log("清理所有queryParamStorage");
    }
  }

  /**
   * 获取URL查询字符串
   * @returns 格式为 {namespace}?{key1}={value1}&{key2}={value2} 的字符串
   */
  public getPathQueryString(): string {
    const data = this.getData();
    const queryParams = new URLSearchParams();
    for (const [key, value] of Object.entries(data)) {
      if (Array.isArray(value)) {
        value.forEach(item => queryParams.append(key, String(item)));
      } else {
        queryParams.append(key, String(value));
      }
    }
    return `${this._namespace}?${queryParams.toString()}`;
  }

  // 以下是静态方法，用于向后兼容，内部使用默认命名空间的实例

  /**
   * 设置值（静态方法，使用默认命名空间）
   * @param name 键名
   * @param value 值
   */
  public static set(name: string, value: any): void {
    this.getInstance().set(name, value);
  }

  /**
   * 获取值（静态方法，使用默认命名空间）
   * @param name 键名
   * @param forceArray 是否强制返回数组类型
   * @returns 值，如果不存在则返回null
   */
  public static get(name: string, forceArray?: boolean): any {
    return this.getInstance().get(name, forceArray);
  }

  /**
   * 删除值（静态方法，使用默认命名空间）
   * @param name 键名
   * @returns 是否成功删除
   */
  public static remove(name: string): boolean {
    return this.getInstance().remove(name);
  }

  /**
   * 清空所有数据（静态方法）
   */
  public static clear(): void {
    this.clearAll();
  }

  /**
   * 检查键是否存在（静态方法，使用默认命名空间）
   * @param name 键名
   * @returns 是否存在
   */
  public static has(name: string): boolean {
    return this.getInstance().has(name);
  }

  /**
   * 获取所有键名（静态方法，使用默认命名空间）
   * @returns 键名数组
   */
  public static keys(): string[] {
    return this.getInstance().keys();
  }

  /**
   * 获取数据大小（静态方法，使用默认命名空间）
   * @returns 数据大小（字节数）
   */
  public static size(): number {
    return typeof window !== 'undefined' ? window.name.length * 2 : 0;
  }

  /**
   * 保存数据（静态方法，用于向后兼容）
   * @param data 数据
   */
  public static saveData(data: StorageData): void {
    const globalData = this.getGlobalData();
    globalData[this.DEFAULT_NAMESPACE] = data;
    this.saveGlobalData(globalData);
  }

  /**
   * 获取URL查询字符串（静态方法，使用默认命名空间）
   * @returns 格式为 {namespace}?{key1}={value1}&{key2}={value2} 的字符串get<T = any>(na
   */
  public static getQueryString(): string {
    return this.getInstance().getPathQueryString();
  }
}

export default QueryParamStorage;
