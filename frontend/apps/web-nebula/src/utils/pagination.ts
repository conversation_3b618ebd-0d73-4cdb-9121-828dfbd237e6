/**
 * 分页处理器
 */
export const pagination = {
  /**
   * 处理分页变更
   * @param component 组件实例
   * @param pageInfo 分页信息
   * @param fetchFunction 获取数据的函数
   */
  async handlePageChange(
    component: any, 
    { current, pageSize }: { current: number; pageSize: number }, 
    fetchFunction?: () => Promise<void>
  ): Promise<void> {
    component.current = current;
    component.pageSize = pageSize;
    if (typeof fetchFunction === 'function') {
      await fetchFunction();
    }
  },

  /**
   * 获取分页参数
   * @param component 组件实例
   * @param extraParams 额外参数
   * @returns 分页参数
   */
  getParams(component: any, extraParams: Record<string, any> = {}): Record<string, any> {
    return {
      skip: (component.current - 1) * component.pageSize,
      limit: component.pageSize,
      ...extraParams
    };
  },

  /**
   * 重置分页
   * @param component 组件实例
   */
  reset(component: any): void {
    component.current = 1;
    component.total = 0;
  }
};
