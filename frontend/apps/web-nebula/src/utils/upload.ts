import { apiService } from '../services/api';
import { useActionService } from '../services/action';
import { notifyUtils } from './notify';
import { message } from 'ant-design-vue';

/**
 * 文件上传处理器
 */
export const uploadUtils = {
  /**
   * 处理文件变更
   * @param file 文件对象
   * @param options 上传选项
   * @returns 上传结果
   */
  async process(file: File, options: any = {}): Promise<any> {
    if (!file) {
      notifyUtils.error('文件格式错误', '无法获取文件数据');
      return null;
    }

    const formData = new FormData();
    formData.append('file', file);

    try {
      if (options.loading) {
        options.loading.value = true;
      }

      // 关闭上传中消息，显示成功消息
      message.destroy();
      message.loading('文件上传中...', 0);

      console.log("option", options)

      const actionService = useActionService();
      const response = await actionService.submit(
        apiService.storage.uploadTempFile,
        {
          body: formData,
          bucketName: options.bucketName || 'nebula'
        },
        {
          loading: options.loading,
          showNotify: false,
          onSuccess: options.onSuccess
        }
      );

      if (response?.object_name) {
        message.destroy();
        message.success('文件上传成功');
        return {
          object_name: response.object_name,
          file_type: '.' + file.name.split('.').pop()?.toLowerCase(),
          file_size: file.size,
          url: response.url,
          bucket: response.bucket,
          temp: response.temp
        };
      }
      return null;
    } catch (error) {
      message.destroy();
      message.error('文件上传失败');
      throw error;
    } finally {
      if (options.loading) {
        options.loading.value = false;
      }
    }
  }
};
