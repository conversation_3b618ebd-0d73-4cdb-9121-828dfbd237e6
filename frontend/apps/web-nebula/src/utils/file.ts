import { apiService } from '../services/api';
import { notifyUtils } from './notify';

/**
 * 文件处理工具
 */
export const fileUtils = {
  /**
   * 获取文件URL
   * @param fileKey 文件键
   * @param prefix 前缀
   * @returns 文件URL
   */
  getFileUrl(fileKey: string, prefix = ''): string {
    if (!fileKey) return '';
    return `${prefix}/${fileKey}`;
  },

  /**
   * 获取文件名
   * @param fileKey 文件键
   * @returns 文件名
   */
  getFileName(fileKey: string): string {
    return fileKey?.split('/').pop() || '';
  },

  /**
   * 获取文件扩展名
   * @param fileName 文件名
   * @returns 文件扩展名
   */
  getFileExtension(fileName: string): string {
    return fileName?.split('.').pop() || '';
  },

  /**
   * 获取文件图标
   * @param fileType 文件类型
   * @returns 文件图标名称
   */
  getFileIcon(fileType: string): string {
    if (!fileType) return 'file';
    const type = fileType.toLowerCase();
    const icons: Record<string, string> = {
      'application/pdf': 'file-pdf',
      'application/msword': 'file-word',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'file-word',
      'application/vnd.ms-excel': 'file-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'file-excel',
      'application/vnd.ms-powerpoint': 'file-ppt',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'file-ppt',
      'application/zip': 'file-zip',
      'application/x-zip-compressed': 'file-zip',
      'application/x-rar-compressed': 'file-zip',
      'text/plain': 'file-text',
      'text/html': 'code',
      'text/javascript': 'code',
      'text/css': 'code',
      'application/json': 'code',
      'image/jpeg': 'image',
      'image/png': 'image',
      'image/gif': 'image',
      'image/webp': 'image'
    };
    return icons[type] || 'file';
  },

  /**
   * 格式化文件大小
   * @param bytes 文件大小（字节）
   * @returns 格式化后的文件大小
   */
  formatFileSize(bytes: number): string {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * 格式化时间
   * @param timestamp 时间戳
   * @returns 格式化后的时间
   */
  formatTime(timestamp: string | number | Date): string {
    if (!timestamp) return '未知时间';

    const date = new Date(timestamp);
    return date.toLocaleString();
  },

  /**
   * 格式化过期时间
   * @param expiryTime 过期时间
   * @returns 格式化后的过期时间
   */
  formatExpiryTime(expiryTime: string | number | Date): string {
    if (!expiryTime) return '';

    const now = new Date();
    const expiry = new Date(expiryTime);
    const diffMs = expiry.getTime() - now.getTime();

    if (diffMs <= 0) return '已过期';

    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (diffDays > 0) {
      return `${diffDays}天${diffHours}小时`;
    } else {
      return `${diffHours}小时`;
    }
  },

  /**
   * 获取文件类型颜色
   * @param filename 文件名
   * @returns 颜色代码
   */
  getFileTypeColor(filename: string): string {
    const ext = this.getFileExtension(filename).toLowerCase();
    const colorMap: Record<string, string> = {
      // 图片
      jpg: 'blue',
      jpeg: 'blue',
      png: 'blue',
      gif: 'blue',
      webp: 'blue',
      svg: 'blue',
      // 文档
      pdf: 'red',
      doc: 'blue',
      docx: 'blue',
      xls: 'green',
      xlsx: 'green',
      ppt: 'orange',
      pptx: 'orange',
      txt: 'cyan',
      // 视频
      mp4: 'purple',
      avi: 'purple',
      mov: 'purple',
      wmv: 'purple',
      flv: 'purple',
      mkv: 'purple',
      // 音频
      mp3: 'magenta',
      wav: 'magenta',
      ogg: 'magenta',
      flac: 'magenta',
      aac: 'magenta',
      // 压缩包
      zip: 'volcano',
      rar: 'volcano',
      '7z': 'volcano',
      tar: 'volcano',
      gz: 'volcano',
      // 代码
      js: 'gold',
      html: 'gold',
      css: 'gold',
      py: 'gold',
      java: 'gold',
      php: 'gold',
      cpp: 'gold',
      c: 'gold',
      h: 'gold',
      json: 'gold',
      xml: 'gold'
    };

    return colorMap[ext] || 'default';
  },

  /**
   * 下载文件
   * @param fileKey 文件键
   * @param fileName 文件名
   * @returns 是否下载成功
   */
  async download(fileKey: string, fileName?: string): Promise<boolean> {
    try {
      // 获取预签名URL
      const response = await apiService.storage.getPresignedUrl({
        bucketName: 'nebula',
        objectName: fileKey
      });

      if (response?.url) {
        const a = document.createElement('a');
        a.href = response.url;
        a.download = fileName || fileKey.split('/').pop() || 'download';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        return true;
      }

      notifyUtils.warning('下载失败', '无法获取文件链接');
      return false;
    } catch (error: any) {
      notifyUtils.error('下载失败', error.message || '无法下载文件');
      return false;
    }
  }
};
