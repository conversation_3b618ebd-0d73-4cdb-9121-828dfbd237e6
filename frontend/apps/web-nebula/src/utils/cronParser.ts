/**
 * Cron 表达式解析器
 *
 * 将 cron 表达式解析为通俗易懂的中文描述
 */

/**
 * 解析单个 cron 字段
 * @param field - cron 字段值
 * @param fieldName - 字段名称（用于描述）
 * @param unit - 单位名称
 * @param valueMap - 值映射表（可选，用于星期、月份等）
 * @returns 解析后的中文描述
 */
function parseField(
  field: string,
  fieldName: string,
  unit: string,
  valueMap?: Record<string, string>
): string {
  // 通配符：每个单位
  if (field === '*') {
    return `每${unit}`;
  }

  // 具体数值
  if (/^\d+$/.test(field)) {
    const value = valueMap ? valueMap[field] || field : field;
    return `${fieldName}在${value}${unit}`;
  }

  // 步长：每n个单位
  if (/^\*\/\d+$/.test(field)) {
    const step = field.split('/')[1];
    return `每${step}${unit}`;
  }

  // 范围步长：在范围内每n个单位
  if (/^\d+-\d+\/\d+$/.test(field)) {
    const [range, step] = field.split('/');
    const [start, end] = range.split('-');
    const startValue = valueMap ? valueMap[start] || start : start;
    const endValue = valueMap ? valueMap[end] || end : end;
    return `${fieldName}在${startValue}到${endValue}${unit}，每${step}${unit}`;
  }

  // 列表：多个具体值
  if (/^(\d+,)+\d+$/.test(field)) {
    const values = field.split(',').map(v => valueMap ? valueMap[v] || v : v);
    return `${fieldName}在${values.join('、')}${unit}`;
  }

  // 范围：从某值到某值
  if (/^\d+-\d+$/.test(field)) {
    const [start, end] = field.split('-');
    const startValue = valueMap ? valueMap[start] || start : start;
    const endValue = valueMap ? valueMap[end] || end : end;
    return `${fieldName}在${startValue}到${endValue}${unit}`;
  }

  // 复杂组合：范围和列表的组合
  if (/^[\d,-]+$/.test(field)) {
    const parts = field.split(',');
    const descriptions: string[] = [];

    for (const part of parts) {
      if (/^\d+-\d+$/.test(part)) {
        const [start, end] = part.split('-');
        const startValue = valueMap ? valueMap[start] || start : start;
        const endValue = valueMap ? valueMap[end] || end : end;
        descriptions.push(`${startValue}到${endValue}`);
      } else if (/^\d+$/.test(part)) {
        const value = valueMap ? valueMap[part] || part : part;
        descriptions.push(value);
      }
    }

    return `${fieldName}在${descriptions.join('、')}${unit}`;
  }

  // 回退：无法解析的情况
  return `${fieldName}为${field}`;
}

/**
 * 解析完整的 cron 表达式
 * @param cron - cron 表达式字符串
 * @returns 解析后的中文描述
 */
export function parseCron(cron: string): string {
  const parts = cron.trim().split(/\s+/);

  // 验证 cron 表达式格式
  if (parts.length < 5) {
    return `Cron表达式格式错误: ${cron}`;
  }

  const [minute, hour, day, month, weekday] = parts;

  // 星期映射表
  const weekdayMap: Record<string, string> = {
    '0': '日', '1': '一', '2': '二', '3': '三',
    '4': '四', '5': '五', '6': '六', '7': '日'
  };

  // 月份映射表
  const monthMap: Record<string, string> = {
    '1': '1月', '2': '2月', '3': '3月', '4': '4月',
    '5': '5月', '6': '6月', '7': '7月', '8': '8月',
    '9': '9月', '10': '10月', '11': '11月', '12': '12月'
  };

  // 解析各个字段
  const minuteDesc = parseField(minute, '分钟', '分钟');
  const hourDesc = parseField(hour, '小时', '小时');
  const dayDesc = parseField(day, '日期', '日');
  const monthDesc = parseField(month, '月份', '月', monthMap);

  // 星期字段特殊处理
  let weekdayDesc = '';
  if (weekday !== '*') {
    weekdayDesc = `，${parseField(weekday, '星期', '', weekdayMap)}`;
  }

  // 组合描述
  const fullDesc = `${minuteDesc}，${hourDesc}，${dayDesc}，${monthDesc}${weekdayDesc}`;

  // 简化常见模式的描述
  return simplifyDescription(fullDesc, minute, hour, day, month, weekday, weekdayMap, monthMap);
}

/**
 * 简化常见 cron 模式的描述
 */
function simplifyDescription(
  fullDesc: string,
  minute: string,
  hour: string,
  day: string,
  month: string,
  weekday: string,
  weekdayMap: Record<string, string>,
  monthMap: Record<string, string>
): string {
  // 优先处理步长语法
  // 每n分钟执行
  if (/^\*\/\d+$/.test(minute) && hour === '*' && day === '*' && month === '*' && weekday === '*') {
    const step = minute.split('/')[1];
    return `每 ${step}分钟 执行`;
  }

  // 每n小时执行
  if (/^\*\/\d+$/.test(hour) && day === '*' && month === '*' && weekday === '*') {
    const step = hour.split('/')[1];
    const minuteNum = parseInt(minute) || 0;
    const minuteStr = minuteNum > 0 ? ` 第${minuteNum}分钟` : '';
    return `每 ${step}小时${minuteStr} 执行`;
  }

  // 每n天执行
  if (/^\*\/\d+$/.test(day) && month === '*' && weekday === '*') {
    const step = day.split('/')[1];
    const hourNum = parseInt(hour) || 0;
    const minuteNum = parseInt(minute) || 0;
    const timeStr = `${hourNum}:${minuteNum.toString().padStart(2, '0')}`;
    return `每 ${step}天 ${timeStr} 执行`;
  }

  // 每分钟执行
  if (minute === '*' && hour === '*' && day === '*' && month === '*' && weekday === '*') {
    return '每分钟 执行';
  }

  // 每小时执行（具体分钟）
  if (/^\d+$/.test(minute) && hour === '*' && day === '*' && month === '*' && weekday === '*') {
    return `每小时 第${minute}分钟 执行`;
  }

  // 每天执行（具体小时，且星期为*）
  if (/^\d+$/.test(hour) && day === '*' && month === '*' && weekday === '*') {
    const hourNum = parseInt(hour);
    const minuteNum = parseInt(minute) || 0;
    const timeStr = `${hourNum}:${minuteNum.toString().padStart(2, '0')}`;
    return `每天 ${timeStr} 执行`;
  }

  // 每周执行
  if (weekday !== '*' && day === '*' && month === '*') {
    const hourNum = parseInt(hour) || 0;
    const minuteNum = parseInt(minute) || 0;
    const timeStr = `${hourNum}:${minuteNum.toString().padStart(2, '0')}`;

    if (weekday.includes(',')) {
      const days = weekday.split(',').map(d => weekdayMap[d] || d).join('、');
      return `每周${days} ${timeStr} 执行`;
    } else {
      const dayName = weekdayMap[weekday] || weekday;
      return `每周${dayName} ${timeStr} 执行`;
    }
  }

  // 每月执行
  if (day !== '*' && month === '*' && weekday === '*') {
    const hourNum = parseInt(hour) || 0;
    const minuteNum = parseInt(minute) || 0;
    const timeStr = `${hourNum}:${minuteNum.toString().padStart(2, '0')}`;
    return `每月${day}日 ${timeStr} 执行`;
  }

  // 特定月份执行
  if (month !== '*') {
    const hourNum = parseInt(hour) || 0;
    const minuteNum = parseInt(minute) || 0;
    const timeStr = `${hourNum}:${minuteNum.toString().padStart(2, '0')}`;

    if (month.includes(',')) {
      const months = month.split(',').map(m => monthMap[m] || m).join('、');
      return `每年${months}${day !== '*' ? day + '日' : ''} ${timeStr} 执行`;
    } else {
      const monthName = monthMap[month] || month;
      return `每年${monthName}${day !== '*' ? day + '日' : ''} ${timeStr} 执行`;
    }
  }

  // 返回完整描述
  return fullDesc;
}

/**
 * 验证 cron 表达式格式
 * @param cron - cron 表达式字符串
 * @returns 是否为有效的 cron 表达式
 */
export function isValidCron(cron: string): boolean {
  const parts = cron.trim().split(/\s+/);
  return parts.length >= 5;
}

/**
 * 检查时间值是否匹配 cron 字段
 * @param value - 时间值
 * @param field - cron 字段
 * @param min - 字段最小值
 * @param max - 字段最大值
 * @returns 是否匹配
 */
function matchesField(value: number, field: string, min: number, _max: number): boolean {
  // 通配符
  if (field === '*') {
    return true;
  }

  // 具体数值
  if (/^\d+$/.test(field)) {
    return value === parseInt(field);
  }

  // 步长：*/n
  if (/^\*\/\d+$/.test(field)) {
    const stepStr = field.split('/')[1];
    if (stepStr) {
      const step = parseInt(stepStr);
      return (value - min) % step === 0;
    }
    return false;
  }

  // 范围步长：a-b/n
  if (/^\d+-\d+\/\d+$/.test(field)) {
    const parts = field.split('/');
    const range = parts[0];
    const stepStr = parts[1];
    if (range && stepStr) {
      const rangeParts = range.split('-');
      const start = rangeParts[0] ? parseInt(rangeParts[0]) : 0;
      const end = rangeParts[1] ? parseInt(rangeParts[1]) : 0;
      const step = parseInt(stepStr);
      return value >= start && value <= end && (value - start) % step === 0;
    }
    return false;
  }

  // 列表：a,b,c
  if (/^[\d,]+$/.test(field)) {
    const values = field.split(',').map(Number);
    return values.includes(value);
  }

  // 范围：a-b
  if (/^\d+-\d+$/.test(field)) {
    const parts = field.split('-');
    const start = parts[0] ? parseInt(parts[0]) : 0;
    const end = parts[1] ? parseInt(parts[1]) : 0;
    return value >= start && value <= end;
  }

  // 复杂组合：范围和列表的组合
  if (/^[\d,-]+$/.test(field)) {
    const parts = field.split(',');
    return parts.some(part => {
      if (/^\d+-\d+$/.test(part)) {
        const rangeParts = part.split('-');
        const start = rangeParts[0] ? parseInt(rangeParts[0]) : 0;
        const end = rangeParts[1] ? parseInt(rangeParts[1]) : 0;
        return value >= start && value <= end;
      } else if (/^\d+$/.test(part)) {
        return value === parseInt(part);
      }
      return false;
    });
  }

  return false;
}

/**
 * 计算 cron 表达式的下次执行时间
 * @param cron - cron 表达式字符串
 * @param fromTime - 起始时间（可选，默认为当前时间）
 * @returns 下次执行时间的 Date 对象，如果无法计算则返回 null
 */
export function getNextExecutionTime(cron: string, fromTime?: Date): Date | null {
  const parts = cron.trim().split(/\s+/);

  // 验证 cron 表达式格式
  if (parts.length < 5) {
    return null;
  }

  const [minute = '*', hour = '*', day = '*', month = '*', weekday = '*'] = parts;

  // 处理特殊的星期表示（MON, TUE等）
  const normalizedWeekday = weekday
    .replace(/MON/gi, '1')
    .replace(/TUE/gi, '2')
    .replace(/WED/gi, '3')
    .replace(/THU/gi, '4')
    .replace(/FRI/gi, '5')
    .replace(/SAT/gi, '6')
    .replace(/SUN/gi, '0');

  // 从指定时间开始（默认为当前时间的下一分钟）
  const startTime = fromTime ? new Date(fromTime) : new Date();
  const nextTime = new Date(startTime);
  nextTime.setSeconds(0, 0); // 重置秒和毫秒
  nextTime.setMinutes(nextTime.getMinutes() + 1); // 从下一分钟开始

  // 最多查找未来2年内的时间，避免无限循环
  const maxIterations = 365 * 24 * 60 * 2; // 2年的分钟数
  let iterations = 0;

  while (iterations < maxIterations) {
    const currentMinute = nextTime.getMinutes();
    const currentHour = nextTime.getHours();
    const currentDay = nextTime.getDate();
    const currentMonth = nextTime.getMonth() + 1; // JavaScript月份从0开始
    const currentWeekday = nextTime.getDay(); // 0=周日, 1=周一, ...

    // 检查是否匹配所有字段
    const minuteMatch = matchesField(currentMinute, minute, 0, 59);
    const hourMatch = matchesField(currentHour, hour, 0, 23);
    const dayMatch = matchesField(currentDay, day, 1, 31);
    const monthMatch = matchesField(currentMonth, month, 1, 12);

    // 星期字段特殊处理（0和7都表示周日）
    let weekdayMatch = true;
    if (normalizedWeekday !== '*') {
      weekdayMatch = matchesField(currentWeekday, normalizedWeekday, 0, 6);
      // 如果字段包含7，也要检查是否匹配周日(0)
      if (!weekdayMatch && normalizedWeekday.includes('7')) {
        weekdayMatch = currentWeekday === 0;
      }
    }

    // 如果所有字段都匹配，返回这个时间
    if (minuteMatch && hourMatch && dayMatch && monthMatch && weekdayMatch) {
      return nextTime;
    }

    // 递增到下一分钟
    nextTime.setMinutes(nextTime.getMinutes() + 1);
    iterations++;
  }

  // 如果找不到匹配的时间，返回 null
  return null;
}

/**
 * 格式化相对时间描述
 * @param date - 目标时间
 * @param baseTime - 基准时间（默认为当前时间）
 * @returns 相对时间描述
 */
function formatRelativeTime(date: Date, baseTime: Date = new Date()): string {
  const diffMs = date.getTime() - baseTime.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMinutes < 1) {
    return '即将执行';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟后`;
  } else if (diffHours < 24) {
    return `${diffHours}小时后`;
  } else if (diffDays === 1) {
    return `明天 ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
  } else if (diffDays < 7) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const weekday = weekdays[date.getDay()];
    return `${weekday} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
  } else {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}

/**
 * 获取 cron 表达式的下次执行时间描述
 * @param cron - cron 表达式字符串
 * @param fromTime - 起始时间（可选，默认为当前时间）
 * @returns 下次执行时间的描述字符串
 */
export function getNextExecutionDescription(cron: string, fromTime?: Date): string {
  const nextTime = getNextExecutionTime(cron, fromTime);

  if (!nextTime) {
    return '无法计算下次执行时间';
  }

  const baseTime = fromTime || new Date();
  return formatRelativeTime(nextTime, baseTime);
}
