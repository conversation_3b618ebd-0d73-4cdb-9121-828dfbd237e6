import { notification } from 'ant-design-vue';
import type { NotificationArgsProps } from 'ant-design-vue';

/**
 * 通知工具
 */
export const notifyUtils = {
  /**
   * 成功通知
   * @param title 标题
   * @param content 内容
   * @param duration 显示时长（毫秒）
   * @param options 其他选项
   */
  success(title: string, content?: string, duration = 5000, options?: Partial<NotificationArgsProps>) {
    notification.success({
      message: title,
      description: content,
      duration: duration / 1000, // Ant Design Vue 使用秒作为单位
      placement: 'bottomRight',
      ...options
    });
  },

  /**
   * 错误通知
   * @param title 标题
   * @param content 内容
   * @param duration 显示时长（毫秒）
   * @param options 其他选项
   */
  error(title: string, content?: string, duration = 5000, options?: Partial<NotificationArgsProps>) {
    notification.error({
      message: title,
      description: content,
      duration: duration / 1000,
      placement: 'bottomRight',
      ...options
    });
  },

  /**
   * 警告通知
   * @param title 标题
   * @param content 内容
   * @param duration 显示时长（毫秒）
   * @param options 其他选项
   */
  warning(title: string, content?: string, duration = 3000, options?: Partial<NotificationArgsProps>) {
    notification.warning({
      message: title,
      description: content,
      duration: duration / 1000,
      placement: 'bottomRight',
      ...options
    });
  },

  /**
   * 信息通知
   * @param title 标题
   * @param content 内容
   * @param duration 显示时长（毫秒）
   * @param options 其他选项
   */
  info(title: string, content?: string, duration = 3000, options?: Partial<NotificationArgsProps>) {
    notification.info({
      description: content,
      duration: duration / 1000,
      placement: 'bottomRight',
      ...options
    });
  }
};
