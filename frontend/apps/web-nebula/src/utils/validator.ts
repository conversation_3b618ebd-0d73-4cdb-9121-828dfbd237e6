/**
 * 数据验证器
 */
export const validator = {
  /**
   * 通用URL验证
   * @param url URL字符串
   * @returns 是否为有效URL
   */
  isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  /**
   * 文件大小验证
   * @param size 文件大小
   * @param maxSize 最大允许大小
   * @returns 是否为有效文件大小
   */
  isValidFileSize(size: number, maxSize: number): boolean {
    return size > 0 && size <= maxSize;
  },

  /**
   * 必填验证
   * @param val 要验证的值
   * @returns 是否为非空值
   */
  isRequired(val: any): boolean {
    if (val === undefined || val === null) return false;
    if (typeof val === 'string') return val.trim().length > 0;
    if (Array.isArray(val)) return val.length > 0;
    if (typeof val === 'object') return Object.keys(val).length > 0;
    return true;
  }
};
