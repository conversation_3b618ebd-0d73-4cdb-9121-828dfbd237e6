/**
 * 声音工具函数
 * 提供播放通知声音的功能
 */

/**
 * 播放通知铃声
 * 使用Web Audio API创建一个简单的"叮"声
 */
export function playNotificationSound() {
  // 检查浏览器是否支持Web Audio API
  if (typeof window === 'undefined' || !('AudioContext' in window)) {
    return;
  }

  try {
    // 创建音频上下文
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    
    // 如果音频上下文被暂停（浏览器策略），尝试恢复
    if (audioContext.state === 'suspended') {
      audioContext.resume();
    }

    // 创建振荡器
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    // 连接节点
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    // 设置声音参数 - 简单的"叮"声
    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(1046.5, audioContext.currentTime); // C6
    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
    
    // 播放声音
    oscillator.start();
    oscillator.stop(audioContext.currentTime + 0.5);
  } catch (error) {
    console.error('播放通知声音失败:', error);
  }
}
