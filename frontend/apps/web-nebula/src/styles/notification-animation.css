/**
 * 通知动画样式
 */

/* 通知图标摆动动画 */
@keyframes notification-shake {
  0% {
    transform: rotate(0deg);
  }
  10% {
    transform: rotate(-10deg);
  }
  20% {
    transform: rotate(10deg);
  }
  30% {
    transform: rotate(-10deg);
  }
  40% {
    transform: rotate(10deg);
  }
  50% {
    transform: rotate(-10deg);
  }
  60% {
    transform: rotate(10deg);
  }
  70% {
    transform: rotate(-10deg);
  }
  80% {
    transform: rotate(5deg);
  }
  90% {
    transform: rotate(-5deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

/* 应用摆动动画的类 */
.notification-icon-shake,
#notification-bell-icon.notification-icon-shake {
  animation: notification-shake 0.8s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  transform-origin: center center;
}
