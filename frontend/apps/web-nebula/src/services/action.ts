import { ref, h } from 'vue';
import { useWebSocketService } from './websocket';
import { notifyUtils } from '../utils/notify';
import { Modal } from 'ant-design-vue';

interface TaskCallback {
  resolve: (value: any) => void;
  reject: (reason?: any) => void;
  timeoutId: ReturnType<typeof setTimeout>;
  loadingRef?: any;
  options: any;
}

interface TaskEvent {
  task_id: string;
  status?: string;
  data?: any;
  message?: string;
  event?: string;
  text?: string; // 错误消息文本
}

// Action 服务单例
let actionServiceInstance: ReturnType<typeof createActionService> | null = null;

// 创建 Action 服务
const createActionService = () => {
  const wsService = useWebSocketService();

  // 任务回调管理
  const taskCallbacks = new Map<string, TaskCallback>();
  // 暂存的任务事件
  const pendingTaskEvents = new Map<string, TaskEvent>();

  // 处理WebSocket任务事件
  const handleTaskEvent = (msg: TaskEvent) => {
    if (!msg?.task_id) return;
    // 如果任务回调还没有注册，将事件暂存到队列中
    if (!taskCallbacks.has(msg.task_id)) {
      console.log("任务响应未注册，暂存事件:", msg.task_id);

      // 将事件添加到队列
      pendingTaskEvents.set(msg.task_id, msg);

      // 设置定时器，如果一段时间后仍然没有注册回调，则清除暂存事件
      setTimeout(() => {
        if (pendingTaskEvents.has(msg.task_id)) {
          console.log("清除过期的暂存任务事件:", msg.task_id);
          pendingTaskEvents.delete(msg.task_id);
        }
      }, 60000); // 1分钟后清除

      return;
    }

    const taskInfo = taskCallbacks.get(msg.task_id)!;
    const { resolve, reject, timeoutId, loadingRef, options } = taskInfo;
    const showNotify = options?.showNotify !== false; // 默认为 true
    const showErrorNotify = options?.showErrorNotify !== false; // 默认为 true

    // 清理loading状态
    if (loadingRef) {
      loadingRef.value = false;
    }

    // 清除超时定时器
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // 从任务回调映射中移除
    taskCallbacks.delete(msg.task_id);

    // 处理任务结果
    if (msg.data?.status) {
      // 处理成功消息
      if (showNotify) {
        const successMsg = options?.successMsg || msg.data?.message || '操作成功';
        const successTitle = options?.successTitle || msg.event?.split('.')[1] || '成功';
        notifyUtils.success(successTitle, successMsg);
      }

      // 调用成功回调
      if (options.onSuccess) {
        options.onSuccess(msg.data);
      }

      resolve(msg.data);
    } else {
      // 处理错误消息
      if (showErrorNotify) {
        const errorMsg = msg.text || options?.errorMsg || '操作失败';
        const errorTitle = options?.errorTitle || msg.event?.split('.')[1] || '错误';
        notifyUtils.error(errorTitle, errorMsg);
      }

      // 调用错误回调
      if (options.onError) {
        options.onError(msg.data);
      }

      // 使用自定义错误对象，避免在控制台显示错误堆栈
      // 将错误对象包装在一个特殊的属性中，避免控制台显示错误
      // const customError = {
      //   __isCustomError: true,
      //   message: msg.text || '操作失败',
      //   data: msg.data,
      //   toString: function() { return this.message; }
      // };
      // reject(customError);
    }
  };

  // 初始化方法，注册 WebSocket 事件监听器
  const initialize = () => {
    // 注册WebSocket事件监听 - 监听原始消息，处理包含 task_id 的消息
    wsService.addMessageListener('message', (msg: any) => {
      // 如果消息包含 task_id，则交给 handleTaskEvent 处理
      if (msg.event == "connected") {
          const result = wsService.subscribeToTopic('GENERAL');
          console.log('订阅主题[GENERAL]:', result);
          return;
      }
      if (msg.event != "subscribed") {  
        console.log("收到ws.message:", msg);
      }
      
      if (msg && msg.task_id) {
        console.log('任务回调:', msg.task_id);
        handleTaskEvent(msg);
      }
    });

    // console.log('Action 服务初始化完成');
  };

  // 初始化服务
  initialize();

  // 提交API请求方法
  const submit = async (apiFunc: Function, args: any = {}, options: any = {}) => {
    const loadingRef = options.loading;
    const showNotify = options.showNotify !== false; // 默认为 true
    const showSubmitNotify = options.showSubmitNotify === true; // 默认为 false
    const submitNotifyMsg = options.submitNotifyMsg || '操作已提交，正在处理中';
    const submitNotifyTitle = options.submitNotifyTitle || '提示';

    // 设置loading状态
    if (loadingRef) {
      loadingRef.value = true;
    }

    try {
      // 处理 response_mode
      const defaultArgs = {
        response_mode: args.response_mode || "sync"
      };

      // 合并默认参数和用户参数
      const mergedArgs = { ...defaultArgs, ...args };

      // 如果是异步任务且需要显示提交通知，则在提交前显示
      if (showSubmitNotify) {
        notifyUtils.info(submitNotifyTitle, submitNotifyMsg);
      }

      // 调用API
      const response = await apiFunc(mergedArgs);

      // 如果响应包含_task_id，说明是异步任务
      if (response && response._task_id) {
        const taskId = response._task_id;
        if (options.onAsyncTaskSubmitted) {
          try{
            options.onAsyncTaskSubmitted(response);
          } catch (error) {
            console.error('onAsyncTaskSubmitted 回调出错:', error);
          }
        }
        return new Promise((resolve, reject) => {
          // 设置超时
          const timeoutId = setTimeout(() => {
            taskCallbacks.delete(taskId);
            if (loadingRef) {
              loadingRef.value = false;
            }
            // 使用自定义错误对象，避免在控制台显示错误堆栈
            const customError = { message: '任务超时', data: null };
            reject(customError);
          }, 10 * 60 * 1000); // 10分钟超时

          console.log("注册任务回调:", taskId);
          // 存储回调
          taskCallbacks.set(taskId, {
            resolve,
            reject,
            timeoutId,
            loadingRef,
            options: { ...options, showNotify }
          });

          // 检查是否有暂存的任务事件
          if (pendingTaskEvents && pendingTaskEvents.has(taskId)) {
            console.log("发现暂存的任务事件，立即处理:", taskId);
            const pendingEvent = pendingTaskEvents.get(taskId)!;
            pendingTaskEvents.delete(taskId);

            // 异步处理，避免阻塞当前执行流
            setTimeout(() => {
              handleTaskEvent(pendingEvent);
            }, 0);
          }
        });
      } else {
        // 同步响应，直接处理结果
        if (loadingRef) {
          loadingRef.value = false;
        }

        // 处理成功消息
        if (response){
          if (showNotify) {
            const successMsg = options?.successMsg || response?.msg || '操作成功';
            const successTitle = options?.successTitle || '成功';
            notifyUtils.success(successTitle, successMsg);
          }

          // 调用成功回调
          if (options.onSuccess) {
            await options.onSuccess(response);
          }
        }

        return response;
      }
    } catch (error: any) {
      // 处理错误
      if (loadingRef) {
        loadingRef.value = false;
      }

      // 显示错误消息
      if (showNotify) {
        const errorMsg = error.message || options.errorMsg || '返回错误';
        const errorTitle = options.errorTitle || '返回错误';
        notifyUtils.error(errorTitle, errorMsg);
      }

      // 调用错误回调
      if (options.onError) {
        await options.onError(error);
      }

      // 使用自定义错误对象，避免在控制台显示错误堆栈
      const customError = {
        message: error.message || options.errorMsg || '操作失败',
        data: error.data || error.response || null,
        originalError: error
      };
      throw customError;
    }
  };

  // 取消任务
  const cancel = (taskId: string) => {
    const taskInfo = taskCallbacks.get(taskId);
    if (taskInfo) {
      const { reject, timeoutId, loadingRef } = taskInfo;

      // 清理loading状态
      if (loadingRef) {
        loadingRef.value = false;
      }

      // 清理超时定时器
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // 使用自定义错误对象，避免在控制台显示错误堆栈
      const customError = { message: '任务已取消', data: null };
      reject(customError);
      taskCallbacks.delete(taskId);
      return true;
    }
    return false;
  };

  return {
    submit,
    handleTaskEvent,
    cancel
  };
};

// 导出单例服务
export const useActionService = () => {
  if (!actionServiceInstance) {
    // console.log('创建新的 Action 服务实例');
    actionServiceInstance = createActionService();
  } else {
    // console.log('复用现有的 Action 服务实例');
  }
  return actionServiceInstance;
};
