import { notifyUtils } from '#/utils';
import { useUserStore, useAccessStore } from '@vben/stores';
import axios from 'axios';

// 导入统一的API配置
import { API_BASE_URL, RESPONSE_CODES } from '#/api/config';

// API 服务
export const apiService = {
  // 获取认证头
  getAuthHeaders(extraHeaders = {}) {
    // 从 Pinia store 中获取 token
    const accessStore = useAccessStore();
    const token = accessStore.accessToken;
    const tokenType = 'Bearer';

    return {
      'Content-Type': 'application/json',
      ...(token ? {
        'Authorization': `${tokenType} ${token}`
      } : {}),
      ...extraHeaders
    };
  },

  // 处理API响应
  async handleResponse(response) {
    // axios将状态码不是2xx的请求视为错误，会进入catch块
    // 因此这里直接处理成功的响应
    const data = response.data;
    if (data.errcode !== RESPONSE_CODES.SUCCESS) {
      throw new Error(data.msg || '返回错误');
    }

    return data;
  },

  // 通用请求方法
  async request(method, endpoint, args = {}, stream = false) {
    try {
      // 提取 response_mode、callback 和 body
      const { response_mode, callback, body, ...otherArgs } = args || {};

      // 准备额外的头部
      const extraHeaders = {};
      if (response_mode && response_mode !== 'sync') {
        extraHeaders['X-Response-Mode'] = response_mode;
      }
      if (callback) {
        extraHeaders['X-Callback-Url'] = callback;
      }

      // 构建请求配置
      const config = {
        method,
        url: `${API_BASE_URL}/${endpoint.replace(/^\//, '')}`,
        headers: this.getAuthHeaders(extraHeaders)
      };
      if (stream) {
        config.responseType = 'stream';
        config.adapter = 'fetch'; // 使用fetch adapter处理流式请求
      }

      // 处理请求体 - POST, PUT, PATCH, DELETE 方法
      if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method.toUpperCase()) && body) config.data = body;

      // 处理URL查询参数 - 仅对GET请求处理otherArgs为查询参数
      if (method.toUpperCase() === 'GET' && otherArgs && Object.keys(otherArgs).length > 0) {
        const params = new URLSearchParams();

        // 递归函数，处理嵌套对象
        const processObject = (obj, prefix = '') => {
          for (const [key, value] of Object.entries(obj)) {
            const paramKey = prefix ? `${prefix}.${key}` : key;

            if (value === null || value === undefined) {
              continue;
            } else if (typeof value === 'object' && !Array.isArray(value)) {
              // 递归处理嵌套对象
              processObject(value, paramKey);
            } else if (Array.isArray(value)) {
              // 处理数组
              value.forEach((item) => {
                if (typeof item === 'object' && !Array.isArray(item)) {
                  processObject(item, paramKey);
                } else {
                  // 直接使用同名参数，而不是索引形式
                  params.append(paramKey, item);
                }
              });
            } else {
              // 处理基本类型
              params.append(paramKey, value);
            }
          }
        };

        processObject(otherArgs);
        const queryString = params.toString();
        if (queryString) {
          config.url = `${config.url}${config.url.includes('?') ? '&' : '?'}${queryString}`;
        }
      }

      // 发送请求
      const response = await axios(config);
      if (stream) {
        return response;
      }
      return await this.handleResponse(response);
    } catch (error) {
      console.error(`API ${method}请求错误 (${endpoint}):`, error);

      // 处理axios错误
      if (error.response) {
        // 服务器返回了错误状态码
        if (error.response.status === RESPONSE_CODES.UNAUTHORIZED) {
          // 使用 useAuthStore 的 logout 方法处理登出
          const accessStore = useAccessStore();
          accessStore.setAccessToken(null);
          window.location.hash = '#/login';
          throw new Error('登录已过期，请重新登录');
        }
        else if (error.response.data) {
          notifyUtils.error(`返回错误,status=${error.response.data.errcode}`, error.response.data.msg);
          // 这里如果不返回值,action.ts里的submit -> await apiFunc(mergedArgs) == null
        }
      } else if (error.request) {
        // 请求已发送但没有收到响应
        throw new Error('服务器无响应，请检查网络连接');
      } else {
        // 请求配置错误
        throw error;
      }
    }
  },

  // 文件上传方法
  async upload(endpoint: string, args: any = {}) {
    try {
      const url = `${API_BASE_URL}/${endpoint.replace(/^\//, '')}`;

      // 获取formData
      const formData = args.body;
      if (!(formData instanceof FormData)) {
        throw new Error('上传失败: 参数必须是FormData对象');
      }

      // 提取 response_mode 和 callback
      const responseMode = args.response_mode;
      const callback = args.callback;

      // 准备额外的头部
      const extraHeaders: Record<string, any> = {
        // 移除 Content-Type，让 axios 自动设置为 multipart/form-data
        'Content-Type': null
      };

      if (responseMode && responseMode !== 'sync') {
        extraHeaders['X-Response-Mode'] = responseMode;
        if (!formData.has('response_mode')) {
          formData.append('response_mode', responseMode);
        }
      }

      if (callback) {
        extraHeaders['X-Callback-Url'] = callback;
        if (!formData.has('callback')) {
          formData.append('callback', callback);
        }
      }

      const response = await axios.post(url, formData, {
        headers: this.getAuthHeaders(extraHeaders)
      });

      return await this.handleResponse(response);
    } catch (error: any) {
      // console.error(`文件上传错误 (${endpoint}):`, error);

      // 处理axios错误
      if (error.response) {
        // 服务器返回了错误状态码
        if (error.response.status === RESPONSE_CODES.UNAUTHORIZED) {
          // 使用 useAuthStore 的 logout 方法处理登出
          const accessStore = useAccessStore();
          accessStore.setAccessToken(null);
          window.location.hash = '#/login';
          throw new Error('登录已过期，请重新登录');
        }
        else if (error.response.data) {
          throw new Error(error.response.data.msg || `返回错误,status=${error.response.data.errcode}`);
        }
      } else if (error.request) {
        // 请求已发送但没有收到响应
        throw new Error('服务器无响应，请检查网络连接');
      } else {
        // 请求配置错误
        throw error;
      }
    }
  },

  // HTTP方法简写
  get: (endpoint: string, args = {}) => apiService.request('GET', endpoint, args),
  post: (endpoint: string, args = {}) => apiService.request('POST', endpoint, args),
  put: (endpoint: string, args = {}) => apiService.request('PUT', endpoint, args),
  delete: (endpoint: string, args = {}) => apiService.request('DELETE', endpoint, args),
  stream: (endpoint: string, args = {}) => apiService.request('POST', endpoint, args, true),

  // 视频API
  video: {
    // 获取视频列表
    list: (args: any) => apiService.get('video', args),

    // 获取单个视频详情
    get: (args: any) => apiService.get(`video/${args.id}`, args),

    // 创建视频
    create: (args: any) => apiService.post('video', args),

    // 更新视频信息
    update: (args: any) => apiService.put(`video/${args.id}`, args),

    // 删除视频
    delete: (args: any) => apiService.delete(`video/${args.id}`, args),

    // 分析视频内容
    analyze: (args: any) => apiService.post(`video/${args.id}/analyze`, { ...args, response_mode: "websocket" }),

    // 从分享链接提取视频信息
    extract: (args: any) => apiService.post('video/extract', { ...args }),

    // 从分享链接导入视频
    importFromShare: (args: any) => apiService.post('video/import-from-share', { ...args, response_mode: "websocket" }),

    // 从URL创建视频
    create_from_url: (args: any) => apiService.post('video/create-from-url', { ...args, response_mode: "websocket" }),

    // 添加标签
    addTags: (args: any) => apiService.post(`video/${args.id}/tags`, args),

    // 删除标签
    removeTags: (args: any) => apiService.delete(`video/${args.id}/tags`, args),
  },

  memo: {
    // 获取备忘录列表
    list: (args: any = {}) => apiService.get('memo', args),

    // 获取单个备忘录详情
    get: (args: any) => apiService.get(`memo/${args.id}`, args),

    // 创建文本备忘录
    createText: (args: any) => apiService.post('memo/text', args),

    // 创建图片备忘录
    createImage: (args: any) => apiService.post('memo/image', args),

    // 创建文件备忘录
    createFile: (args: any) => apiService.post('memo/file', args),

    // 创建分享备忘录
    createShare: (args: any) => apiService.post('memo/share', args),

    // 更新备忘录
    update: (args: any) => apiService.put(`memo/${args.id}`, args),

    // 删除备忘录
    delete: (args: any) => apiService.delete(`memo/${args.id}`, args),

    // 分析备忘录内容
    analyze: (args: any) => apiService.post(`memo/${args.id}/analyze`, { ...args, response_mode: "websocket" }),
  },

  // 存储服务相关
  storage: {
    // 上传文件
    uploadFile: ({ bucketName, ...restArgs }) => apiService.upload(`storage/upload/${bucketName}`, restArgs),
    uploadTempFile: ({ bucketName, ...restArgs }) => apiService.upload(`storage/upload-temp/${bucketName}`, restArgs),
    cleanupTempFiles: (restArgs) => apiService.upload('storage/cleanup-temp-files', restArgs),

    // 查询和获取
    getFileList: ({ bucketName, ...restArgs }) => apiService.get(`storage/${bucketName}`, restArgs),
    getTempFileList: ({ bucketName, ...restArgs }) => apiService.get(`storage/${bucketName}`, {...restArgs, temp_only: true}),
    getPresignedUrl: ({ bucketName, objectName, ...restArgs }) => apiService.get(`storage/presigned-url/${bucketName}/${objectName}`, restArgs),

    // 删除操作
    deleteFile: ({ bucketName, objectName, ...restArgs }) => apiService.delete(`storage/${bucketName}/${objectName}`, restArgs),
    deleteTempFile: ({ bucketName, objectName, ...restArgs }) => apiService.delete(`storage/${bucketName}/temp/${objectName}`, restArgs)
  },

  // Gemini AI 相关API
  gemini: {
    // 根据文本生成内容
    generateText: (args) => apiService.post('gemini/generate/text', args),
    // 根据文件生成内容
    generateFile: (args) => apiService.post('gemini/generate/file', args),
    // 流式根据文本生成内容
    // 注意：流式响应需要使用原生 fetch API 处理，不能使用标准的 action.submit 方法
    streamGenerateText: (args) => apiService.stream('gemini/generate/text/stream', args),
    // 流式根据文件生成内容
    // 注意：流式响应需要使用原生 fetch API 处理，不能使用标准的 action.submit 方法
    streamGenerateFile: (args) => apiService.stream('gemini/generate/file/stream', args),
  },

  // Cloudflare相关API
  cloudflare: {
    // 保存Cloudflare配置
    saveConfig: (restArgs: any) => apiService.post('cloudflare/config', { ...restArgs, response_mode: "websocket" }),

    // 获取Cloudflare账户信息
    getFullAccountInfo: (restArgs: any = {}) => apiService.get('cloudflare/config', restArgs),

    // 验证API密钥
    validateApiKey: (restArgs: any) => apiService.post('cloudflare/validate-api-key', { ...restArgs, response_mode: "websocket" }),

    // 同步域名
    syncDomains: (restArgs: any) => apiService.post('cloudflare/sync', { ...restArgs, response_mode: "websocket" }),

    // 创建DNS记录
    createDnsRecord: ({ domainId, ...restArgs }: any) => apiService.post(`cloudflare/domain/${domainId}/dns`, { ...restArgs, response_mode: "websocket" }),

    // 更新DNS记录
    updateDnsRecord: ({ domainId, recordId, ...restArgs }: any) => apiService.put(`cloudflare/domain/${domainId}/dns_record/${recordId}`, { ...restArgs, response_mode: "websocket" }),

    // 删除DNS记录
    deleteDnsRecord: ({ domainId, recordId, ...restArgs }: any) => apiService.delete(`cloudflare/domain/${domainId}/dns_record/${recordId}`, { ...restArgs, response_mode: "websocket" }),
  },

  // 任务相关API
  task: {
    // 获取任务列表
    list: (args: any = {}) => {
      return apiService.get('tasks', args).then(response => {
        // 转换响应格式
        return {
          items: response.data || [],
          total: response.total || 0
        };
      });
    },

    // 获取单个任务详情
    get: (args: any) => {
      return apiService.get(`tasks/${args.task_id}`, { ...args, include_logs: args.include_logs || false }).then(response => {
        // 确保logs字段存在
        if (args.include_logs && !response.logs) {
          console.warn('Task info response missing logs field:', response);
          response.logs = [];
        }
        return response;
      });
    },

    // 获取任务日志
    getLogs: (args: any) => {
      return apiService.get(`tasks/${args.task_id}/logs`, args).then(response => {
        // 转换响应格式
        return {
          items: response.data || [],
          total: response.total || 0
        };
      });
    },

    // 获取任务状态
    getStatus: (args: any) => apiService.get(`tasks/${args.task_id}/status`, args),
  },

  // 其他API...
};
