import { ref, shallowRef } from 'vue';
import { useWebSocket } from '@vueuse/core';
import { useAccessStore } from '@vben/stores';

// WebSocket 状态类型
type WebSocketStatus = 'OPEN' | 'CONNECTING' | 'CLOSED';

// WebSocket 服务接口
interface WebSocketService {
  status: { value: WebSocketStatus };
  data: { value: any };
  send: (data: string | ArrayBuffer | Blob, useBuffer?: boolean) => boolean;
  sendMessage: (type: string, data: any) => void;
  open: () => void;
  close: () => void;
  addMessageListener: (eventType: string, callback: (data: any) => void) => void;
  removeMessageListener: (eventType: string, callback?: (data: any) => void) => void;
  subscribeToTopic: (topic: string) => boolean;
  unsubscribeFromTopic: (topic: string) => boolean;
}

// 单例类
class WebSocketServiceImpl implements WebSocketService {
  private static instance: WebSocketServiceImpl | null = null;
  private wsInstance: ReturnType<typeof useWebSocket> | null = null;
  private messageListeners = ref<Map<string, Set<(data: any) => void>>>(new Map());
  private initialized = false;

  // 公开的状态和数据引用
  public status = shallowRef<WebSocketStatus>('CLOSED');
  public data = ref<any>(null);

  // 私有构造函数，防止外部直接创建实例
  private constructor() {}

  // 获取单例实例
  public static getInstance(): WebSocketServiceImpl {
    if (!WebSocketServiceImpl.instance) {
      WebSocketServiceImpl.instance = new WebSocketServiceImpl();
    }
    return WebSocketServiceImpl.instance;
  }

  // 创建 WebSocket URL
  private createWebSocketUrl(): string {
    // 从 pinia store 中获取认证令牌和用户信息
    const accessStore = useAccessStore();
    const token = accessStore.accessToken;

    if (!token) {
      console.error('WebSocket连接失败：未找到认证令牌');
      return '';
    }

    // 使用固定的 WebSocket endpoint
    // const wsEndpoint = '//192.168.16.161:8092/ws';
    const wsEndpoint = '/socket.io';
    // 构建WebSocket URL，适配后端的路径格式: {endpoint}/{userid}/{token}
    return `${wsEndpoint}/${token}`;
  }

  // 初始化 WebSocket 连接
  private initWebSocket() {
    if (this.initialized) {
      console.log('WebSocket 已初始化，跳过');
      return;
    }

    // console.log('初始化 WebSocket 服务...');

    // 使用 @vueuse/core 的 useWebSocket
    this.wsInstance = useWebSocket(
      this.createWebSocketUrl(),
      {
        // 禁用自动连接，我们将手动控制连接
        immediate: false,
        autoConnect: false,
        autoReconnect: {
          retries: 10,
          delay: 5000,
          onFailed: () => {
            console.error('WebSocket connection failed after multiple retries');
          },
        },
        // 保留心跳机制，每60秒发送一次心跳包
        heartbeat: {
          message: JSON.stringify({ action: 'ping' }),
          interval: 60000, // 60秒发送一次心跳包
          responseMessage: JSON.stringify({ event: 'pong' }), // 期望的响应消息
          pongTimeout: 30000, // 30秒内没有收到pong则认为连接已断开
        },
        onMessage: (_, event) => {
          try {
            const parsedData = JSON.parse(event.data);
            // 分发消息给监听器
            if (parsedData.event) {
              this.dispatchEvent(parsedData.event, parsedData);
            }

            // 分发原始消息
            this.dispatchEvent('message', parsedData);

            // 更新数据引用
            this.data.value = parsedData;

            return parsedData;
          } catch (error) {
            console.error('websocket封包解析失败:', error);
            return event.data;
          }
        },
        onConnected: () => {
          // console.log('WebSocket连接成功');
          this.status.value = 'OPEN';

          // 自动订阅GENERAL主题
          this.subscribeToTopic('GENERAL');
        },
        onDisconnected: () => {
          // console.log('WebSocket disconnected');
          this.status.value = 'CLOSED';
        },
        onError: (_, event) => {
          console.error('Error details:', event);
          this.status.value = 'CLOSED';
        },
      }
    );

    // 同步状态
    this.status.value = this.wsInstance.status.value;

    this.initialized = true;
    // console.log('WebSocket 服务初始化完成');
  }

  // 分发事件到监听器
  private dispatchEvent(eventName: string, data: any) {
    if (this.messageListeners.value.has(eventName)) {
      const listeners = this.messageListeners.value.get(eventName);
      // console.log(`分发 ${eventName} 事件到 ${listeners?.size} 个监听器`);

      listeners?.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`执行 ${eventName} 事件监听器时出错:`, error);
        }
      });
    } else {
      console.warn(`收到未注册事件: ${eventName}`, data);
    }
  }

  // 公开方法：打开连接
  public open() {
    // 确保已初始化
    if (!this.initialized) {
      this.initWebSocket();
    }

    if (!this.wsInstance) {
      console.error('WebSocket 实例未初始化');
      return;
    }

    if (this.status.value !== 'OPEN') {
      // console.log('打开 WebSocket 连接...');
      this.wsInstance.open();
    } else {
      // console.log('WebSocket 连接已打开');
    }
  }

  // 公开方法：关闭连接
  public close() {
    if (!this.wsInstance) {
      console.warn('WebSocket 实例未初始化，无需关闭');
      return;
    }

    console.log('关闭 WebSocket 连接...');
    this.wsInstance.close();
    this.status.value = 'CLOSED';
    this.initialized = false;
  }

  // 公开方法：发送数据
  public send(data: string | ArrayBuffer | Blob, useBuffer: boolean = true): boolean {
    if (!this.wsInstance) {
      console.error('WebSocket 实例未初始化，无法发送数据');
      return false;
    }

    if (this.status.value !== 'OPEN') {
      console.warn('WebSocket 未连接，无法发送数据');
      return false;
    }

    return this.wsInstance.send(data, useBuffer);
  }

  // 公开方法：发送消息
  public sendMessage(type: string, data: any) {
    this.send(JSON.stringify({
      type,
      data,
      timestamp: Date.now(),
    }));
  }

  // 公开方法：添加消息监听器
  public addMessageListener(eventType: string, callback: (data: any) => void) {
    if (typeof callback !== 'function') {
      console.warn('注册事件监听器失败: 回调必须是一个函数');
      return;
    }

    if (!this.messageListeners.value.has(eventType)) {
      this.messageListeners.value.set(eventType, new Set());
    }

    this.messageListeners.value.get(eventType)?.add(callback);
    console.log(`已添加 ${eventType} 事件监听器，当前共有 ${this.messageListeners.value.get(eventType)?.size} 个`);
  }

  // 公开方法：移除消息监听器
  public removeMessageListener(eventType: string, callback?: (data: any) => void) {
    if (!this.messageListeners.value.has(eventType)) {
      return;
    }

    if (callback) {
      this.messageListeners.value.get(eventType)?.delete(callback);
    } else {
      this.messageListeners.value.set(eventType, new Set());
    }
  }

  // 公开方法：订阅主题
  public subscribeToTopic(topic: string): boolean {
    if (this.status.value !== 'OPEN') {
      console.warn(`WebSocket未连接，无法订阅主题 ${topic}`);
      return false;
    }

    const subscriptionRequest = {
      action: 'subscribe',
      topic: topic
    };

    return this.send(JSON.stringify(subscriptionRequest));
  }

  // 公开方法：取消订阅主题
  public unsubscribeFromTopic(topic: string): boolean {
    if (this.status.value !== 'OPEN') {
      console.warn(`WebSocket未连接，无法取消订阅主题 ${topic}`);
      return false;
    }

    const unsubscriptionRequest = {
      action: 'unsubscribe',
      topic: topic
    };

    return this.send(JSON.stringify(unsubscriptionRequest));
  }
}

// 导出单例服务
export const useWebSocketService = (): WebSocketService => {
  return WebSocketServiceImpl.getInstance();
};
