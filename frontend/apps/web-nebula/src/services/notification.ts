import { ref, onMounted } from 'vue';
import { useWebSocketService } from './websocket';
import {
  getNotifications<PERSON>pi,
  markAsRead<PERSON>pi,
  markAllAsReadApi,
  clearAllNotificationsApi,
} from '../api/notification';
import type { NotificationApi } from '../api/notification';
import type { NotificationItem } from '@vben/layouts';
import { playNotificationSound } from '../utils/sound';

// 扩展 NotificationItem 接口，添加必要字段
interface ExtendedNotificationItem extends Omit<NotificationItem, 'type'> {
  id: string;
  title: string;
  message: string;
  from: string;
  date: string;
  timestamp: number;
  isRead: boolean;
  type: string;
  event?: string;
  metadata?: any;
}

// WebSocket 通知消息格式
interface WebSocketNotification {
  event: string;
  text: string;
  id: string;
  from_source: string;
  type: string;
  title: string;
  message: string;
  metadata?: any;
}

// 格式化时间的函数
/**
 * 将时间格式化为相对时间（刚刚、n分钟前、昨天等）
 * @param timestamp 时间戳或日期字符串
 * @returns 格式化后的时间字符串
 */
function formatRelativeTime(timestamp: number | string): string {
  // 如果是字符串，尝试转换为时间戳
  let time: number;
  if (typeof timestamp === 'string') {
    // 尝试解析日期字符串
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) {
      return timestamp as string; // 如果无法解析，返回原字符串
    }
    time = date.getTime();
  } else {
    time = timestamp;
  }

  const now = Date.now();
  const diff = now - time;

  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚';
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000));
    return `${minutes}分钟前`;
  }

  // 小于24小时
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000));
    return `${hours}小时前`;
  }

  // 小于48小时
  if (diff < 48 * 60 * 60 * 1000) {
    return '昨天';
  }

  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000));
    return `${days}天前`;
  }

  // 其他情况下返回具体日期
  const date = new Date(time);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
}

// 通知列表
const notifications = ref<ExtendedNotificationItem[]>([]);

// 是否有未读通知
const hasUnread = ref(false);

// 未读通知数量
const unreadCount = ref(0);

// 是否正在加载
const loading = ref(false);

// 是否已初始化
let initialized = false;

// 通知图标元素
let notificationIconElement: HTMLElement | null = null;

// 设置通知图标元素
function setNotificationIconElement(element: HTMLElement) {
  console.log('设置通知图标元素:', element);
  notificationIconElement = element;
}

// 触发通知图标动画
function triggerNotificationAnimation() {
  console.log('触发通知图标动画');

  // 首先尝试使用已保存的引用
  let iconElement = notificationIconElement;

  // 如果没有引用，尝试使用document.querySelector获取
  if (!iconElement) {
    console.log('尝试使用querySelector获取通知图标元素');
    iconElement = document.querySelector('#notification-bell-icon');

    // 如果找到了元素，保存引用
    if (iconElement) {
      console.log('找到通知图标元素，保存引用');
      notificationIconElement = iconElement;
    }
  }

  // 如果找到了元素，应用动画
  if (iconElement) {
    console.log('应用动画到通知图标元素');

    // 移除可能存在的动画类
    iconElement.classList.remove('notification-icon-shake');

    // 触发重排以确保动画重新开始
    void iconElement.offsetWidth;

    // 添加动画类
    iconElement.classList.add('notification-icon-shake');

    // 动画结束后移除类
    setTimeout(() => {
      if (iconElement) {
        iconElement.classList.remove('notification-icon-shake');
      }
    }, 1000);
  } else {
    console.warn('未找到通知图标元素，无法应用动画');
  }
}

// 更新未读状态
function updateUnreadStatus() {
  const unreadItems = notifications.value.filter(item => !item.isRead);
  hasUnread.value = unreadItems.length > 0;
  unreadCount.value = unreadItems.length;
}

// 从API加载通知数据
async function loadNotificationsFromApi(skip = 0, limit = 50) {
  if (loading.value) return;

  loading.value = true;
  try {
    const response = await getNotificationsApi({
      skip,
      limit,
      include_read: true
    });

    // 添加调试日志
    // console.log('API 响应原始数据:', response);
    // console.log('API 响应数据类型:', typeof response);
    // console.log('API 响应数据结构:', Object.keys(response));

    // 检查 response 的结构
    let responseItems: NotificationApi.NotificationItem[] = [];

    if (response && typeof response === 'object') {
      // 检查是否是数组
      if (Array.isArray(response)) {
        // console.log('API 响应是数组');
        responseItems = response as NotificationApi.NotificationItem[];
      }
      // 检查是否有 data 字段，且 data 是数组
      else if ('data' in response && Array.isArray(response.data)) {
        // console.log('API 响应包含 data 字段，且 data 是数组');
        responseItems = response.data as NotificationApi.NotificationItem[];
      }
      // 检查是否有 items 字段，且 items 是数组
      else if ('items' in response && Array.isArray(response.items)) {
        // console.log('API 响应包含 items 字段');
        responseItems = response.items as NotificationApi.NotificationItem[];
      }
      // 检查是否有 data.items 字段，且 data.items 是数组
      else if ('data' in response && response.data && typeof response.data === 'object' && 'items' in response.data && Array.isArray(response.data.items)) {
        // console.log('API 响应包含 data.items 字段');
        responseItems = response.data.items as NotificationApi.NotificationItem[];
      }
      // 其他情况
      else {
        console.log('API 响应格式未知，尝试直接使用 response');
        console.log('完整响应:', JSON.stringify(response, null, 2));
        responseItems = [];
      }
    } else {
      console.log('API 响应不是对象，无法处理');
      responseItems = [];
    }

    // 添加更多调试日志
    // console.log('responseItems:', responseItems);

    // 检查 responseItems 是否为空
    if (!responseItems || responseItems.length === 0) {
      // console.error('responseItems 为空!');
      return [];
    }

    // 转换API响应为前端通知格式
    const items: ExtendedNotificationItem[] = responseItems.map(item => {
      // console.log('处理通知项:', item);
      return {
        id: item.id,
        title: item.title || item.message, // 使用 title 字段，如果不存在则使用 message
        message: item.message,
        from: item.from_source,
        date: formatRelativeTime(new Date(item.created_at).getTime()),
        timestamp: new Date(item.created_at).getTime(),
        isRead: item.is_read,
        type: item.type as any,
        event: item.event,
        metadata: item.metadata
      };
    });

    // 更新通知列表
    notifications.value = items;

    // 手动计算未读数量
    // console.log('手动计算未读数量');
    const unreadItems = items.filter(item => !item.isRead);
    unreadCount.value = unreadItems.length;
    hasUnread.value = unreadItems.length > 0;
    // console.log('未读数量:', unreadCount.value);

    return items;
  } catch (error) {
    console.error('加载通知数据失败:', error);
    return [];
  } finally {
    loading.value = false;
  }
}

// 添加通知
async function addNotification(notification: WebSocketNotification | Partial<NotificationItem>) {
  // console.log('添加通知:', notification);

  // 创建新的通知项
  const newNotification: ExtendedNotificationItem = {
    id: 'id' in notification ? notification.id : String(Date.now()),
    title: 'title' in notification ? notification.title : (notification.message || ''),
    message: notification.message || '',
    from: 'from_source' in notification ? notification.from_source : '系统',
    date: formatRelativeTime(new Date().getTime()),
    timestamp: new Date().getTime(),
    isRead: false,
    type: notification.type || 'info',
    event: 'event' in notification ? notification.event : undefined,
    metadata: 'metadata' in notification ? notification.metadata : null
  };

  // 添加到通知列表的开头
  notifications.value.unshift(newNotification);

  // 更新未读状态
  updateUnreadStatus();

  // 播放通知声音
  playNotificationSound();

  // 触发通知图标动画
  triggerNotificationAnimation();

  return newNotification;
}

// 标记所有为已读
async function markAllAsRead() {
  try {
    await markAllAsReadApi();

    // 更新本地通知状态
    notifications.value.forEach(item => {
      item.isRead = true;
    });

    // 更新未读状态
    hasUnread.value = false;
    unreadCount.value = 0;

    return true;
  } catch (error) {
    console.error('标记所有通知为已读失败:', error);
    return false;
  }
}

// 标记单个通知为已读
async function markAsRead(notification: ExtendedNotificationItem) {
  try {
    await markAsReadApi(notification.id);

    // 更新本地通知状态
    const index = notifications.value.findIndex(item => item.id === notification.id);
    if (index !== -1 && index < notifications.value.length) {
      notifications.value[index].isRead = true;

      // 更新未读状态
      updateUnreadStatus();
    }

    return true;
  } catch (error) {
    console.error('标记通知为已读失败:', error);
    return false;
  }
}

// 清空所有通知
async function clearAll() {
  try {
    await clearAllNotificationsApi();

    // 清空本地通知列表
    notifications.value = [];

    // 更新未读状态
    hasUnread.value = false;
    unreadCount.value = 0;

    return true;
  } catch (error) {
    console.error('清空所有通知失败:', error);
    return false;
  }
}

// 定时更新通知时间
let updateTimeInterval: ReturnType<typeof setInterval> | null = null;

/**
 * 更新所有通知的时间显示
 */
function updateAllNotificationTimes() {
  notifications.value.forEach(notification => {
    if (notification.timestamp) {
      notification.date = formatRelativeTime(notification.timestamp);
    }
  });
}

/**
 * 启动定时更新时间
 */
function startTimeUpdater() {
  if (!updateTimeInterval) {
    // 每分钟更新一次时间显示
    updateTimeInterval = setInterval(updateAllNotificationTimes, 60000);
  }
}

/**
 * 停止定时更新时间
 */
function stopTimeUpdater() {
  if (updateTimeInterval) {
    clearInterval(updateTimeInterval);
    updateTimeInterval = null;
  }
}

/**
 * 初始化WebSocket监听
 */
function lister_notification() {
  const wsService = useWebSocketService();
  // 监听通知事件
  wsService.addMessageListener('message', async (data) => {
    if (data && data.event == "message.NOTIFICATION") {
      // console.log("收到通知消息", data);

      // 直接添加通知，而不是重新加载所有通知

      // 创建通知对象
      const notification: WebSocketNotification = {
        event: data.event,
        text: data.text,
        id: data.id,
        from_source: data.from_source,
        type: data.type,
        title: data.title,
        message: data.message,
        metadata: null // 没有 metadata 字段
      };

      // 添加通知
      await addNotification(notification);
    }
  });
}

/**
 * 初始化通知服务
 */
async function initialize() {
  if (initialized) return;
  // 从API加载通知
  await loadNotificationsFromApi();
  lister_notification()

  // 初始化WebSocket监听
  // initWebSocketListeners();

  // 启动定时更新器
  startTimeUpdater();

  // 标记为已初始化
  initialized = true;

  // 在页面卸载时清除定时器
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', stopTimeUpdater);
  }
}

// 导出通知服务
export const useNotificationService = () => {
  // 在组件挂载时初始化

  return {
    notifications,
    hasUnread,
    unreadCount,
    loading,
    loadNotificationsFromApi,
    addNotification,
    markAllAsRead,
    markAsRead,
    clearAll,
    updateAllNotificationTimes,
    initialize,
    setNotificationIconElement,
    triggerNotificationAnimation
  };
};
