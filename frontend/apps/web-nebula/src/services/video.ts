import { apiService } from './api';
import { useActionService } from './action';

// 视频服务
export const videoService = {
  // 获取视频列表
  list: (args: any = {}) => apiService.request('GET', 'video', args),

  // 获取单个视频详情
  get: (args: any) => apiService.request('GET', `video/${args.id}`, args),

  // 创建视频
  create: (args: any) => apiService.request('POST', 'video', args),

  // 更新视频信息
  update: (args: any) => apiService.request('PUT', `video/${args.id}`, args),

  // 删除视频
  delete: (args: any) => apiService.request('DELETE', `video/${args.id}`, args),

  // 分析视频内容
  analyze: (args: any) => apiService.request('POST', `video/${args.id}/analyze`, args),

  // 从分享链接提取视频信息
  extract: (args: any) => {
    // 确保使用websocket异步模式
    const params = { ...args, response_mode: "websocket" };
    return apiService.request('POST', 'video/extract', params);
  },

  // 从分享链接导入视频
  importFromShare: (args: any) => {
    // 确保使用websocket异步模式
    const params = { ...args, response_mode: "websocket" };
    return apiService.request('POST', 'video/import-from-share', params);
  },

  // 更新视频标签
  updateTags: (args: any) => apiService.request('PUT', `video/${args.id}/tags`, args),

  // 添加视频标签
  addTag: (args: any) => apiService.request('POST', `video/${args.id}/tags`, args),

  // 删除视频标签
  removeTag: (args: any) => apiService.request('DELETE', `video/${args.id}/tags/${args.tag}`, args),
};

// 导出视频服务单例
export const useVideoService = () => {
  return {
    ...videoService,
    actionService: useActionService()
  };
};
