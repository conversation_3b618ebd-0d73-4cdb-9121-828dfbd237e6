import { createApp, watchEffect,watch } from 'vue';

import { registerAccessDirective } from '@vben/access';
import { registerLoadingDirective } from '@vben/common-ui/es/loading';
import { preferences } from '@vben/preferences';
import { initStores, useAccessStore, useUserStore } from '@vben/stores';
import '@vben/styles';
import '@vben/styles/antd';

// 导入应用自定义样式
import './styles';

// 导入 WebSocket 服务
import { useWebSocketService } from './services/websocket';
import { useActionService } from './services/action';

// 全局导入 Ant Design Vue 组件
import {
  Card,
  Empty,
  Button,
  Input,
  InputNumber,
  Table,
  Tag,
  Space,
  Modal,
  Radio,
  RadioGroup,
  RadioButton,
  Upload,
  Tooltip,
  InputGroup,
  Tabs,
  TabPane,
  Form,
  FormItem,
  Drawer,
  Progress,
  Select,
  Spin,
  Switch,
  Popover
} from 'ant-design-vue';

import { useTitle } from '@vueuse/core';

import { $t, setupI18n } from '#/locales';

import { initComponentAdapter } from './adapter/component';
import App from './app.vue';
import { router } from './router';

async function bootstrap(namespace: string) {
  // 初始化组件适配器
  await initComponentAdapter();

  // // 设置弹窗的默认配置
  // setDefaultModalProps({
  //   fullscreenButton: false,
  // });
  // // 设置抽屉的默认配置
  // setDefaultDrawerProps({
  //   zIndex: 1020,
  // });

  const app = createApp(App);

  // 注册v-loading指令
  registerLoadingDirective(app, {
    loading: 'loading', // 在这里可以自定义指令名称，也可以明确提供false表示不注册这个指令
    spinning: 'spinning',
  });

  // 国际化 i18n 配置
  await setupI18n(app);

  // 配置 pinia-tore
  await initStores(app, { namespace });

  // 初始化 WebSocket 服务
  // 只需要引用它，它会自动初始化单例
  const wsService = useWebSocketService();

  // 初始化 Action 服务（不使用变量接收，避免未使用警告）
  useActionService();

  // 通过监视accessToken的变化来控制WebSocket的连接状态
  const accessStore = useAccessStore();
  watch(() => accessStore.accessToken, (token) => {
    console.log("token status:",Boolean(token));
    if (token) wsService.open();
    else wsService.close();

  },{ immediate: true })

  // 安装权限指令
  registerAccessDirective(app);

  // 初始化 tippy
  const { initTippy } = await import('@vben/common-ui/es/tippy');
  initTippy(app);

  // 全局注册 Ant Design Vue 组件
  app.component('a-card', Card);
  app.component('a-empty', Empty);
  app.component('a-button', Button);
  app.component('a-input', Input);
  app.component('a-input-number', InputNumber);
  app.component('a-table', Table);
  app.component('a-tag', Tag);
  app.component('a-space', Space);
  app.component('a-modal', Modal);
  app.component('a-radio', Radio);
  app.component('a-radio-group', RadioGroup);
  app.component('a-radio-button', RadioButton);
  app.component('a-upload', Upload);
  app.component('a-tooltip', Tooltip);
  app.component('a-input-group', InputGroup);
  app.component('a-tabs', Tabs);
  app.component('a-tab-pane', TabPane);
  app.component('a-form', Form);
  app.component('a-form-item', FormItem);
  app.component('a-drawer', Drawer);
  app.component('a-progress', Progress);
  app.component('a-select', Select);
  app.component('a-select-option', Select.Option);
  app.component('a-spin', Spin);
  app.component('a-switch', Switch);
  app.component('a-popover', Popover);

  // 配置路由及路由守卫
  app.use(router);

  // 配置Motion插件
  const { MotionPlugin } = await import('@vben/plugins/motion');
  app.use(MotionPlugin);

  // 动态更新标题
  watchEffect(() => {
    if (preferences.app.dynamicTitle) {
      const routeTitle = router.currentRoute.value.meta?.title;
      const pageTitle =
        (routeTitle ? `${$t(routeTitle)} - ` : '') + preferences.app.name;
      useTitle(pageTitle);
    }
  });

  app.mount('#app');
}

export { bootstrap };
