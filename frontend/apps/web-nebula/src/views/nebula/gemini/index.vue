<template>
  <Flex>
    <ViewContainer variant="page">
      <template #header>
        <ViewHeader>
          <template #title>
            <span>Gemini AI 助手</span>
          </template>
          <template #actions>
            <div class="card-actions">
              <Switch
                v-model:checked="chatState.useStreamResponse"
                size="small"
                :checkedChildren="'流式'"
                :unCheckedChildren="'普通'"
              />
              <Switch
                v-model:checked="useHistory"
                size="small"
                :checkedChildren="'上下文'"
                :unCheckedChildren="'单次'"
              />
              <Button
                type="text"
                size="small"
                class="format-button"
                @click="showResponseFormatModal = true"
              >
                <template #icon>
                  <FormatOutlined />
                </template>
                响应格式: {{ currentResponseFormat }}
              </Button>
              <Button
                type="text"
                size="small"
                class="prompt-button"
                @click="showPromptModal = true"
              >
                <template #icon>
                  <SettingOutlined />
                </template>
                提示词: {{ currentPrompt }}
              </Button>
            </div>
          </template>
        </ViewHeader>
      </template>

      <template #body>
        <ChatContainer :messages="chatState.messages" :isTyping="isTyping" />
      </template>

      <template #footer>
        <ChatInput
          :loading="chatState.loading"
          @send="handleSendMessage"
          @sendFile="handleSendFile"
        />
      </template>
    </ViewContainer>

    <!-- Prompt设置对话框 -->
    <Modal
      v-model:open="showPromptModal"
      title="Prompt设置"
      @ok="showPromptModal = false"
      @cancel="showPromptModal = false"
    >
      <div class="prompt-editor">
        <p>选择预设Prompt</p>
        <Radio.Group v-model:value="selectedPreset" @change="(e) => selectPreset(e.target.value)">
          <Space direction="vertical">
            <Radio v-for="(preset, index) in presetPrompts" :key="index" :value="index.toString()">
              {{ preset.label }}
            </Radio>
          </Space>
        </Radio.Group>

        <Divider />

        <p>自定义Prompt（系统提示词）</p>
        <Input.TextArea
          v-model:value="customPrompt"
          :rows="6"
          placeholder="请输入自定义Prompt"
        />
        <div class="prompt-tips">
          <p>提示：</p>
          <ul>
            <li>Prompt是发送给AI的系统指令，用于控制AI的回复风格和内容。</li>
            <li>您可以选择预设Prompt，也可以自定义Prompt。</li>
            <li>自定义的Prompt会自动保存，下次打开时自动加载。</li>
          </ul>
        </div>
      </div>
    </Modal>

    <!-- 响应格式设置对话框 -->
    <Modal
      v-model:open="showResponseFormatModal"
      title="响应格式设置"
      @ok="showResponseFormatModal = false"
      @cancel="showResponseFormatModal = false"
    >
      <div class="format-editor">
        <p>选择预设格式</p>
        <Radio.Group v-model:value="selectedResponseFormat" @change="(e) => selectResponseFormat(e.target.value)">
          <Space direction="vertical">
            <Radio v-for="(preset, index) in presetResponseFormats" :key="index" :value="index.toString()">
              {{ preset.label }}
            </Radio>
          </Space>
        </Radio.Group>

        <Divider />

        <p>自定义格式（JSON格式）</p>
        <Input.TextArea
          v-model:value="responseFormat"
          :rows="6"
          placeholder='{"title": "标题", "content": "内容"}'
          :disabled="selectedResponseFormat === '0'"
        />
        <div class="format-tips">
          <p>提示：</p>
          <ul>
            <li v-if="selectedResponseFormat === '0'">选择"非结构化"时，不需要设置自定义格式。</li>
            <li v-else>格式必须是有效的JSON对象。</li>
            <li>您可以选择预设格式，也可以自定义格式。</li>
            <li>自定义的格式会自动保存，下次打开时自动加载。</li>
          </ul>
        </div>
      </div>
    </Modal>
  </Flex>
</template>

<script lang="ts" setup>
import { reactive, onMounted, computed, ref, h, watch } from 'vue';
import { Switch, Input, Button, Modal, Radio, Space, Divider, message as antMessage } from 'ant-design-vue';
import ViewContainer from '../../../components/ViewContainer.vue';
import ViewHeader from '../../../components/ViewHeader.vue';

// 自定义设置图标
const SettingOutlined = () => h('svg', {
  viewBox: '0 0 24 24',
  width: '1em',
  height: '1em',
  fill: 'currentColor',
}, [
  h('path', {
    d: 'M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.***********.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.***********.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z'
  })
]);

// 自定义格式图标
const FormatOutlined = () => h('svg', {
  viewBox: '0 0 24 24',
  width: '1em',
  height: '1em',
  fill: 'currentColor',
}, [
  h('path', {
    d: 'M3 21h18v-2H3v2zm0-4h18v-2H3v2zm0-4h18v-2H3v2zm0-4h18V7H3v2zm0-6v2h18V3H3z'
  })
]);
import { useUserStore } from '@vben/stores';
import { apiService } from '#/services/api';
import { useActionService } from '#/services/action';

// 导入组件和类型
import type { ChatMessage, ChatState } from './components/types';
import { MessageType } from './components/types';
import ChatContainer from './components/ChatContainer.vue';
import ChatInput from './components/ChatInput.vue';

// 获取用户信息
const userStore = useUserStore();
const actionService = useActionService();

// 聊天状态
const chatState = reactive<ChatState>({
  messages: [],
  loading: false,
  useStreamResponse: true
});

// 打字效果相关状态
const typingBuffer = ref(''); // 打字缓冲区
const isTyping = ref(false); // 是否正在打字

// 根据缓冲区剩余大小计算打字速度和字符数量
const calculateTypingParams = (remainingChars: number) => {
  // 缓冲区大小阈值
  const BUFFER_THRESHOLD_LARGE = 1000; // 大缓冲区阈值
  const BUFFER_THRESHOLD_MEDIUM = 500; // 中等缓冲区阈值
  const BUFFER_THRESHOLD_SMALL = 100; // 小缓冲区阈值

  // 根据缓冲区大小计算打字速度和字符数量
  if (remainingChars > BUFFER_THRESHOLD_LARGE) {
    // 大缓冲区：快速打字，多字符
    return { speed: 10, charsPerUpdate: 10 };
  } else if (remainingChars > BUFFER_THRESHOLD_MEDIUM) {
    // 中等缓冲区：中速打字，适中字符
    return { speed: 20, charsPerUpdate: 5 };
  } else if (remainingChars > BUFFER_THRESHOLD_SMALL) {
    // 小缓冲区：正常速度，正常字符
    return { speed: 40, charsPerUpdate: 3 };
  } else {
    // 很小缓冲区：慢速打字，少字符，提供更自然的打字效果
    return { speed: 50, charsPerUpdate: 1 };
  }
};

// 预设结构化响应格式数组
const presetResponseFormats = [
  {
    label: "非结构化",
    value: ""
  },
  {
    label: "视频格式",
    value: '{"title": "标题", "overview": "概述", "highlights": ["要点1", "要点2"], "location": "位置", "category": "分类", "sub_category": "子分类", "keywords": ["关键词1", "关键词2"], "subtitles_chunks": ["字幕1", "字幕2"]}'
  },
  {
    label: "问答格式",
    value: '{"question": "问题", "answer": "回答"}'
  },
  {
    label: "列表格式",
    value: '{"title": "标题", "items": ["项目1", "项目2", "项目3"]}'
  },
  {
    label: "表格格式",
    value: '{"headers": ["列1", "列2"], "rows": [["行1列1", "行1列2"], ["行2列1", "行2列2"]]}'
  },
  {
    label: "详细信息",
    value: '{"title": "标题", "summary": "摘要", "details": "详细信息", "conclusion": "结论"}'
  }
];

// 结构化响应相关状态
const RESPONSE_FORMAT_KEY = 'nebula-gemini-response-format';
const SELECTED_RESPONSE_FORMAT_KEY = 'nebula-gemini-selected-response-format';

const selectedResponseFormat = ref(localStorage.getItem(SELECTED_RESPONSE_FORMAT_KEY) || "0"); // 默认选择第一个预设
const responseFormat = ref(localStorage.getItem(RESPONSE_FORMAT_KEY) || presetResponseFormats[0].value);
const showResponseFormatModal = ref(false);

// 根据选择的预设格式设置useStructuredResponse的值
const useStructuredResponse = ref(selectedResponseFormat.value !== "0");

// 监听responseFormat变化，保存到localStorage
watch(responseFormat, (newValue) => {
  localStorage.setItem(RESPONSE_FORMAT_KEY, newValue);
});

// 监听selectedResponseFormat变化，保存到localStorage
watch(selectedResponseFormat, (newValue) => {
  localStorage.setItem(SELECTED_RESPONSE_FORMAT_KEY, newValue);
});

// 选择预设responseFormat
const selectResponseFormat = (index: string) => {
  selectedResponseFormat.value = index;
  responseFormat.value = presetResponseFormats[Number(index)].value;

  // 如果选择的是"非结构化"选项，关闭结构化响应
  useStructuredResponse.value = index !== "0";
};

// 上下文相关状态
const useHistory = ref(true);
const historyMessageCount = ref(10); // 默认包含10条历史消息

// 预设prompt数组
const presetPrompts = [
  {
    label: "默认",
    value: "请以友好、专业的口吻回答用户的问题。如果不确定答案，请诚实地表明。"
  },
  {
    label: "简洁模式",
    value: "请用简洁明了的语言回答问题，尽量减少不必要的解释。"
  },
  {
    label: "详细模式",
    value: "请提供详细的解释和示例，确保回答全面且易于理解。"
  },
  {
    label: "技术专家",
    value: "请以技术专家的身份回答问题，提供准确的技术细节和专业建议。"
  },
  {
    label: "创意模式",
    value: "请以创意的方式回答问题，提供新颖的想法和独特的视角。"
  },
  {
    label: "视频专家",
    value: `请以专业、客观的方式分析视频。
    用"中文"生成以下内容：
    一个简洁且描述性强的标题，字数不超过80个字
    一段客观的内容概述（800字以内）,请勿以"本视频"/"该视频"/这种第三方视角描述
    3-5个相关的关键词标签
    3-5个核心摘要或关键亮点
    输出不包含时间戳的字幕: 如果非中文,请翻译成中文(注意:每3~5句话合并成一个chunk，这样既不会过短影响语义理解，也不会过长影响向量检索性能)`
  }
];

// prompt相关状态
const STORAGE_KEY = 'nebula-gemini-prompt';
const SELECTED_PRESET_KEY = 'nebula-gemini-selected-preset';

// 从localStorage读取自定义prompt，如果没有则使用默认值
const customPrompt = ref(localStorage.getItem(STORAGE_KEY) || presetPrompts[0].value);
const selectedPreset = ref(localStorage.getItem(SELECTED_PRESET_KEY) || "0"); // 默认选择第一个预设
const showPromptModal = ref(false);

// 监听customPrompt变化，保存到localStorage
watch(customPrompt, (newValue) => {
  localStorage.setItem(STORAGE_KEY, newValue);
});

// 监听selectedPreset变化，保存到localStorage
watch(selectedPreset, (newValue) => {
  localStorage.setItem(SELECTED_PRESET_KEY, newValue);
});

// 选择预设prompt
const selectPreset = (index: string) => {
  selectedPreset.value = index;
  customPrompt.value = presetPrompts[Number(index)].value;
};

// 当前选定的响应格式
const currentResponseFormat = computed(() => {
  return presetResponseFormats[Number(selectedResponseFormat.value)].label;
});

// 当前选定的提示词
const currentPrompt = computed(() => {
  return presetPrompts[Number(selectedPreset.value)].label;
});

// 用户头像
const userAvatar = computed(() => {
  return userStore.userInfo?.avatar || 'https://avatar.vercel.sh/user.svg?text=User';
});

// AI头像
const aiAvatar = 'https://avatar.vercel.sh/ai.svg?text=AI';

// 添加一条欢迎消息
onMounted(() => {
  const welcomeMessage: ChatMessage = {
    id: '1',
    content: '您好！我是 Gemini AI 助手，有什么可以帮助您的？',
    timestamp: new Date().toLocaleTimeString(),
    sender: 'ai',
    avatar: aiAvatar
  };
  chatState.messages.push(welcomeMessage);
});

// 处理发送文本消息
const handleSendMessage = async (message: string) => {
  // 添加用户消息
  const userMessage: ChatMessage = {
    id: Date.now().toString(),
    content: message,
    timestamp: new Date().toLocaleTimeString(),
    sender: 'user',
    avatar: userAvatar.value
  };

  chatState.messages.push(userMessage);

  // 设置加载状态
  chatState.loading = true;

  // 创建 AI 消息占位符
  const aiMessageId = Date.now() + 1;
  const aiMessage: ChatMessage = {
    id: aiMessageId.toString(),
    content: '思考中...',
    timestamp: new Date().toLocaleTimeString(),
    sender: 'ai',
    avatar: aiAvatar
  };
  chatState.messages.push(aiMessage);

  try {
    if (chatState.useStreamResponse) {
      // 使用流式响应
      await handleStreamResponse(message, aiMessageId.toString());
    } else {
      // 使用普通响应
      await handleNormalResponse(message, aiMessageId.toString());
    }
  } catch (error) {
    console.error('生成回复失败:', error);
    antMessage.error('生成回复失败，请稍后再试');

    // 更新错误消息
    const aiMessageIndex = chatState.messages.findIndex(msg => msg.id === aiMessageId.toString());
    if (aiMessageIndex !== -1 && chatState.messages[aiMessageIndex]) {
      chatState.messages[aiMessageIndex].content = "抱歉，我遇到了一些问题，无法生成回复。请稍后再试。";
      chatState.messages[aiMessageIndex].type = MessageType.ERROR;
    }
  } finally {
    chatState.loading = false;
  }
};

// 处理发送文件消息
const handleSendFile = async (fileInfo: any, message: string) => {
  // 设置加载状态
  chatState.loading = true;

  try {
    // 使用已上传的文件信息
    const fileUrl = fileInfo.fileUrl;

    // 添加用户消息（包含文件）
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: message || customPrompt.value || '请分析这个文件',
      timestamp: new Date().toLocaleTimeString(),
      sender: 'user',
      avatar: userAvatar.value,
      fileUrl: fileInfo.fileUrl,
      fileType: fileInfo.fileType,
      fileName: fileInfo.fileName,
      fileSize: fileInfo.fileSize
    };

    chatState.messages.push(userMessage);

    // 创建 AI 消息占位符
    const aiMessageId = Date.now() + 1;
    const aiMessage: ChatMessage = {
      id: aiMessageId.toString(),
      content: '思考中...',
      timestamp: new Date().toLocaleTimeString(),
      sender: 'ai',
      avatar: aiAvatar
    };
    chatState.messages.push(aiMessage);

    // 根据是否使用流式响应选择不同的处理方式
    if (chatState.useStreamResponse) {
      // 使用流式响应
      await handleStreamFileResponse(fileUrl, message, aiMessageId.toString());
    } else {
      // 使用普通响应
      await handleNormalFileResponse(fileUrl, message, aiMessageId.toString());
    }
  } catch (error) {
    console.error('文件处理失败:', error);
    antMessage.error('文件处理失败，请稍后再试');

    // 如果已经添加了 AI 消息占位符，则更新错误消息
    const aiMessageId = Date.now() + 1;
    const aiMessageIndex = chatState.messages.findIndex(msg => msg.id === aiMessageId.toString());
    if (aiMessageIndex !== -1 && chatState.messages[aiMessageIndex]) {
      chatState.messages[aiMessageIndex].content = error || "抱歉，我遇到了一些问题，无法处理这个文件。请稍后再试。";
      chatState.messages[aiMessageIndex].type = MessageType.ERROR;
    } else {
      // 如果还没有添加 AI 消息占位符，则添加一个错误消息
      chatState.messages.push({
        id: aiMessageId.toString(),
        content: error || "抱歉，我遇到了一些问题，无法处理这个文件。请稍后再试。",
        timestamp: new Date().toLocaleTimeString(),
        sender: 'ai',
        avatar: aiAvatar,
        type: MessageType.ERROR
      });
    }
  } finally {
    chatState.loading = false;
  }
};

// 提取历史消息作为上下文
const getHistoryMessages = () => {
  if (!useHistory.value) {
    return []; // 如果不使用上下文，返回空数组
  }

  // 获取最近的消息，但不包括最新的用户消息和AI回复
  const recentMessages = chatState.messages
    .slice(0, -2) // 排除最新的用户消息和AI回复
    .slice(-historyMessageCount.value) // 只取最近的historyMessageCount条消息
    .map(msg => ({
      role: msg.sender === 'user' ? 'user' : 'model',
      content: msg.content
    }));

  return recentMessages;
};

// 处理普通响应
const handleNormalResponse = async (userInput: string, messageId: string) => {
  // 准备请求参数
  const params: any = {
    text: userInput,
    prompt: customPrompt.value, // 使用自定义prompt
    history: getHistoryMessages() // 添加历史消息作为上下文
  };

  // 如果使用结构化响应，添加相关参数
  if (useStructuredResponse.value) {
    params.use_structured_response = true;
    try {
      params.response_format = JSON.parse(responseFormat.value);
    } catch (e) {
      console.error('解析响应格式失败:', e);
      antMessage.error('响应格式必须是有效的JSON');
      return;
    }
  }

  // 调用 Gemini API
  const response = await actionService.submit(
    apiService.gemini.generateText,
    {
      body: params
    },
    {
      loading: false, // 使用组件自己的 loading 状态
      showSubmitNotify: false
    }
  );

  // 更新 AI 回复
  const aiMessageIndex = chatState.messages.findIndex(msg => msg.id === messageId);
  if (aiMessageIndex !== -1 && chatState.messages[aiMessageIndex]) {
    if (response.content) {
      chatState.messages[aiMessageIndex].content = response.content;
    } else {
      chatState.messages[aiMessageIndex].content = "抱歉，我无法生成回复。";
      chatState.messages[aiMessageIndex].type = MessageType.ERROR;
    }
  }
};

// 打字效果函数
const typeEffect = (messageIndex: number) => {
  // 如果消息索引无效，直接返回
  if (messageIndex === -1 || !chatState.messages[messageIndex]) {
    isTyping.value = false;
    return;
  }

  // 获取当前已显示的内容和缓冲区内容
  const currentContent = chatState.messages[messageIndex].content;
  const bufferContent = typingBuffer.value;

  // 计算剩余字符数
  const remainingChars = bufferContent.length - currentContent.length;

  // 如果当前显示的内容长度小于缓冲区内容长度，继续打字
  if (remainingChars > 0) {
    // 根据剩余缓冲区大小计算打字参数
    const { speed, charsPerUpdate } = calculateTypingParams(remainingChars);

    // 计算下一次要显示的内容长度，但不超过缓冲区总长度
    const nextLength = Math.min(currentContent.length + charsPerUpdate, bufferContent.length);

    // 更新显示内容
    chatState.messages[messageIndex].content = bufferContent.substring(0, nextLength);

    // 继续打字，使用动态计算的速度
    setTimeout(() => typeEffect(messageIndex), speed);
  } else {
    // 打字完成
    isTyping.value = false;
  }
};

// 处理流式响应（文本）
const handleStreamResponse = async (userInput: string, messageId: string) => {
  try {
    // 准备请求参数
    const params: any = {
      text: userInput,
      prompt: customPrompt.value, // 使用自定义prompt
      history: getHistoryMessages() // 添加历史消息作为上下文
    };

    // 如果使用结构化响应，添加相关参数
    if (useStructuredResponse.value) {
      params.use_structured_response = true;
      try {
        params.response_format = JSON.parse(responseFormat.value);
      } catch (e) {
        console.error('解析响应格式失败:', e);
        antMessage.error('响应格式必须是有效的JSON');
        return;
      }
    }

    // 调用流式 API
    const response = await apiService.gemini.streamGenerateText({
      body: params
    });

    // 检查响应状态
    if (response.status !== 200) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 获取响应数据流
    const reader = new ReadableStreamDefaultReader(response.data);

    // 查找消息索引
    const aiMessageIndex = chatState.messages.findIndex(msg => msg.id === messageId);
    if (aiMessageIndex === -1) {
      throw new Error('找不到消息');
    }

    // 重置打字状态
    typingBuffer.value = '';
    isTyping.value = false;

    // 读取流
    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        break;
      }

      // 解码文本
      const chunk = new TextDecoder().decode(value);

      // 处理每一行（每个 JSON 对象）
      const lines = chunk.split('\n').filter(line => line.trim());

      for (const line of lines) {
        try {
          const data = JSON.parse(line);
          if (data.content) {
            // 将内容添加到缓冲区
            typingBuffer.value += data.content;

            // 如果没有正在打字，开始打字效果
            if (!isTyping.value) {
              isTyping.value = true;
              typeEffect(aiMessageIndex);
            }
          }
        } catch (e) {
          console.error('解析流数据失败:', e);
        }
      }
    }

    // 如果没有收到任何内容
    if (!typingBuffer.value && aiMessageIndex !== -1 && chatState.messages[aiMessageIndex]) {
      chatState.messages[aiMessageIndex].content = "抱歉，我无法生成回复。";
      chatState.messages[aiMessageIndex].type = MessageType.ERROR;
    }

  } catch (error) {
    console.error('流式生成失败:', error);
    throw error;
  }
};

// 处理流式响应（文件）
const handleStreamFileResponse = async (fileUrl: string, userMessage: string, messageId: string) => {
  let errormsg = "";
  try {
    // 准备请求参数
    const params: any = {
      file_uri: fileUrl,
      prompt: userMessage || customPrompt.value, // 使用自定义prompt
      history: getHistoryMessages() // 添加历史消息作为上下文
    };

    // 如果使用结构化响应，添加相关参数
    if (useStructuredResponse.value) {
      params.use_structured_response = true;
      try {
        params.response_format = JSON.parse(responseFormat.value);
      } catch (e) {
        console.error('解析响应格式失败:', e);
        antMessage.error('响应格式必须是有效的JSON');
        return;
      }
    }

    // 调用流式 API
    const response = await apiService.gemini.streamGenerateFile({
      body: params
    });

    // 检查响应状态
    if (response.status !== 200) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 获取响应数据流
    const reader = new ReadableStreamDefaultReader(response.data);

    // 查找消息索引
    const aiMessageIndex = chatState.messages.findIndex(msg => msg.id === messageId);
    if (aiMessageIndex === -1) {
      throw new Error('找不到消息');
    }

    // 重置打字状态
    typingBuffer.value = '';
    isTyping.value = false;

    // 读取流
    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        break;
      }

      // 解码文本
      const chunk = new TextDecoder().decode(value);

      // 处理每一行（每个 JSON 对象）
      const lines = chunk.split('\n').filter(line => line.trim());

      for (const line of lines) {
        try {
          const data = JSON.parse(line);
          if (data.content) {
            // 将内容添加到缓冲区
            typingBuffer.value += data.content;

            // 如果没有正在打字，开始打字效果
            if (!isTyping.value) {
              isTyping.value = true;
              typeEffect(aiMessageIndex);
            }
          }
          else{
            errormsg = data.msg;
          }
        } catch (e) {
          console.error('解析流数据失败:', e);
        }
      }
    }

    // 如果没有收到任何内容
    if (!typingBuffer.value && aiMessageIndex !== -1 && chatState.messages[aiMessageIndex]) {
      chatState.messages[aiMessageIndex].content = errormsg || "抱歉，我无法分析这个文件。";
      chatState.messages[aiMessageIndex].type = MessageType.ERROR;
    }

  } catch (error) {
    console.error('流式文件生成失败:', error);
    throw error;
  }
};

// 处理普通响应（文件）
const handleNormalFileResponse = async (fileUrl: string, userMessage: string, messageId: string) => {
  // 准备请求参数
  const params: any = {
    file_uri: fileUrl,
    prompt: userMessage || customPrompt.value, // 使用自定义prompt
    history: getHistoryMessages() // 添加历史消息作为上下文
  };

  // 如果使用结构化响应，添加相关参数
  if (useStructuredResponse.value) {
    params.use_structured_response = true;
    try {
      params.response_format = JSON.parse(responseFormat.value);
    } catch (e) {
      console.error('解析响应格式失败:', e);
      antMessage.error('响应格式必须是有效的JSON');
      return;
    }
  }

  // 调用 Gemini API
  const response = await actionService.submit(
    apiService.gemini.generateFile,
    {
      body: params
    },
    {
      loading: false, // 使用组件自己的 loading 状态
      showSubmitNotify: false
    }
  );

  // 更新 AI 回复
  const aiMessageIndex = chatState.messages.findIndex(msg => msg.id === messageId);
  if (aiMessageIndex !== -1 && chatState.messages[aiMessageIndex]) {
    if (response.content) {
      chatState.messages[aiMessageIndex].content = response.content;
    } else {
      chatState.messages[aiMessageIndex].content = "抱歉，我无法分析这个文件。";
      chatState.messages[aiMessageIndex].type = MessageType.ERROR;
    }
  }
};
</script>

<style lang="less" scoped>
.card-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-container {
  height: 100%;
}

.response-format-editor {
  margin-top: 8px;
  margin-bottom: 8px;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
}

.prompt-editor {
  padding: 0 16px;
}

.prompt-tips {
  margin-top: 16px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.format-editor {
  padding: 0 16px;
}

.format-tips {
  margin-top: 16px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.card-actions .prompt-button,
.card-actions .format-button {
  margin-left: 8px;
}
</style>
