// 文件类型枚举
export enum FileType {
  IMAGE = 'image',
  DOCUMENT = 'document',
  VIDEO = 'video',
  AUDIO = 'audio',
  OTHER = 'other'
}

// 消息类型枚举
export enum MessageType {
  NORMAL = 'normal',
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info'
}

// 消息类型
export interface ChatMessage {
  id: string;
  content: string;
  timestamp: string;
  sender: 'user' | 'ai';
  avatar: string;
  // 消息类型，默认为普通消息
  type?: MessageType;
  // 文件相关字段，可选
  fileUrl?: string;
  fileType?: FileType;
  fileName?: string;
  fileSize?: number;
}

// 聊天状态
export interface ChatState {
  messages: ChatMessage[];
  loading: boolean;
  useStreamResponse: boolean;
  // 当前选择的文件
  selectedFile?: File | null;
}
