<template>
  <div class="chat-input-container">
    <!-- 文件预览区域 -->
    <div v-if="selectedFile || uploadedFileInfo" class="file-preview">
      <div class="file-info">
        <div class="file-icon">
          <FileImageIcon v-if="isImageFile || (uploadedFileInfo && uploadedFileInfo.fileType === FileType.IMAGE)" />
          <FileTextIcon v-else-if="isDocumentFile || (uploadedFileInfo && uploadedFileInfo.fileType === FileType.DOCUMENT)" />
          <VideoIcon v-else-if="isVideoFile || (uploadedFileInfo && uploadedFileInfo.fileType === FileType.VIDEO)" />
          <FileIcon v-else />
        </div>
        <div class="file-details">
          <div class="file-name">{{ selectedFile ? selectedFile.name : (uploadedFileInfo ? uploadedFileInfo.fileName : '') }}</div>
          <div class="file-size">
            {{ selectedFile ? formatFileSize(selectedFile.size) : (uploadedFileInfo ? formatFileSize(uploadedFileInfo.fileSize) : '0 B') }}
            <span v-if="uploading" class="upload-status">上传中...</span>
            <span v-else-if="uploadedFileInfo" class="upload-status success">已上传</span>
          </div>
        </div>
      </div>
      <Button type="text" :disabled="uploading" @click="clearSelectedFile">
        <CloseIcon />
      </Button>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input">
      <div class="textarea-wrapper">
        <Input.TextArea
          v-model:value="inputMessage"
          placeholder="输入消息...（按 Ctrl+Enter 或 Shift+Enter 发送）"
          :disabled="loading"
          :rows="2"
          :autoSize="{ minRows: 2, maxRows: 5 }"
          @keydown="handleKeyDown"
        />
      </div>

      <!-- 文件上传按钮 -->
      <Popover
        v-model:open="showFilePopover"
        title="添加文件"
        trigger="click"
        placement="topRight"
        overlayClassName="file-popover"
      >
        <template #content>
          <div class="file-popover-content">
            <div class="file-url-input">
              <Input
                v-model:value="fileUrl"
                placeholder="输入文件链接"
                @pressEnter="handleFileUrl"
              />
              <Button type="primary" size="small" @click="handleFileUrl">确定</Button>
            </div>
            <Divider>或</Divider>
            <Upload
              :showUploadList="false"
              :beforeUpload="beforeUpload"
              :customRequest="customUpload"
              accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.mp4,.mp3,.webp,.ogg,.wav,.xlsx,.pptx"
              :drag="true"
              class="full-width-upload"
            >
              <div class="upload-drag-area">
                <p class="upload-drag-icon">
                  <UploadIcon />
                </p>
                <p class="upload-drag-text">点击或拖拽文件到此区域上传</p>
                <p class="upload-tip">支持多种类型文件，单个文件最大 1GB</p>
              </div>
            </Upload>
          </div>
        </template>
        <Button :disabled="loading || !!selectedFile || uploading">
          <PaperClipIcon />
        </Button>
      </Popover>

      <!-- 发送按钮 -->
      <Button
        type="primary"
        class="send-button"
        :loading="loading || uploading"
        :disabled="uploading"
        @click="sendMessage"
      >
        <SendIcon />
      </Button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { Input, Button, Upload, Popover, Divider, message } from 'ant-design-vue';
import { SendIcon, FileIcon, FileImageIcon, FileTextIcon, VideoIcon, PaperClipIcon, CloseIcon, UploadIcon } from '@vben/icons';
import { FileType } from './types';
import { uploadUtils } from '#/utils/upload';

// 定义组件属性和事件
const props = defineProps<{
  loading: boolean;
}>();

const emit = defineEmits<{
  (e: 'send', message: string): void;
  (e: 'sendFile', fileInfo: any, message: string): void;
}>();

const inputMessage = ref('');
const selectedFile = ref<File | null>(null);
const uploading = ref(false);
const uploadedFileInfo = ref<any>(null);

// 文件链接相关状态
const fileUrl = ref('');
const showFilePopover = ref(false);

// 文件类型判断
const isImageFile = computed(() => {
  if (!selectedFile.value) return false;
  return /\.(jpg|jpeg|png|gif|webp)$/i.test(selectedFile.value.name);
});

const isVideoFile = computed(() => {
  if (!selectedFile.value) return false;
  return /\.(mp4|webm|ogg|mov)$/i.test(selectedFile.value.name);
});

const isDocumentFile = computed(() => {
  if (!selectedFile.value) return false;
  return /\.(pdf|doc|docx|txt|xls|xlsx|ppt|pptx)$/i.test(selectedFile.value.name);
});

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }
};

// 处理文件选择前的验证
const beforeUpload = (file: File) => {
  // 文件大小限制（1000MB）
  const maxSize = 1000 * 1024 * 1024;
  if (file.size > maxSize) {
    message.error('文件大小不能超过1GB');
    return Upload.LIST_IGNORE;
  }

  // 允许的文件类型
  const allowedTypes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'video/mp4', 'video/webm', 'video/ogg', 'audio/mpeg', 'audio/ogg', 'audio/wav'
  ];

  if (!allowedTypes.includes(file.type)) {
    message.error('不支持的文件类型');
    return Upload.LIST_IGNORE;
  }

  return true;
};

// 自定义上传
const customUpload = async (options: any) => {
  const { file, onSuccess, onError, onProgress } = options;

  // 设置上传中状态
  uploading.value = true;
  selectedFile.value = file;
  uploadedFileInfo.value = null;

  try {
    // 使用uploadUtils处理上传
    const result = await uploadUtils.process(file, {
      bucketName: 'nebula',
      loading: uploading
    });

    if (result && result.object_name) {
      // 保存上传成功的文件信息
      uploadedFileInfo.value = {
        fileUrl: result.url,
        fileName: file.name,
        fileType: getFileType(file),
        fileSize: file.size
      };

      onSuccess(result, file);
      showFilePopover.value = false; // 关闭气泡
    } else {
      onError(new Error('上传失败'));
      clearSelectedFile();
    }
  } catch (error) {
    console.error('上传文件失败:', error);
    onError(error);
    message.error('文件上传失败');
    clearSelectedFile();
  } finally {
    uploading.value = false;
  }
};

// 获取文件类型
const getFileType = (file: File): FileType => {
  if (/\.(jpg|jpeg|png|gif|webp)$/i.test(file.name)) {
    return FileType.IMAGE;
  } else if (/\.(mp4|webm|ogg|mov)$/i.test(file.name)) {
    return FileType.VIDEO;
  } else if (/\.(pdf|doc|docx|txt|xls|xlsx|ppt|pptx)$/i.test(file.name)) {
    return FileType.DOCUMENT;
  } else if (/\.(mp3|wav|ogg|m4a)$/i.test(file.name)) {
    return FileType.AUDIO;
  } else {
    return FileType.OTHER;
  }
};

// 从URL获取文件类型
const getFileTypeFromUrl = (url: string): FileType => {
  if (/\.(jpg|jpeg|png|gif|webp)$/i.test(url)) {
    return FileType.IMAGE;
  } else if (/\.(mp4|webm|ogg|mov)$/i.test(url)) {
    return FileType.VIDEO;
  } else if (/\.(pdf|doc|docx|txt|xls|xlsx|ppt|pptx)$/i.test(url)) {
    return FileType.DOCUMENT;
  } else if (/\.(mp3|wav|ogg|m4a)$/i.test(url)) {
    return FileType.AUDIO;
  } else {
    return FileType.OTHER;
  }
};

// 处理文件链接
const handleFileUrl = async () => {
  if (!fileUrl.value.trim()) {
    message.error('请输入文件链接');
    return;
  }

  // 设置上传中状态
  uploading.value = true;

  try {
    // 尝试解析URL
    let fileName = '';
    try {
      const url = new URL(fileUrl.value);
      // 尝试从查询参数中获取文件名
      const nameFromQuery = url.searchParams.get('filename') || url.searchParams.get('name');
      if (nameFromQuery) {
        fileName = decodeURIComponent(nameFromQuery);
      } else {
        // 如果查询参数中没有文件名，则从路径中提取
        fileName = url.pathname.split('/').pop() || '未知文件';
      }
    } catch (e) {
      // 如果URL解析失败，则使用原来的逻辑
      fileName = fileUrl.value.split('/').pop() || '未知文件';
    }

    // 创建文件信息
    uploadedFileInfo.value = {
      fileUrl: fileUrl.value,
      fileName: fileName,
      fileType: getFileTypeFromUrl(fileUrl.value),
      fileSize: 0
    };

    message.success('文件链接添加成功');
    showFilePopover.value = false;
  } catch (error) {
    console.error('添加文件链接失败:', error);
    message.error('添加文件链接失败');
    uploadedFileInfo.value = null;
  } finally {
    uploading.value = false;
  }
};

// 清除已选择的文件
const clearSelectedFile = () => {
  selectedFile.value = null;
  uploadedFileInfo.value = null;
};

// 处理键盘事件
const handleKeyDown = (e: KeyboardEvent) => {
  // 如果按下 Ctrl+Enter 或 Shift+Enter，发送消息
  if (e.key === 'Enter' && (e.ctrlKey || e.shiftKey)) {
    e.preventDefault(); // 阻止默认行为
    sendMessage();
  }
};

// 发送消息
const sendMessage = () => {
  // 如果有选择文件且已上传成功，或者有文件链接，则发送文件
  if ((selectedFile.value && uploadedFileInfo.value) || (!selectedFile.value && uploadedFileInfo.value)) {
    emit('sendFile', uploadedFileInfo.value, inputMessage.value);
    inputMessage.value = '';
    selectedFile.value = null;
    uploadedFileInfo.value = null;
    return;
  }

  // 如果有选择文件但未上传成功，提示用户
  if (selectedFile.value && !uploadedFileInfo.value) {
    if (uploading.value) {
      message.warning('文件正在上传中，请稍候...');
    } else {
      message.warning('文件上传失败，请重新选择文件');
      clearSelectedFile();
    }
    return;
  }

  // 否则发送文本消息
  if (!inputMessage.value.trim()) return;

  emit('send', inputMessage.value);
  inputMessage.value = '';
};
</script>

<style lang="less" scoped>
.chat-input-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  padding: 8px 16px;
}

.chat-input {
  display: flex;
  gap: 8px;
  align-items: flex-end; /* 改为底部对齐，适应多行输入框 */

  .textarea-wrapper {
    flex: 1;
    width: 100%;
  }

  :deep(.ant-input-textarea-affix-wrapper) {
    width: 100%;
  }

  :deep(.ant-input) {
    resize: none; /* 禁止手动调整大小 */
    padding: 8px 12px; /* 调整内边距 */
    line-height: 1.5; /* 调整行高 */
  }
}

.send-button {
  flex-shrink: 0;
}

.file-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 8px;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

.file-icon {
  font-size: 24px;
  margin-right: 8px;
  color: #1890ff;
}

.file-details {
  flex: 1;
  overflow: hidden;
}

.file-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-status {
  font-size: 12px;
  padding: 0 6px;
  border-radius: 10px;
  background-color: #faad14;
  color: #fff;
}

.upload-status.success {
  background-color: #52c41a;
}

.file-popover-content {
  width: 400px;
  padding: 12px;
}

.file-url-input {
  display: flex;
  gap: 8px;
}

:deep(.ant-popover-inner-content) {
  padding: 16px;
}

.full-width-upload {
  width: 100%;

  :deep(.ant-upload.ant-upload-drag) {
    width: 100%;
    height: auto;
  }
}

.upload-drag-area {
  padding: 16px;
  text-align: center;
}

.upload-drag-icon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 8px;
}

.upload-drag-text {
  font-size: 16px;
  margin-bottom: 4px;
}

.upload-tip {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}
</style>
