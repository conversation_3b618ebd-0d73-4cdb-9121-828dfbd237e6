<template>
  <div :class="['message', message.sender === 'user' ? 'message-user' : 'message-ai']">
    <div class="message-avatar">
      <img :src="message.avatar" alt="avatar" />
    </div>
    <div :class="['message-content', messageTypeClass]">
      <!-- 文件预览 -->
      <div v-if="message.fileUrl" class="message-file">
        <!-- 图片文件 -->
        <div v-if="message.fileType === FileType.IMAGE" class="file-preview image-preview">
          <Image :src="message.fileUrl" :alt="message.fileName || '图片'" />
        </div>

        <!-- 视频文件 -->
        <div v-else-if="message.fileType === FileType.VIDEO" class="file-preview video-preview">
          <video controls :src="message.fileUrl"></video>
        </div>

        <!-- 音频文件 -->
        <div v-else-if="message.fileType === FileType.AUDIO" class="file-preview audio-preview">
          <audio controls :src="message.fileUrl"></audio>
        </div>

        <!-- 其他文件类型 -->
        <div v-else class="file-preview document-preview">
          <div class="file-icon">
            <FileTextIcon v-if="message.fileType === FileType.DOCUMENT" />
            <FileIcon v-else />
          </div>
          <div class="file-info">
            <div class="file-name">{{ message.fileName || '文件' }}</div>
            <div class="file-size" v-if="message.fileSize">{{ formatFileSize(message.fileSize) }}</div>
          </div>
          <Button type="link" size="small" @click="downloadFile">
            <DownloadIcon />
          </Button>
        </div>
      </div>

      <!-- 警告图标 (仅在错误消息中显示) -->
      <div v-if="isErrorMessage" class="message-error-icon">
        <WarningIcon />
        <span>警告</span>
      </div>

      <!-- 文本内容 -->
      <div class="message-text">
        {{ message.content }}
        <span v-if="message.sender === 'ai' && isTyping" class="typing-cursor">|</span>
      </div>
      <div class="message-time">{{ message.timestamp }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { Image, Button } from 'ant-design-vue';
import { FileIcon, FileTextIcon, DownloadIcon, WarningIcon } from '@vben/icons';
import type { ChatMessage } from './types';
import { FileType, MessageType } from './types';

const props = defineProps<{
  message: ChatMessage;
  isTyping?: boolean;
}>();

// 判断是否为错误消息
const isErrorMessage = computed(() => {
  return props.message.type === MessageType.ERROR;
});

// 根据消息类型返回对应的样式类
const messageTypeClass = computed(() => {
  if (props.message.type === MessageType.ERROR) {
    return 'message-error';
  } else if (props.message.type === MessageType.WARNING) {
    return 'message-warning';
  } else if (props.message.type === MessageType.INFO) {
    return 'message-info';
  }
  return '';
});

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }
};

// 下载文件
const downloadFile = () => {
  if (props.message.fileUrl) {
    const a = document.createElement('a');
    a.href = props.message.fileUrl;
    a.download = props.message.fileName || 'download';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }
};
</script>

<style lang="less" scoped>
.message {
  display: flex;
  max-width: 80%;
}

.message-user {
  margin-left: auto;
  flex-direction: row-reverse;
}

.message-ai {
  margin-right: auto;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-content {
  margin: 0 12px;
  padding: 12px;
  border-radius: 8px;
  background-color: #f0f2f5;
  max-width: 100%;
  position: relative;
}

.message-user .message-content {
  background-color: #e6f7ff;
}

/* 错误消息样式 */
.message-error {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
}

/* 警告消息样式 */
.message-warning {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
}

/* 信息消息样式 */
.message-info {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.message-error-icon {
  color: #ff4d4f;
  font-size: 16px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.message-error-icon svg {
  margin-right: 8px;
}

.message-text {
  word-break: break-word;
  white-space: pre-wrap;
}

.message-time {
  font-size: 12px;
  margin-top: 4px;
  color: rgba(0, 0, 0, 0.45);
  text-align: right;
}

.message-file {
  margin-bottom: 8px;
  border-radius: 4px;
  overflow: hidden;
}

.file-preview {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
}

.image-preview, .video-preview {
  max-width: 300px;
  max-height: 200px;
  overflow: hidden;
  display: flex;
  justify-content: center;
}

.image-preview :deep(img) {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
}

.video-preview video, .audio-preview audio {
  max-width: 100%;
}

.document-preview {
  display: flex;
  align-items: center;
  padding: 8px;
}

.file-icon {
  font-size: 24px;
  margin-right: 8px;
  color: #1890ff;
}

.file-info {
  flex: 1;
  overflow: hidden;
}

.file-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.typing-cursor {
  display: inline-block;
  width: 2px;
  height: 16px;
  background-color: currentColor;
  margin-left: 2px;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 49% {
    opacity: 1;
  }
  50%, 100% {
    opacity: 0;
  }
}
</style>
