<template>
  <div ref="scrollbarRef" class="chat-messages">
    <div v-for="message in messages" :key="message.id" class="message-wrapper">
      <ChatMessage :message="message" :isTyping="isTyping && message.id === getTypingMessageId()" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onUpdated, nextTick } from 'vue';
import type { ChatMessage as ChatMessageType } from './types';
import ChatMessage from './ChatMessage.vue';

const props = defineProps<{
  messages: ChatMessageType[];
  isTyping?: boolean;
}>();

// 获取正在打字的消息ID
const getTypingMessageId = () => {
  if (!props.isTyping) return '';
  const aiMessages = props.messages.filter(msg => msg.sender === 'ai');
  return aiMessages.length > 0 ? aiMessages[aiMessages.length - 1].id : '';
};

const scrollbarRef = ref<HTMLElement | null>(null);

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick();
  if (scrollbarRef.value) {
    scrollbarRef.value.scrollTop = scrollbarRef.value.scrollHeight;
  }
};

// 当消息更新时滚动到底部
onUpdated(() => {
  scrollToBottom();
});
</script>

<style lang="less" scoped>
.chat-messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  height: 100%;
}

.message-wrapper {
  margin-bottom: 16px;
}
</style>
