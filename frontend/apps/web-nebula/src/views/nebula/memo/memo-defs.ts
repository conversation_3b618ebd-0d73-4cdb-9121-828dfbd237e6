// 备忘录类型
export type MemoType = 'text' | 'image' | 'file' | 'share';

// 备忘录实体
export interface Memo {
  id: string;
  type: MemoType;
  text: string;
  created_at: string;
  updated_at: string;
  file_key?: string;
  file_type?: string;
  file_size?: number;
  link?: string;
  desc?: string;
  icon?: string;
  stock_code?: string;
  generated?: boolean;  // 是否已通过AI生成内容
  gen_text?: string;    // AI生成的文本内容
  signed_url?: string;  // 签名URL
}

// 备忘录表单数据
export interface MemoForm {
  text: {
    text: string;
  };
  image: {
    text: string;
    file_key: string;
    file_type: string;
    file_size: number;
  };
  file: {
    text: string;
    file_key: string;
    file_type: string;
    file_size: number;
    stock_code: string;
  };
  share: {
    text: string;
    link: string;
    desc: string;
    icon: string;
    stock_code: string;
  };
}

// 图片预览数据
export interface MemoPreview {
  imageUrl: string;
  text: string;
  editText: string;
  isEditing: boolean;
  memoId: string | null;
}

// 备忘录对话框状态
export interface MemoDialogs {
  text: boolean;
  image: boolean;
  file: boolean;
  share: boolean;
  imagePreview: boolean;
  deleteConfirm: boolean;
}

// 备忘录加载状态
export interface MemoLoading {
  list: boolean;
  save: boolean;
  delete: boolean;
  download: boolean;
}
