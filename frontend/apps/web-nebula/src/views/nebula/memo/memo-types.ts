// 备忘录类型
export type MemoType = 'text' | 'image' | 'file' | 'share';

// 备忘录实体
export interface MemoEntity {
  id: string;
  type: MemoType;
  text: string;
  created_at: string;
  updated_at: string;
  file_key?: string;
  file_type?: string;
  file_size?: number;
  link?: string;
  desc?: string;
  icon?: string;
  stock_code?: string;
}

// 备忘录列表响应
export interface MemoListResponse {
  data: MemoEntity[];
  total: number;
}

// 文本备忘表单
export interface TextMemoForm {
  text: string;
}

// 图片备忘表单
export interface ImageMemoForm {
  file: any[];
  text: string;
  file_key: string;
  file_type: string;
  file_size: number;
}

// 文件备忘表单
export interface FileMemoForm {
  file: any[];
  text: string;
  file_key: string;
  file_type: string;
  file_size: number;
  stock_code: string;
}

// 分享备忘表单
export interface ShareMemoForm {
  text: string;
  link: string;
  desc: string;
  icon: string;
  stock_code: string;
}

// 备忘表单数据
export interface MemoFormDataType {
  text: TextMemoForm;
  image: ImageMemoForm;
  file: FileMemoForm;
  share: ShareMemoForm;
}

// 图片预览数据
export interface MemoImagePreviewType {
  imageUrl: string;
  text: string;
  editText: string;
  isEditing: boolean;
  memoId: string | null;
}

// 备忘录对话框状态
export interface MemoDialogsType {
  text: boolean;
  image: boolean;
  file: boolean;
  share: boolean;
  imagePreview: boolean;
  deleteConfirm: boolean;
}

// 备忘录加载状态
export interface MemoLoadingType {
  list: boolean;
  save: boolean;
  delete: boolean;
  download: boolean;
}
