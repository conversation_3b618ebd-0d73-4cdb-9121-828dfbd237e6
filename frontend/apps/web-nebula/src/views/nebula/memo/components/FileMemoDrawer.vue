<template>
  <Drawer
    :open="open"
    :title="isEdit ? '编辑文件备忘' : '新建文件备忘'"
    @update:open="$emit('update:open', $event)"
    :width="isMobile ? '100%' : '520px'"
    :height="isMobile ? '80%' : undefined"
    :placement="isMobile ? 'top' : 'right'"
    :footer-style="{ textAlign: 'right' }"
    @close="$emit('update:open', false)"
  >
    <Form ref="formRef" :model="formState" :rules="rules" layout="vertical">
      <Form.Item name="file" label="上传文件" v-if="!isEdit">
        <Upload
          v-model:file-list="fileList"
          name="file"
          :before-upload="beforeUpload"
          :customRequest="customUpload"
          :multiple="false"
          :maxCount="1"
          :drag="true"
          class="full-width-upload"
        >
          <Flex vertical align="center" class="upload-drag-area">
            <Typography.Text class="upload-drag-icon">
              <UploadIcon />
            </Typography.Text>
            <Typography.Text class="upload-drag-text">点击或拖拽文件到此区域上传</Typography.Text>
            <Typography.Text type="secondary" class="upload-tip">支持任意类型文件</Typography.Text>
          </Flex>
        </Upload>
      </Form.Item>
      <Form.Item name="text" label="文件描述">
        <Input.TextArea
          v-model:value="formState.text"
          placeholder="添加文件描述..."
          :auto-size="{ minRows: 20, maxRows: 28 }"
          :maxlength="200"
          show-count
        />
      </Form.Item>
      <Form.Item name="stock_code" label="股票代码 (可选)">
        <Input
          v-model:value="formState.stock_code"
          placeholder="例如: SH600000"
          :maxlength="20"
        />
      </Form.Item>
    </Form>
    <template #footer>
      <Flex justify="end" gap="small">
        <Button @click="$emit('update:open', false)">取消</Button>
        <Button type="primary" :loading="loading" @click="handleConfirm">确定</Button>
      </Flex>
    </template>
  </Drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, h } from 'vue';
import { Drawer, Form, Input, Upload, Button, message, Flex, Typography, Space } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { uploadUtils } from '#/utils/upload';

// 自定义上传图标
const UploadIcon = () => h('svg', {
  viewBox: '0 0 24 24',
  width: '1em',
  height: '1em',
  fill: 'currentColor',
  style: 'width: 36px; height: 36px;'
}, [
  h('path', {
    d: 'M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM14 13v4h-4v-4H7l5-5 5 5h-3z'
  })
]);

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({ file: [], text: '', file_key: '', file_type: '', file_size: 0, stock_code: '' })
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:open', 'confirm']);

// 表单引用
const formRef = ref();

// 表单状态
const formState = reactive({
  text: '',
  file_key: '',
  file_type: '',
  file_size: 0,
  stock_code: ''
});

// 检测是否为移动设备
const isMobile = ref(window.innerWidth < 768);

// 监听窗口大小变化
window.addEventListener('resize', () => {
  isMobile.value = window.innerWidth < 768;
});

// 文件列表
const fileList = ref<any[]>([]);

// 表单验证规则
const rules: Record<string, Rule[]> = {
  text: [
    { required: false, message: '请输入文件描述', trigger: 'blur' }
  ],
  stock_code: [
    { required: false, pattern: /^[A-Za-z0-9]+$/, message: '股票代码格式不正确', trigger: 'blur' }
  ]
};

// 监听表单数据变化
// watch(
//   () => props.formData,
//   (newVal) => {
//     formState.text = newVal.text || '';
//     formState.file_key = newVal.file_key || '';
//     formState.file_type = newVal.file_type || '';
//     formState.file_size = newVal.file_size || 0;
//     formState.stock_code = newVal.stock_code || '';

//     // 如果是编辑模式且有文件键，显示已有文件
//     if (props.isEdit && newVal.file_key) {
//       fileList.value = [{
//         uid: '-1',
//         name: newVal.file_key.split('/').pop() || 'file',
//         status: 'done',
//         url: `https://z.fee.red/nebula/${newVal.file_key}`
//       }];
//     } else {
//       fileList.value = [];
//     }
//   },
//   { immediate: true, deep: true }
// );

// 上传前检查
const beforeUpload = () => {
  // 不限制文件大小
  return true;
};

// 自定义上传
const customUpload = async (options: any) => {
  const { file, onSuccess, onError } = options;

  try {
    // 使用uploadUtils处理上传
    const result = await uploadUtils.process(file, {
      bucketName: 'memo',
      loading: ref(false)
    });

    if (result && result.object_name) {
      // 更新表单状态
      formState.file_key = result.object_name;
      formState.file_type = file.type;
      formState.file_size = file.size;

      onSuccess(result, file);
    } else {
      onError(new Error('上传失败'));
    }
  } catch (error) {
    console.error('上传文件失败:', error);
    onError(error);
  }
};

// 处理确认
const handleConfirm = () => {
  formRef.value
    .validate()
    .then(() => {
      // 如果是编辑模式且没有上传新文件，保留原有文件信息
      if (props.isEdit && !formState.file_key && props.formData.file_key) {
        formState.file_key = props.formData.file_key;
        formState.file_type = props.formData.file_type;
        formState.file_size = props.formData.file_size;
      }

      // 检查是否有文件
      if (!formState.file_key && !props.isEdit) {
        message.error('请上传文件');
        return;
      }

      emit('confirm', {
        text: formState.text,
        file_key: formState.file_key,
        file_type: formState.file_type,
        file_size: formState.file_size,
        stock_code: formState.stock_code
      });
    })
    .catch((error: any) => {
      console.error('表单验证失败:', error);
    });
};
</script>
