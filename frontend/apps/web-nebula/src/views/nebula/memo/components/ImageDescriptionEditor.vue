<template>
  <Modal
    :open="open"
    title="编辑图片描述"
    @update:open="$emit('update:open', $event)"
    :footer="null"
    :width="isPhone ? '95%' : isMobile ? '90%' : '1000px'"
    class="image-preview-modal"
    :bodyStyle="{ padding: '0px', maxHeight: isPhone ? '80vh' : 'none', overflow: 'auto' }"
  >
    <!-- 桌面端和平板端布局 -->
    <Flex v-if="!isPhone" class="image-preview-container" :direction="isMobile ? 'vertical' : 'horizontal'" :gap="16" style="padding: 16px;">
      <Card class="image-preview" :bordered="false" :bodyStyle="{ padding: 0, display: 'flex', alignItems: 'center', justifyContent: 'center' }">
        <Image :src="previewData.imageUrl" alt="预览图片" :preview="true" class="desktop-image" />
      </Card>
      <Card class="image-info" :bordered="true" :bodyStyle="{ padding: 0, display: 'flex', flexDirection: 'column', flex: 1, overflow: 'hidden' }">
        <template #title>图片描述</template>
        <Flex vertical class="image-edit">
          <Input.TextArea
            v-model:value="previewData.editText"
            placeholder="添加图片描述..."
            :rows="isMobile ? 12 : 20"
            :maxlength="200"
            show-count
            class="large-textarea"
          />
        </Flex>
        <template #actions>
          <Flex justify="end" gap="small">
            <Button type="primary" @click="handleSave" :loading="loading">
              保存
            </Button>
            <Button @click="$emit('update:open', false)">
              取消
            </Button>
          </Flex>
        </template>
      </Card>
    </Flex>

    <!-- 手机端布局 -->
    <Flex v-else vertical class="phone-preview-container" style="padding: 0;">
      <!-- 使用单个折叠面板包裹图片预览和描述编辑区域，设置accordion模式 -->
      <Collapse v-model:activeKey="activeCollapseKeys" class="phone-collapse" accordion @change="(key) => key === '' && togglePanel(activeCollapseKeys === 'preview' ? 'description' : 'preview')">
        <!-- 图片预览面板 -->
        <Collapse.Panel key="preview" header="图片预览">
          <Image :src="previewData.imageUrl" alt="预览图片" :preview="true" class="phone-image" />
        </Collapse.Panel>

        <!-- 描述编辑面板 -->
        <Collapse.Panel key="description" header="图片描述">
          <Flex vertical class="phone-description-edit">
            <Input.TextArea
              v-model:value="previewData.editText"
              placeholder="添加图片描述..."
              :rows="10"
              :maxlength="200"
              show-count
              class="phone-textarea"
            />
            <Flex justify="end" gap="small" style="margin-top: 12px;">
              <Button type="primary" @click="handleSave" :loading="loading">
                保存
              </Button>
              <Button @click="$emit('update:open', false)">
                取消
              </Button>
            </Flex>
          </Flex>
        </Collapse.Panel>
      </Collapse>
    </Flex>
  </Modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Modal, Input, Button, Flex, Card, Collapse, Image } from 'ant-design-vue';
import type { MemoPreview } from '../memo-defs';

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  previewData: {
    type: Object as () => MemoPreview,
    default: () => ({
      imageUrl: '',
      text: '',
      editText: '',
      isEditing: false,
      memoId: null
    })
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:open', 'save']);

// 检测是否为移动设备
const isMobile = ref(window.innerWidth < 768);
// 检测是否为手机设备（更小的屏幕）
const isPhone = ref(window.innerWidth < 480);
// 折叠面板激活的key，默认只展开图片预览区域
const activeCollapseKeys = ref('preview');

// 监听窗口大小变化
window.addEventListener('resize', () => {
  isMobile.value = window.innerWidth < 768;
  isPhone.value = window.innerWidth < 480;
});

// 初始化编辑状态
watch(() => props.open, (newVal) => {
  if (newVal) {
    // 当对话框打开时，确保编辑文本与原文本一致
    props.previewData.editText = props.previewData.text;
  }
});

// 监听折叠面板的变化，确保始终有一个面板是展开的
watch(activeCollapseKeys, (newVal, oldVal) => {
  if (!newVal) {
    // 如果所有面板都收起，则自动展开另一个面板
    // 如果之前是展开图片预览，现在收起了，则展开描述编辑
    // 如果之前是展开描述编辑，现在收起了，则展开图片预览
    activeCollapseKeys.value = oldVal === 'preview' ? 'description' : 'preview';
  }
});

// 保存描述
const handleSave = () => {
  emit('save');
};

// 切换面板
const togglePanel = (key: string) => {
  // 如果当前面板已经展开，则收起它并展开另一个面板
  if (activeCollapseKeys.value === key) {
    activeCollapseKeys.value = key === 'preview' ? 'description' : 'preview';
  } else {
    // 否则展开当前面板
    activeCollapseKeys.value = key;
  }
};


</script>

<style lang="less" scoped>
.image-preview-modal {
  :deep(.ant-modal-content) {
    /* 移动设备上的样式 */
    @media (max-width: 767px) {
      max-height: 90vh; /* 限制最大高度为视口高度的90% */
      overflow: auto; /* 允许滚动 */
    }

    html.dark & {
      background-color: hsl(var(--background));
      box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
    }
  }

  :deep(.ant-modal-body) {
    /* 手机设备上的样式 */
    @media (max-width: 480px) {
      padding: 12px; /* 减小内边距 */
      max-height: 80vh; /* 限制最大高度 */
      overflow: auto; /* 允许滚动 */
    }
  }

  :deep(.ant-modal-header) {
    html.dark & {
      background-color: hsl(var(--accent-dark));
      border-color: hsl(var(--border));
    }
  }

  :deep(.ant-modal-title) {
    html.dark & {
      color: hsl(var(--foreground));
    }
  }

  :deep(.ant-modal-close) {
    html.dark & {
      color: hsl(var(--muted-foreground));

      &:hover {
        color: hsl(var(--foreground));
      }
    }
  }
}
.image-preview-container {
  height: 650px;
  overflow: hidden;

  /* 平板设备上的样式 */
  @media (max-width: 767px) and (min-width: 481px) {
    height: 550px; /* 在平板设备上使用较小的固定高度 */
    overflow: auto; /* 允许滚动 */
  }

  /* 手机设备上的样式 */
  @media (max-width: 480px) {
    height: auto; /* 在手机设备上使用自动高度 */
    max-height: 70vh; /* 限制最大高度 */
    overflow: visible; /* 不需要滚动 */
    gap: 8px !important; /* 减小间距 */
  }
}

.image-preview {
  width: 350px;
  flex-shrink: 0;
  background-color: #f5f5f5;
  max-height: 650px;
  overflow: auto;
  border-radius: 4px;

  /* 平板设备上的样式 */
  @media (max-width: 767px) and (min-width: 481px) {
    height: 250px; /* 在平板设备上设置较小的固定高度 */
    flex: 0 0 auto; /* 不要伸缩 */
    width: 100%;
    margin-bottom: 16px; /* 添加底部间距 */
  }

  /* 手机设备上的样式 */
  @media (max-width: 480px) {
    height: 200px; /* 在手机设备上设置较小的固定高度 */
    flex: 0 0 auto; /* 不要伸缩 */
    width: 100%;
    margin-bottom: 8px; /* 添加底部间距 */
  }

  html.dark & {
    background-color: hsl(var(--secondary));
  }

  .desktop-image {
    max-width: 100%;
    max-height: 600px;
    height: auto;

    :deep(img) {
      object-fit: contain;
      max-width: 100%;
      max-height: 600px;
      height: auto;

      /* 移动设备上的样式 */
      @media (max-width: 767px) {
        max-height: 200px; /* 稍微小于容器高度 */
      }

      /* 手机设备上的样式 */
      @media (max-width: 480px) {
        max-height: 180px; /* 更小的高度 */
      }

      html.dark & {
        filter: brightness(1.05) contrast(1.05);
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
      }
    }
  }
}

.image-info {
  flex: 1;
  max-height: 650px;
  display: flex;
  flex-direction: column;

  /* 平板设备上的样式 */
  @media (max-width: 767px) and (min-width: 481px) {
    height: 250px; /* 在平板设备上设置较小的固定高度 */
    flex: 0 0 auto; /* 不要伸缩 */
    min-height: 0; /* 允许内容区域收缩 */
    width: 100%;
  }

  /* 手机设备上的样式 */
  @media (max-width: 480px) {
    height: auto; /* 在手机设备上使用自动高度 */
    flex: 0 0 auto; /* 不要伸缩 */
    min-height: 0; /* 允许内容区域收缩 */
    width: 100%;
  }

  :deep(.ant-card-head) {
    background-color: #fafafa;
    padding: 0 16px;

    /* 手机设备上的样式 */
    @media (max-width: 480px) {
      min-height: 40px; /* 减小标题区域高度 */
      padding: 0 12px;
    }

    html.dark & {
      background-color: hsl(var(--accent-dark));
      border-color: hsl(var(--border));
    }
  }

  :deep(.ant-card-head-title) {
    padding: 12px 0;

    /* 手机设备上的样式 */
    @media (max-width: 480px) {
      padding: 8px 0; /* 减小标题内边距 */
      font-size: 14px; /* 减小字体大小 */
    }
  }
}

.image-edit {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  min-height: 0; /* 重要：允许内容区域收缩 */

  /* 平板设备上的样式 */
  @media (max-width: 767px) and (min-width: 481px) {
    padding: 8px; /* 减小内边距 */
    flex: 1 1 auto; /* 允许伸缩 */
  }

  /* 手机设备上的样式 */
  @media (max-width: 480px) {
    padding: 8px; /* 减小内边距 */
    flex: 1 1 auto; /* 允许伸缩 */
    height: auto; /* 使用自动高度 */
  }

  :deep(.ant-input) {
    resize: none;
    border-radius: 2px;

    html.dark & {
      background-color: hsl(var(--secondary));
      border-color: hsl(var(--border));
    }
  }

  :deep(.ant-input-textarea) {
    height: 100%;
  }

  :deep(.ant-input-textarea-show-count::after) {
    position: absolute;
    bottom: 4px;
    right: 8px;
    color: #999;
    font-size: 12px;

    /* 手机设备上的样式 */
    @media (max-width: 480px) {
      bottom: 2px;
      right: 4px;
    }

    html.dark & {
      color: hsl(var(--muted-foreground));
    }
  }

  .large-textarea {
    height: 100%;
    min-height: 0; /* 重要：允许内容区域收缩 */

    /* 平板设备上的样式 */
    @media (max-width: 767px) and (min-width: 481px) {
      max-height: 200px; /* 在平板设备上限制最大高度 */
    }

    /* 手机设备上的样式 */
    @media (max-width: 480px) {
      max-height: none; /* 在手机设备上不限制最大高度 */
      height: 150px; /* 设置固定高度 */
    }
  }
}

.image-desc {
  margin-bottom: 16px;

  p {
    margin-bottom: 16px;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .no-desc {
    color: #999;
    font-style: italic;

    html.dark & {
      color: hsl(var(--muted-foreground));
    }
  }
}

:deep(.ant-card-actions) {
  background-color: #fafafa;

  /* 手机设备上的样式 */
  @media (max-width: 480px) {
    padding: 4px 0; /* 减小底部操作区域内边距 */
  }

  html.dark & {
    background-color: hsl(var(--accent-dark));
    border-color: hsl(var(--border));
  }
}

/* 手机端布局样式 */
.phone-preview-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 70vh;
  overflow: auto;
}

/* 手机端折叠面板样式 */
.phone-collapse {
  width: 100%;
  border-radius: 4px;
  overflow: hidden;

  :deep(.ant-collapse-item) {
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    html.dark & {
      border-bottom-color: hsl(var(--border));
    }
  }

  :deep(.ant-collapse-header) {
    padding: 12px 16px;
    font-size: 15px;
    font-weight: 500;
    background-color: #f5f5f5;

    html.dark & {
      background-color: hsl(var(--secondary));
      color: hsl(var(--foreground));
    }
  }

  :deep(.ant-collapse-content) {
    border-top: 1px solid #f0f0f0;

    html.dark & {
      border-top-color: hsl(var(--border));
    }
  }

  /* 图片预览面板内容样式 */
  :deep(.ant-collapse-item[data-key="preview"] .ant-collapse-content-box) {
    padding: 0px;
    line-height: 0;
  }

  /* 描述编辑面板内容样式 */
  :deep(.ant-collapse-item[data-key="description"] .ant-collapse-content-box) {
    padding: 0px;
  }
}

/* 手机端图片样式 */
.phone-image {
  width: 100%;
  height: auto;
  display: block;
  background-color: #f5f5f5;

  :deep(img) {
    width: 100%;
    height: auto;
    object-fit: contain;
  }

  html.dark & {
    background-color: hsl(var(--secondary));

    :deep(img) {
      filter: brightness(1.05) contrast(1.05);
    }
  }
}

.phone-description-edit {
  padding: 0;

  .phone-textarea {
    width: 100%;
    resize: none;
    margin-bottom: 8px;
  }
}
</style>
