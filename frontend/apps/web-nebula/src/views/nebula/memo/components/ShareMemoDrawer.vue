<template>
  <Drawer
    :open="open"
    :title="isEdit ? '编辑分享备忘' : '新建分享备忘'"
    @update:open="$emit('update:open', $event)"
    :width="isMobile ? '100%' : '520px'"
    :height="isMobile ? '80%' : undefined"
    :placement="isMobile ? 'top' : 'right'"
    :footer-style="{ textAlign: 'right' }"
    @close="$emit('update:open', false)"
  >
    <Form ref="formRef" :model="formState" :rules="rules" layout="vertical">
      <Form.Item name="text" label="标题">
        <Input
          v-model:value="formState.text"
          placeholder="分享标题..."
          :maxlength="100"
          show-count
        />
      </Form.Item>
      <Form.Item name="link" label="链接">
        <Input
          v-model:value="formState.link"
          placeholder="https://..."
          :maxlength="500"
        />
      </Form.Item>
      <Form.Item name="desc" label="描述 (可选)">
        <Input.TextArea
          v-model:value="formState.desc"
          placeholder="添加描述..."
          :auto-size="{ minRows: 6, maxRows: 10 }"
          :maxlength="200"
          show-count
        />
      </Form.Item>
      <Form.Item name="icon" label="图标URL (可选)">
        <Input
          v-model:value="formState.icon"
          placeholder="图标URL..."
          :maxlength="200"
        />
      </Form.Item>
      <Form.Item name="stock_code" label="股票代码 (可选)">
        <Input
          v-model:value="formState.stock_code"
          placeholder="例如: SH600000"
          :maxlength="20"
        />
      </Form.Item>
    </Form>
    <template #footer>
      <Flex justify="end" gap="small">
        <Button @click="$emit('update:open', false)">取消</Button>
        <Button type="primary" :loading="loading" @click="handleConfirm">确定</Button>
      </Flex>
    </template>
  </Drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import { Drawer, Form, Input, Button, Flex } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({ text: '', link: '', desc: '', icon: '', stock_code: '' })
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:open', 'confirm']);

// 表单引用
const formRef = ref();

// 表单状态
const formState = reactive({
  text: '',
  link: '',
  desc: '',
  icon: '',
  stock_code: ''
});

// 检测是否为移动设备
const isMobile = ref(window.innerWidth < 768);

// 监听窗口大小变化
window.addEventListener('resize', () => {
  isMobile.value = window.innerWidth < 768;
});

// 表单验证规则
const rules: Record<string, Rule[]> = {
  text: [
    { required: true, message: '请输入分享标题', trigger: 'blur' }
  ],
  link: [
    { required: true, message: '请输入分享链接', trigger: 'blur' },
    { pattern: /^https?:\/\/.+/, message: '请输入有效的URL', trigger: 'blur' }
  ],
  desc: [
    { required: false, message: '请输入分享描述', trigger: 'blur' }
  ],
  icon: [
    { required: false, pattern: /^https?:\/\/.+/, message: '请输入有效的URL', trigger: 'blur' }
  ],
  stock_code: [
    { required: false, pattern: /^[A-Za-z0-9]+$/, message: '股票代码格式不正确', trigger: 'blur' }
  ]
};

// 监听表单数据变化
watch(
  () => props.formData,
  (newVal) => {
    formState.text = newVal.text || '';
    formState.link = newVal.link || '';
    formState.desc = newVal.desc || '';
    formState.icon = newVal.icon || '';
    formState.stock_code = newVal.stock_code || '';
  },
  { immediate: true, deep: true }
);

// 处理确认
const handleConfirm = () => {
  formRef.value
    .validate()
    .then(() => {
      emit('confirm', {
        text: formState.text,
        link: formState.link,
        desc: formState.desc,
        icon: formState.icon,
        stock_code: formState.stock_code
      });
    })
    .catch((error: any) => {
      console.error('表单验证失败:', error);
    });
};
</script>
