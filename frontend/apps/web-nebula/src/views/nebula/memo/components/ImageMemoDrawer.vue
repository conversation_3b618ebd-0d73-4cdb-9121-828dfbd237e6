<template>
  <Drawer
    :open="open"
    :title="isEdit ? '编辑图片备忘' : '新建图片备忘'"
    @update:open="$emit('update:open', $event)"
    :width="isMobile ? '100%' : '520px'"
    :height="isMobile ? '80%' : undefined"
    :placement="isMobile ? 'top' : 'right'"
    :footer-style="{ textAlign: 'right' }"
    @close="$emit('update:open', false)"
  >
    <Form ref="formRef" :model="formState" :rules="rules" layout="vertical">
      <Form.Item name="file" label="上传图片" v-if="!isEdit">
        <Upload
          v-model:file-list="fileList"
          name="file"
          :before-upload="beforeUpload"
          :customRequest="customUpload"
          :multiple="false"
          :maxCount="1"
          :drag="true"
          @preview="handlePreview"
          class="full-width-upload"
        >
          <Flex vertical align="center" class="upload-drag-area">
            <Typography.Text class="upload-drag-icon">
              <UploadIcon />
            </Typography.Text>
            <Typography.Text class="upload-drag-text">点击或拖拽图片到此区域上传</Typography.Text>
            <Typography.Text type="secondary" class="upload-tip">支持常见图片格式</Typography.Text>
          </Flex>
        </Upload>
        <Modal
          :open="previewVisible"
          :title="previewTitle"
          :footer="null"
          @update:open="previewVisible = $event"
        >
          <Image alt="预览图片" width="100%" :src="previewImage" />
        </Modal>
      </Form.Item>
      <Form.Item name="text" label="图片描述">
        <Input.TextArea
          v-model:value="formState.text"
          placeholder="添加图片描述..."
          :auto-size="{ minRows: 20, maxRows: 28 }"
          :maxlength="200"
          show-count
        />
      </Form.Item>
    </Form>
    <template #footer>
      <Flex justify="end" gap="small">
        <Button @click="$emit('update:open', false)">取消</Button>
        <Button type="primary" :loading="loading" @click="handleConfirm">确定</Button>
      </Flex>
    </template>
  </Drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, h } from 'vue';
import { Drawer, Form, Input, Upload, Modal, Button, message, Typography, Flex, Image } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';

import { uploadUtils } from '#/utils/upload';

// 自定义上传图标
const UploadIcon = () => h('svg', {
  viewBox: '0 0 24 24',
  width: '1em',
  height: '1em',
  fill: 'currentColor',
  style: 'width: 36px; height: 36px;'
}, [
  h('path', {
    d: 'M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM14 13v4h-4v-4H7l5-5 5 5h-3z'
  })
]);

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({ file: [], text: '', file_key: '', file_type: '', file_size: 0 })
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:open', 'confirm']);

// 表单引用
const formRef = ref();

// 表单状态
const formState = reactive({
  text: '',
  file_key: '',
  file_type: '',
  file_size: 0
});

// 检测是否为移动设备
const isMobile = ref(window.innerWidth < 768);

// 监听窗口大小变化
window.addEventListener('resize', () => {
  isMobile.value = window.innerWidth < 768;
});

// 文件列表
const fileList = ref<any[]>([]);

// 预览状态
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// 表单验证规则
const rules: Record<string, Rule[]> = {
  text: [
    { required: false, message: '请输入图片描述', trigger: 'blur' }
  ]
};

// 监听表单数据变化
// watch(
//   () => props.formData,
//   (newVal) => {
//     formState.text = newVal.text || '';
//     formState.file_key = newVal.file_key || '';
//     formState.file_type = newVal.file_type || '';
//     formState.file_size = newVal.file_size || 0;

//     // 如果是编辑模式且有文件键，显示已有图片
//     if (props.isEdit && newVal.file_key) {
//       fileList.value = [{
//         uid: '-1',
//         name: newVal.file_key.split('/').pop() || 'image.jpg',
//         status: 'done',
//         url: `https://z.fee.red/nebula/${newVal.file_key}`
//       }];
//     } else {
//       fileList.value = [];
//     }
//   },
//   { immediate: true, deep: true }
// );

// 上传前检查
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    message.error('只能上传图片文件!');
  }

  return isImage;
};

// 自定义上传
const customUpload = async (options: any) => {
  const { file, onSuccess, onError } = options;

  try {
    // 使用uploadUtils处理上传
    const result = await uploadUtils.process(file, {
      bucketName: 'memo',
      loading: ref(false)
    });

    if (result && result.object_name) {
      // 更新表单状态
      formState.file_key = result.object_name;
      formState.file_type = file.type;
      formState.file_size = file.size;

      onSuccess(result, file);
    } else {
      onError(new Error('上传失败'));
    }
  } catch (error) {
    console.error('上传图片失败:', error);
    onError(error);
  }
};

// 处理预览
const handlePreview = async (file: any) => {
  if (!file.url && !file.preview) {
    file.preview = await getBase64(file.originFileObj);
  }
  previewImage.value = file.url || file.preview;
  previewVisible.value = true;
  previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1);
};

// 转换为Base64
const getBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};

// 处理确认
const handleConfirm = () => {
  formRef.value
    .validate()
    .then(() => {
      // 如果是编辑模式且没有上传新文件，保留原有文件信息
      if (props.isEdit && !formState.file_key && props.formData.file_key) {
        formState.file_key = props.formData.file_key;
        formState.file_type = props.formData.file_type;
        formState.file_size = props.formData.file_size;
      }

      // 检查是否有文件
      if (!formState.file_key && !props.isEdit) {
        message.error('请上传图片');
        return;
      }

      emit('confirm', {
        text: formState.text,
        file_key: formState.file_key,
        file_type: formState.file_type,
        file_size: formState.file_size
      });
    })
    .catch((error: any) => {
      console.error('表单验证失败:', error);
    });
};
</script>
