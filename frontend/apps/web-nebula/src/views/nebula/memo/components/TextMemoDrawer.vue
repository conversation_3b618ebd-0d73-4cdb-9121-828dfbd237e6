<template>
  <Drawer
    :open="open"
    :title="isEdit ? '编辑文本备忘' : '新建文本备忘'"
    @update:open="$emit('update:open', $event)"
    :width="isMobile ? '100%' : '520px'"
    :height="isMobile ? '80%' : undefined"
    :placement="isMobile ? 'top' : 'right'"
    :footer-style="{ textAlign: 'right' }"
    @close="$emit('update:open', false)"
  >
    <Form ref="formRef" :model="formState" :rules="rules" layout="vertical">
      <Form.Item name="text" label="备忘内容">
        <Input.TextArea
          v-model:value="formState.text"
          placeholder="写下你的备忘..."
          :maxlength="500"
          :auto-size="{ minRows: 20, maxRows: 28 }"
          show-count
        />
      </Form.Item>
    </Form>
    <template #footer>
      <Flex justify="end" gap="small">
        <Button @click="$emit('update:open', false)">取消</Button>
        <Button type="primary" :loading="loading" @click="handleConfirm">确定</Button>
      </Flex>
    </template>
  </Drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import { Drawer, Form, Input, Button, Flex } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({ text: '' })
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:open', 'confirm']);

// 表单引用
const formRef = ref();

// 表单状态
const formState = reactive({
  text: ''
});

// 检测是否为移动设备
const isMobile = ref(window.innerWidth < 768);

// 监听窗口大小变化
window.addEventListener('resize', () => {
  isMobile.value = window.innerWidth < 768;
});

// 表单验证规则
const rules: Record<string, Rule[]> = {
  text: [
    { required: true, message: '请输入备忘内容', trigger: 'blur' }
  ]
};

// 监听表单数据变化
watch(
  () => props.formData,
  (newVal) => {
    if (newVal) {
      formState.text = newVal.text || '';
    }
  },
  { immediate: true, deep: true }
);

// 监听对话框可见性变化
watch(
  () => props.open,
  (newVal) => {
    if (newVal && props.formData) {
      // 当对话框打开时，确保表单数据被正确设置
      formState.text = props.formData.text || '';
    }
  }
);

// 处理确认
const handleConfirm = () => {
  formRef.value
    .validate()
    .then(() => {
      // 传递表单数据的副本，避免引用问题
      emit('confirm', { text: formState.text });
    })
    .catch((error: any) => {
      console.error('表单验证失败:', error);
    });
};
</script>
