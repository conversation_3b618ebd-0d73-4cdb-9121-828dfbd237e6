<template>
  <Flex vertical class="view-container">
    <ViewContainer ref="dataCardRef" variant="page">
      <!-- 头部区域 -->
      <template #header>
        <ViewHeader ref="dataHeaderRef">
          <template #title>
            <Typography.Text>备忘录</Typography.Text>
          </template>

          <template #toolbar>
            <Space>
              <!-- 分类选择器 -->
              <Segmented
                v-model:value="filterType"
                :options="[
                  { value: '', label: '全部' },
                  { value: 'text', label: '文本' },
                  { value: 'image', label: '图片' },
                  { value: 'file', label: '文件' },
                  { value: 'share', label: '分享' }
                ]"
                @change="handleFilterChange"
                style="margin-right: 8px;"
              />

              <!-- 搜索栏 -->
              <Input
                v-model:value="searchText"
                placeholder="搜索备忘录..."
                style="width: 220px"
                allow-clear
                @press-enter="handleSearch"
                @clear="handleSearch"
              >
                <template #suffix>
                  <SearchIcon @click="handleSearch" />
                </template>
              </Input>
              <Button
                type="text"
                class="refresh-btn"
                @click="fetchMemoList"
                :disabled="loading.list.value"
                title="刷新"
              >
                <template #icon><RefreshIcon /></template>
              </Button>
            </Space>
          </template>

          <template #actions>
            <!-- 创建备忘按钮 -->
            <Dropdown :trigger="['click']" placement="bottomRight">
              <Button type="primary">
                <span class="plus-icon">+</span>创建备忘
              </Button>
              <template #overlay>
                <Menu @click="handleCreateMenuClick">
                  <MenuItem key="text">
                    <Flex align="center" gap="small" class="menu-item-content">
                      <FileTextIcon /> <Typography.Text>文本备忘</Typography.Text>
                    </Flex>
                  </MenuItem>
                  <MenuItem key="image">
                    <Flex align="center" gap="small" class="menu-item-content">
                      <ImageIcon /> <Typography.Text>图片备忘</Typography.Text>
                    </Flex>
                  </MenuItem>
                  <MenuItem key="file">
                    <Flex align="center" gap="small" class="menu-item-content">
                      <FileIcon /> <Typography.Text>文件备忘</Typography.Text>
                    </Flex>
                  </MenuItem>
                  <MenuItem key="share">
                    <Flex align="center" gap="small" class="menu-item-content">
                      <LinkIcon /> <Typography.Text>分享备忘</Typography.Text>
                    </Flex>
                  </MenuItem>
                </Menu>
              </template>
            </Dropdown>
          </template>
        </ViewHeader>
      </template>

      <!-- 内容区域 -->
      <template #body>
        <ViewBody
          :extraPadding="16"
          :minHeight="300"
        >
          <!-- 瀑布流内容区 -->
          <Flex vertical class="waterfall-container">
            <Spin :spinning="loading.list.value">
              <Image.PreviewGroup>
                <div v-if="memoList.length > 0" class="waterfall-wrapper">
                  <Flex
                    v-for="memo in memoList"
                    :key="memo.id"
                    vertical
                    class="waterfall-item"
                    :class="getCardClass(memo.type)"
                  >
                  <Flex justify="space-between" align="center" class="memo-header">
                    <Space>
                      <component :is="getTypeIcon(memo.type)" />
                      <Typography.Text class="memo-time" :title="formatDate(memo.created_at)">{{ formatter.relativeTime(memo.created_at) }}</Typography.Text>
                      <template v-if="memo.generated">
                        <!-- 图片或文件类型时使用Popover -->
                        <Popover v-if="memo.type === 'image' || memo.type === 'file'"
                                placement="bottom"
                                trigger="hover"
                                overlayClassName="memo-text-popover"
                                :popupStyle="{ maxWidth: '500px' }">
                          <template #content>
                            <div class="memo-text-content markdown-content">
                              <Typography.Paragraph v-html="parseMarkdown(memo.gen_text)"></Typography.Paragraph>
                            </div>
                          </template>
                          <Tag color="blue">AI生成</Tag>
                        </Popover>
                        <!-- 其他类型直接显示Tag -->
                        <Tag v-else color="blue">AI生成</Tag>
                      </template>
                    </Space>
                    <Space>
                      <Button type="text" size="large" style="color: #1890ff;" @click="handleEdit(memo)" title="编辑">
                        <template #icon><EditIcon /></template>
                      </Button>
                      <Button type="text" size="large" style="color: #722ed1;" @click="handleAnalyze(memo)" title="AI分析">
                        <template #icon><GeminiIcon /></template>
                      </Button>
                      <Button v-if="memo.type === 'image' || memo.type === 'file'" type="text" size="large" style="color: #52c41a;" @click="handleDownload(memo)" title="下载">
                        <template #icon><DownloadIcon /></template>
                      </Button>
                      <Button type="text" size="large" style="color: #ff4d4f;" @click="handleDelete(memo)" title="删除">
                        <template #icon><DeleteIcon /></template>
                      </Button>
                    </Space>
                  </Flex>

                  <!-- 文本内容 -->
                  <Typography.Paragraph v-if="memo.type === 'text'" class="memo-content text-content">
                    {{ memo.text }}
                  </Typography.Paragraph>

                  <!-- 图片内容 -->
                  <Flex v-if="memo.type === 'image'" vertical class="memo-content image-content">
                    <div class="image-container">
                      <Image
                        :src="memo.signed_url || getFileUrl(memo.file_key)"
                        :alt="memo.text"
                        :preview="true"
                        loading="lazy"
                        class="memo-image"
                        fallback="data:image/png;base64,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"
                      />
                      <Button
                        type="primary"
                        size="small"
                        class="edit-desc-btn"
                        @click.stop="handleEditImageDesc(memo)"
                        title="编辑描述"
                      >
                        <template #icon><EditIcon /></template>
                      </Button>
                    </div>
                  </Flex>

                  <!-- 文件内容 -->
                  <Flex v-if="memo.type === 'file'" vertical class="memo-content file-content">
                    <Flex class="file-card-container">
                      <Flex class="file-card">
                        <Typography.Link :href="memo.signed_url || getFileUrl(memo.file_key)" target="_blank" download class="file-link">
                          <Flex justify="center" align="center" class="file-icon-wrapper" :class="getFileIconColorClass(memo.file_key || '')">
                            <component :is="getFileIcon(memo.file_key || '')" class="file-icon" />
                          </Flex>
                          <Flex vertical class="file-info">
                            <Typography.Text class="file-name">
                              {{ memo.text || getFileName(memo.file_key) }}
                            </Typography.Text>
                            <Typography.Text class="file-size">
                              {{ formatFileSize(memo.file_size) }} <Typography.Text class="file-ext" :class="'ext-' + getFileExtension(memo.file_key)">({{ getFileExtension(memo.file_key) }})</Typography.Text>
                            </Typography.Text>
                          </Flex>
                        </Typography.Link>
                      </Flex>
                    </Flex>
                  </Flex>

                  <!-- 分享内容 -->
                  <Flex v-if="memo.type === 'share'" vertical class="memo-content share-content">
                    <Flex align="center" class="share-title">
                      <Avatar v-if="memo.icon" :src="memo.icon" size="small" />
                      <Typography.Link :href="memo.link" target="_blank" class="share-link-title">
                        {{ memo.text }}
                      </Typography.Link>
                    </Flex>
                    <Typography.Paragraph v-if="memo.desc" class="share-desc">{{ memo.desc }}</Typography.Paragraph>
                    <Flex class="share-link">
                      <Typography.Link :href="memo.link" target="_blank" class="share-url">
                        {{ formatUrl(memo.link) }}
                      </Typography.Link>
                    </Flex>
                    <Tag v-if="memo.stock_code" color="blue">{{ memo.stock_code }}</Tag>
                  </Flex>
                </Flex>
              </div>
              <Empty v-else-if="!loading.list.value" description="暂无备忘录" />
              </Image.PreviewGroup>
            </Spin>
          </Flex>
        </ViewBody>
      </template>

      <!-- 底部区域 -->
      <template #footer>
        <ViewFooter ref="dataFooterRef">
          <template #left>
            <Typography.Text class="record-count">共 {{ total }} 条记录</Typography.Text>
          </template>
          <Pagination
            :total="total"
            :current="current"
            :pageSize="pageSize"
            :showSizeChanger="true"
            :pageSizeOptions="['12', '24', '36', '48']"
            :responsive="true"
            @change="handlePageChange"
            @showSizeChange="(_current, size) => { handlePageChange(1, size); }"
          />
        </ViewFooter>
      </template>
    </ViewContainer>

    <!-- 文本备忘抽屉 -->
    <TextMemoDrawer
      v-model:open="dialogs.text"
      :loading="loading.save.value"
      :form-data="forms.text"
      :is-edit="!!currentEditId"
      @confirm="handleCreateTextMemo"
    />

    <!-- 图片备忘抽屉 -->
    <ImageMemoDrawer
      v-model:open="dialogs.image"
      :loading="loading.save.value"
      :form-data="forms.image"
      :is-edit="!!currentEditId"
      @confirm="handleCreateImageMemo"
    />

    <!-- 文件备忘抽屉 -->
    <FileMemoDrawer
      v-model:open="dialogs.file"
      :loading="loading.save.value"
      :form-data="forms.file"
      :is-edit="!!currentEditId"
      @confirm="handleCreateFileMemo"
    />

    <!-- 分享备忘抽屉 -->
    <ShareMemoDrawer
      v-model:open="dialogs.share"
      :loading="loading.save.value"
      :form-data="forms.share"
      :is-edit="!!currentEditId"
      @confirm="handleCreateShareMemo"
    />

    <!-- 图片描述编辑器 -->
    <ImageDescriptionEditor
      v-model:open="dialogs.imagePreview"
      :preview-data="preview"
      :loading="loading.save.value"
      @save="handleSaveImageDesc"
    />

    <!-- 删除确认对话框 -->
    <Modal
      v-model:open="dialogs.deleteConfirm"
      title="确认删除"
      @ok="confirmDelete"
      :confirmLoading="loading.delete.value"
    >
      <Typography.Paragraph>确定要删除这条备忘录吗？此操作不可恢复。</Typography.Paragraph>
    </Modal>
  </Flex>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, h } from 'vue';
import { Modal, Pagination, Space, Segmented, Dropdown, Menu, Button, MenuItem, Input, Spin, Empty, Tag, Avatar, Typography, Flex, Image, Popover } from 'ant-design-vue';
import { marked } from 'marked';

import { useActionService } from '#/services/action';
import { apiService } from '#/services/api';
import QueryParamStorage from '#/utils/queryParamStorage';
import { formatter } from '#/utils';

import ViewContainer from '../../../components/ViewContainer.vue';
import ViewHeader from '../../../components/ViewHeader.vue';
import ViewBody from '../../../components/ViewBody.vue';
import ViewFooter from '../../../components/ViewFooter.vue';

// 导入 Iconify 图标
import {
  SearchIcon,
  FileIcon,
  FileTextIcon,
  ImageIcon,
  LinkIcon,
  RobotIcon,
  DownloadIcon,
  EditIcon,
  DeleteIcon,
  UploadIcon,
  AppsIcon,
  VideoIcon,
  CodeIcon,
  RefreshIcon,
  GeminiIcon
} from '@vben/icons';
import type { Memo, MemoForm, MemoDialogs, MemoPreview } from './memo-defs';
import TextMemoDrawer from './components/TextMemoDrawer.vue';
import ImageMemoDrawer from './components/ImageMemoDrawer.vue';
import FileMemoDrawer from './components/FileMemoDrawer.vue';
import ShareMemoDrawer from './components/ShareMemoDrawer.vue';
import ImageDescriptionEditor from './components/ImageDescriptionEditor.vue';
import dayjs from 'dayjs';

// 组件引用
const dataCardRef = ref(null);
const dataHeaderRef = ref(null);
const dataFooterRef = ref(null);

// Action 服务
const action = useActionService();
const tabStorage = new QueryParamStorage(location.pathname);

// 列表数据
const memoList = ref<Memo[]>([]);
const total = ref(0);
const current = ref(1);
const pageSize = ref(12);
const filterType = ref('');
const searchText = ref('');

// 加载状态 - 使用ref而不是reactive，与原始JavaScript项目保持一致
const loading = {
  list: ref(false),
  save: ref(false),
  delete: ref(false),
  download: ref(false)
};

// 对话框显示状态
const dialogs = reactive<MemoDialogs>({
  text: false,
  image: false,
  file: false,
  share: false,
  imagePreview: false,
  deleteConfirm: false
});

// 表单数据
const forms = reactive<MemoForm>({
  text: { text: '' },
  image: { text: '', file_key: '', file_type: '', file_size: 0 },
  file: { text: '', file_key: '', file_type: '', file_size: 0, stock_code: '' },
  share: { text: '', link: '', desc: '', icon: '', stock_code: '' }
});

// 预览数据
const preview = reactive<MemoPreview>({
  imageUrl: '',
  text: '',
  editText: '',
  isEditing: false,
  memoId: null
});

// 当前编辑的备忘录ID
const currentEditId = ref<string | null>(null);

// 待删除的备忘录
const memoToDelete = ref<Memo | null>(null);

// 文件服务器前缀
const FILE_SERVER_PREFIX = 'https://z.fee.red/memo';

// 解析Markdown文本
function parseMarkdown(text: string): string {
  if (!text) return '';
  return marked(text);
}

// 处理下载
function handleDownload(memo: Memo) {
  if (memo.type === 'image' || memo.type === 'file') {
    const url = memo.signed_url || getFileUrl(memo.file_key);
    if (url) {
      const a = document.createElement('a');
      a.href = url;
      a.download = getFileName(memo.file_key);
      a.target = '_blank';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  }
}

// 生命周期钩子
onMounted(() => {
  // 初始化参数
  initParams();

  fetchMemoList();
});

// 组件卸载时移除事件监听
onUnmounted(() => {
});



// 从 URL 和 QueryParamStorage 中读取参数的简化函数
function initParams() {
  current.value = (tabStorage.get('skip') || 0) / (tabStorage.get('limit') || pageSize.value) + 1;
  pageSize.value = tabStorage.get('limit') || pageSize.value;
  searchText.value = tabStorage.get('keyword') || '';
  filterType.value = tabStorage.get('type') || '';
}

// 更新URL参数
function updateUrlParams(params: Record<string, any>) {
  // @ts-ignore - saveData方法在类型定义中可能是私有的，但实际可用
  tabStorage.saveData(params);
  window.history.pushState({}, '', tabStorage.getPathQueryString()); // 更新URL，不刷新页面
};

// 获取备忘录列表
async function fetchMemoList() {
  const params: any = {
    skip: (current.value - 1) * pageSize.value,
    limit: pageSize.value,
    type: filterType.value,
    keyword: searchText.value
  };
  updateUrlParams(params);
  await action.submit(
    apiService.memo.list,
    params,
    {
      loading: loading.list,
      showNotify: false,
      onSuccess: (result: any) => {
        memoList.value = result.data || [];
        total.value = result.total || 0;

        // 添加调试代码，查看备忘录数据
        // console.log('备忘录数据:', memoList.value);

        // 查找generated为true的备忘录
        const generatedMemos = memoList.value.filter(memo => memo.generated);
        // console.log('AI生成的备忘录:', generatedMemos);

        // 检查gen_text字段
        generatedMemos.forEach(memo => {
          // console.log(`备忘录ID: ${memo.id}, gen_text: ${memo.gen_text}, text: ${memo.text}`);
        });
      }
    }
  );
}

// 处理搜索
function handleSearch() {
  current.value = 1;
  fetchMemoList();
}

// 处理筛选变更
function handleFilterChange() {
  current.value = 1;
  fetchMemoList();
}

// 处理页码变更
function handlePageChange(page: number, size?: number) {
  current.value = page;
  if (size !== undefined) {
    pageSize.value = size;
  }
  fetchMemoList();
}

// 处理创建菜单点击
function handleCreateMenuClick(info: any) {
  const key = info.key as string;
  // 重置当前编辑ID
  currentEditId.value = null;

  switch (key) {
    case 'text':
      forms.text = { text: '' };
      dialogs.text = true;
      break;
    case 'image':
      forms.image = { text: '', file_key: '', file_type: '', file_size: 0 };
      dialogs.image = true;
      break;
    case 'file':
      forms.file = { text: '', file_key: '', file_type: '', file_size: 0, stock_code: '' };
      dialogs.file = true;
      break;
    case 'share':
      forms.share = { text: '', link: '', desc: '', icon: '', stock_code: '' };
      dialogs.share = true;
      break;
  }
}

// 处理编辑备忘录
function handleEdit(memo: Memo) {
  // 设置当前编辑ID
  currentEditId.value = memo.id;

  // 根据备忘类型设置表单数据
  switch (memo.type) {
    case 'text':
      forms.text = { text: memo.text };
      dialogs.text = true;
      break;
    case 'image':
      // 对于图片，直接使用图片预览对话框进行编辑
      handleEditImageDesc(memo);
      break;
    case 'file':
      forms.file = {
        text: memo.text,
        file_key: memo.file_key || '',
        file_type: memo.file_type || '',
        file_size: memo.file_size || 0,
        stock_code: memo.stock_code || ''
      };
      dialogs.file = true;
      break;
    case 'share':
      forms.share = {
        text: memo.text,
        link: memo.link || '',
        desc: memo.desc || '',
        icon: memo.icon || '',
        stock_code: memo.stock_code || ''
      };
      dialogs.share = true;
      break;
  }
}

// 处理删除备忘录
function handleDelete(memo: Memo) {
  memoToDelete.value = memo;
  dialogs.deleteConfirm = true;
}

// 确认删除
async function confirmDelete() {
  if (!memoToDelete.value) return;
  await action.submit(
    apiService.memo.delete,
    {
      id: memoToDelete.value.id
    },
    {
      loading: loading.delete,
      onSuccess: () => {
        dialogs.deleteConfirm = false;
        fetchMemoList();
      }
    }
  );
}

// 处理创建文本备忘
async function handleCreateTextMemo(formData: any) {
  // 更新forms.text为从对话框传来的数据
  forms.text = formData;

  const apiMethod = currentEditId.value ? apiService.memo.update : apiService.memo.createText;
  const params = currentEditId.value
    ? { id: currentEditId.value, body: { ...forms.text, type: 'text' } }
    : { body: forms.text };

  await action.submit(
    apiMethod,
    params,
    {
      loading: loading.save,
      onSuccess: () => {
        dialogs.text = false;
        fetchMemoList();
      }
    }
  );
}

// 处理创建图片备忘
async function handleCreateImageMemo(formData: any) {
  // 更新forms.image为从对话框传来的数据
  forms.image = formData;

  const apiMethod = currentEditId.value ? apiService.memo.update : apiService.memo.createImage;
  const params = currentEditId.value
    ? { id: currentEditId.value, body: { ...forms.image, type: 'image' } }
    : { body: forms.image };

  await action.submit(
    apiMethod,
    params,
    {
      loading: loading.save,
      onSuccess: () => {
        dialogs.image = false;
        fetchMemoList();
      }
    }
  );
}

// 处理创建文件备忘
async function handleCreateFileMemo(formData: any) {
  // 更新forms.file为从对话框传来的数据
  forms.file = formData;

  const apiMethod = currentEditId.value ? apiService.memo.update : apiService.memo.createFile;
  const params = currentEditId.value
    ? { id: currentEditId.value, body: { ...forms.file, type: 'file' } }
    : { body: forms.file };

  await action.submit(
    apiMethod,
    params,
    {
      loading: loading.save,
      onSuccess: () => {
        dialogs.file = false;
        fetchMemoList();
      }
    }
  );
}

// 处理创建分享备忘
async function handleCreateShareMemo() {
  const apiMethod = currentEditId.value ? apiService.memo.update : apiService.memo.createShare;
  const params = currentEditId.value
    ? { id: currentEditId.value, body: { ...forms.share, type: 'share' } }
    : { body: forms.share };

  await action.submit(
    apiMethod,
    params,
    {
      loading: loading.save,
      onSuccess: () => {
        dialogs.share = false;
        fetchMemoList();
      }
    }
  );
}

// 处理图片描述编辑
function handleEditImageDesc(memo: Memo) {
  if (memo.type !== 'image' || !memo.file_key) return;

  preview.imageUrl = memo.signed_url || getFileUrl(memo.file_key);
  preview.text = memo.text || '';
  preview.editText = memo.text || '';
  preview.isEditing = false;
  preview.memoId = memo.id;

  dialogs.imagePreview = true;
}

// 保存图片描述
async function handleSaveImageDesc() {
  if (!preview.memoId) return;
  await action.submit(
    apiService.memo.update,
    {
      id: preview.memoId,
      body: {
        type: 'image',
        text: preview.editText
      }
    },
    {
      loading: loading.save,
      onSuccess: () => {
        preview.text = preview.editText;
        preview.isEditing = false;
        // 关闭对话框
        dialogs.imagePreview = false;
        // 刷新列表
        fetchMemoList();
      }
    }
  );
}

// 获取文件URL
function getFileUrl(fileKey?: string) {
  if (!fileKey) return '';
  return `${FILE_SERVER_PREFIX}/${fileKey}`;
}

// 获取文件名
function getFileName(fileKey?: string) {
  if (!fileKey) return '';
  return fileKey.split('/').pop() || fileKey;
}

// 获取文件扩展名
function getFileExtension(fileKey?: string) {
  if (!fileKey) return '';
  const fileName = fileKey.split('/').pop() || '';
  const ext = fileName.split('.').pop() || '';
  return ext.toLowerCase();
}

// 格式化日期
function formatDate(dateStr: string) {
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm');
}

// 格式化URL
function formatUrl(url?: string) {
  if (!url) return '';
  try {
    const urlObj = new URL(url);
    return urlObj.hostname + urlObj.pathname;
  } catch (e) {
    return url;
  }
}

// 根据文件名获取文件图标
function getFileIcon(fileKey: string) {
  if (!fileKey) return FileIcon;

  const ext = fileKey.split('.').pop()?.toLowerCase() || '';

  switch (ext) {
    // PDF文件
    case 'pdf':
      return FileTextIcon;

    // 图片文件
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
    case 'webp':
      return ImageIcon;

    // 文档文件
    case 'doc':
    case 'docx':
    case 'txt':
    case 'rtf':
    case 'md':
      return FileTextIcon;

    // 压缩文件
    case 'zip':
    case 'rar':
    case '7z':
    case 'tar':
    case 'gz':
      return FileIcon;

    // 音频文件
    case 'mp3':
    case 'wav':
    case 'ogg':
    case 'flac':
    case 'm4a':
      return FileIcon;

    // 视频文件
    case 'mp4':
    case 'avi':
    case 'mov':
    case 'wmv':
    case 'mkv':
    case 'flv':
      return VideoIcon;

    // 表格文件
    case 'xls':
    case 'xlsx':
    case 'csv':
      return FileTextIcon;

    // 应用程序
    case 'exe':
    case 'msi':
    case 'app':
    case 'apk':
      return AppsIcon;

    // 代码文件
    case 'js':
    case 'ts':
    case 'html':
    case 'css':
    case 'py':
    case 'java':
    case 'c':
    case 'cpp':
    case 'php':
    case 'go':
    case 'json':
    case 'xml':
      return CodeIcon;

    // 默认图标
    default:
      return FileIcon;
  }
}

// 根据文件名获取图标颜色类名
function getFileIconColorClass(fileKey: string): string {
  if (!fileKey) return 'file-icon-default';

  const ext = fileKey.split('.').pop()?.toLowerCase() || '';

  switch (ext) {
    // PDF文件
    case 'pdf':
      return 'file-icon-pdf';

    // 图片文件
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
    case 'webp':
      return 'file-icon-image';

    // 文档文件
    case 'doc':
    case 'docx':
    case 'txt':
    case 'rtf':
    case 'md':
      return 'file-icon-doc';

    // 压缩文件
    case 'zip':
    case 'rar':
    case '7z':
    case 'tar':
    case 'gz':
      return 'file-icon-zip';

    // 音频文件
    case 'mp3':
    case 'wav':
    case 'ogg':
    case 'flac':
    case 'm4a':
      return 'file-icon-audio';

    // 视频文件
    case 'mp4':
    case 'avi':
    case 'mov':
    case 'wmv':
    case 'mkv':
    case 'flv':
      return 'file-icon-video';

    // 表格文件
    case 'xls':
    case 'xlsx':
    case 'csv':
      return 'file-icon-excel';

    // 应用程序
    case 'exe':
    case 'msi':
    case 'app':
    case 'apk':
      return 'file-icon-app';

    // 代码文件
    case 'js':
    case 'ts':
    case 'html':
    case 'css':
    case 'py':
    case 'java':
    case 'c':
    case 'cpp':
    case 'php':
    case 'go':
    case 'json':
    case 'xml':
      return 'file-icon-code';

    // 默认图标
    default:
      return 'file-icon-default';
  }
}

// 格式化文件大小
function formatFileSize(size: number | undefined) {
  if (!size) return '0 B';

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let i = 0;
  let fileSize = size;

  while (fileSize >= 1024 && i < units.length - 1) {
    fileSize /= 1024;
    i++;
  }

  return `${fileSize.toFixed(2)} ${units[i]}`;
}

// 获取类型图标
function getTypeIcon(type: string) {
  switch (type) {
    case 'text': return FileTextIcon;
    case 'image': return ImageIcon;
    case 'file': return FileIcon;
    case 'share': return LinkIcon;
    default: return FileTextIcon;
  }
}

// 获取卡片类名
function getCardClass(type: string) {
  return {
    'text-memo': type === 'text',
    'image-memo': type === 'image',
    'file-memo': type === 'file',
    'share-memo': type === 'share'
  };
}

// 处理AI分析
function handleAnalyze(memo: Memo) {
  // 使用函数式调用确认对话框
  Modal.confirm({
    title: '确认AI分析',
    content: h('div', {}, [
      h('p', {}, `确定要对备忘录 "${memo.text?.substring(0, 20)}${memo.text && memo.text.length > 20 ? '...' : ''}" 进行AI分析吗？`),
      h('p', {}, '注意：分析过程可能需要一定时间，请耐心等待。')
    ]),
    okText: '开始分析',
    cancelText: '取消',
    // 不使用loading状态，以便立即关闭对话框
    onOk: () => {
      // 点击确认后立即开始分析，不等待对话框关闭
      action.submit(
        apiService.memo.analyze,
        {
          id: memo.id
        },
        {
          // 不使用loading参数，手动管理loading状态
          showSubmitNotify: true,
          submitNotifyTitle: '开始分析',
          submitNotifyMsg: 'AI分析已启动，分析完成后将自动更新备忘录信息',
          onSuccess: () => {
            // 分析已启动，不需要显示结果
            // 刷新视频列表，以便在分析完成后显示更新的信息
            // notificationService.addNotification({
            //   message: `备忘录[${memo.id}]的AI分析完成`,
            //   from: 'Memo',
            //   type: 'success'
            // });
          }
        }
      );
      // 返回true表示对话框可以关闭
      return Promise.resolve(true);
    }
  });
}



</script>


<style lang="less" scoped>
.view-container {
  height: 100%;
  width: 100%;
  position: relative;
  padding: 0;
}

.search-with-refresh {
  position: relative;
}

.refresh-btn {
  margin-left: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.3s;
}

.record-count {
  font-size: 14px;
  color: var(--text-color-secondary);
}

.menu-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-input {
  width: 300px;
  margin-right: 16px;
}

.ant-segmented {
  background-color: #e1e5ea;
  html.dark & {
    background-color: #26282b;
  }
  :deep(.ant-segmented-item-selected){
    background-color: #006be6;
    color: #fff;
  }
  html.dark & :deep(.ant-segmented-item-selected) {
    background-color: #45494e;
    color: rgb(242, 242, 243);
  }
  :deep(.ant-segmented-item) {
    transition: all 0.3s;
    &:hover:not(.ant-segmented-item-selected) {
      background-color: #35373a;
      color: #ffffff;
    }
  }
}

// html.dark .ant-segmented {

// }

.waterfall-container {
  width: 100%;
  padding: 0 16px;
}

.waterfall-wrapper {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 16px;
  margin: 0 auto;

  /* 响应式布局 - 媒体查询 */
  @media (max-width: 1400px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (max-width: 1100px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 768px) {
    grid-template-columns: repeat(1, 1fr);
  }
}

.waterfall-item {
  padding: 16px;
  background: var(--card);
  color: var(--card-foreground);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
}

html.dark .waterfall-item {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
}

.waterfall-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

html.dark .waterfall-item:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.6);
  border-color: hsl(var(--primary) / 0.5);
  transform: translateY(-4px);
}

.memo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 10px;
  position: relative;
  border-bottom: 1px dashed rgba(96, 125, 139, 0.3);
}

html.dark .memo-header {
  border-bottom: 1px solid hsl(var(--border));
  background-color: hsl(var(--accent-lighter));
  margin: -16px -16px 12px -16px;
  padding: 16px 16px 10px 16px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.memo-header :deep(.ant-btn) {
  width: 28px;
  height: 28px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.3s;

  &:hover {
    background-color: var(--secondary);
    transform: scale(1.1);
  }
}

.memo-time {
  color: var(--muted-foreground);
  font-size: 12px;
}

// Popover样式
:global(.memo-text-popover) {
  max-width: 500px !important;
}

:global(.memo-text-popover .ant-popover-inner-content) {
  max-height: 400px;
  overflow-y: auto;
  padding: 0;
}

.memo-text-content {
  padding: 12px;
  max-height: 400px;
  overflow-y: auto;

  :deep(.ant-typography) {
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.6;
    margin-bottom: 0;
  }
}

.memo-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.text-content {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
}

.image-content {
  :deep(.ant-image) {
    position: relative;
    width: 100%;
    padding-top: 75%;
    overflow: hidden;
    border-radius: 4px;
    cursor: pointer;

    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    html.dark & {
      background-color: rgba(255, 255, 255, 0.02);
    }
  }

  .image-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    border-radius: 4px;
  }

  .edit-desc-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 10;
    background-color: rgba(0, 0, 0, 0.5);
    border: none;

    &:hover {
      background-color: rgba(0, 0, 0, 0.7);
    }
  }

  .image-container:hover .edit-desc-btn {
    opacity: 1;
  }

  .memo-image {
    width: 100%;
    height: auto;
    max-height: 300px;
    object-fit: contain;
    border-radius: 4px;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.02);
    }
  }

  .image-desc {
    margin-top: 8px;
    color: var(--muted-foreground);
    font-size: 14px;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .image-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 8px;
    opacity: 1;
    transition: opacity 0.3s;
    z-index: 10;

    .download-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      color: white;
      font-size: 16px;
      transition: all 0.3s;

      &:hover {
        background-color: rgba(0, 0, 0, 0.7);
        transform: scale(1.1);
      }
    }
  }

  .image-desc {
    margin-top: 8px;
    color: var(--muted-foreground);
    font-size: 14px;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

.file-content {
  .file-card-container {
    padding: 10px 0;
  }

  .file-card {
    background-color: var(--card);
    border-radius: 8px;
    padding: 6px;
    width: 100%;
    max-width: 500px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(223, 216, 216, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .file-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--card-foreground);
    position: relative;
    z-index: 1;
    padding: 4px;
    border-radius: 6px;
    transition: all 0.3s ease;

    html.dark & {
      background-color: rgba(255, 255, 255, 0.03);
    }

    &:hover {
      html.dark & {
        background-color: rgba(255, 255, 255, 0.05);
      }
    }
  }

  .file-icon-wrapper {
    width: 64px;
    height: 64px;
    min-width: 64px;
    border: 2px dashed;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
    border-color: #1890ff;
    background-color: rgba(24, 144, 255, 0.03);
    position: relative;
    z-index: 1;

    html.dark & {
      background-color: rgba(24, 144, 255, 0.1);
      box-shadow: 0 0 8px rgba(24, 144, 255, 0.2);
    }

    &:has(:deep(.vben-icon-file-text)) {
      border-color: #ff4d4f;
      background-color: rgba(255, 77, 79, 0.03);

      html.dark & {
        background-color: rgba(255, 77, 79, 0.1);
        box-shadow: 0 0 8px rgba(255, 77, 79, 0.2);
      }
    }

    &:has(:deep(.vben-icon-image)) {
      border-color: #1890ff;
      background-color: rgba(24, 144, 255, 0.03);

      html.dark & {
        background-color: rgba(24, 144, 255, 0.1);
        box-shadow: 0 0 8px rgba(24, 144, 255, 0.2);
      }
    }

    &:has(:deep(.vben-icon-file-text)) {
      border-color: #52c41a;
      background-color: rgba(82, 196, 26, 0.03);

      html.dark & {
        background-color: rgba(82, 196, 26, 0.1);
        box-shadow: 0 0 8px rgba(82, 196, 26, 0.2);
      }
    }

    &:has(:deep(.vben-icon-file)) {
      border-color: #fa8c16;
      background-color: rgba(250, 140, 22, 0.03);

      html.dark & {
        background-color: rgba(250, 140, 22, 0.1);
        box-shadow: 0 0 8px rgba(250, 140, 22, 0.2);
      }
    }

    &:has(:deep(.vben-icon-file)) {
      border-color: #722ed1;
      background-color: rgba(114, 46, 209, 0.03);

      html.dark & {
        background-color: rgba(114, 46, 209, 0.1);
        box-shadow: 0 0 8px rgba(114, 46, 209, 0.2);
      }
    }

    &:has(:deep(.vben-icon-video)) {
      border-color: #eb2f96;
      background-color: rgba(235, 47, 150, 0.03);

      html.dark & {
        background-color: rgba(235, 47, 150, 0.1);
        box-shadow: 0 0 8px rgba(235, 47, 150, 0.2);
      }
    }

    &:has(:deep(.vben-icon-file-text)) {
      border-color: #52c41a;
      background-color: rgba(82, 196, 26, 0.03);

      html.dark & {
        background-color: rgba(82, 196, 26, 0.1);
        box-shadow: 0 0 8px rgba(82, 196, 26, 0.2);
      }
    }

    &:has(:deep(.vben-icon-apps)) {
      border-color: #faad14;
      background-color: rgba(250, 173, 20, 0.03);

      html.dark & {
        background-color: rgba(250, 173, 20, 0.1);
        box-shadow: 0 0 8px rgba(250, 173, 20, 0.2);
      }
    }

    &:has(:deep(.vben-icon-code)) {
      border-color: #13c2c2;
      background-color: rgba(19, 194, 194, 0.03);

      html.dark & {
        background-color: rgba(19, 194, 194, 0.1);
        box-shadow: 0 0 8px rgba(19, 194, 194, 0.2);
      }
    }
  }

  .file-icon {
    font-size: 32px;

    :deep(svg) {
      width: 32px;
      height: 32px;
    }
  }

  .file-icon-default :deep(svg) {
    fill: #1890ff;
  }

  .file-icon-pdf :deep(svg) {
    fill: #ff4d4f;
  }

  .file-icon-image :deep(svg) {
    fill: #1890ff;
  }

  .file-icon-doc :deep(svg) {
    fill: #52c41a;
  }

  .file-icon-zip :deep(svg) {
    fill: #fa8c16;
  }

  .file-icon-audio :deep(svg) {
    fill: #722ed1;
  }

  .file-icon-video :deep(svg) {
    fill: #eb2f96;
  }

  .file-icon-excel :deep(svg) {
    fill: #52c41a;
  }

  .file-icon-app :deep(svg) {
    fill: #faad14;
  }

  .file-icon-code :deep(svg) {
    fill: #13c2c2;
  }

  .file-info {
    display: flex;
    flex-direction: column;
    max-width: 400px;
    overflow: hidden;
    position: relative;
    z-index: 1;
    padding: 4px 8px;
    border-radius: 4px;

    html.dark & {
      background-color: rgba(255, 255, 255, 0.02);
    }
  }

  .file-name {
    font-size: 14px;
    color: var(--card-foreground);
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 1.4;
    margin-bottom: 4px;
    font-weight: 500;

    html.dark & {
      color: hsl(var(--foreground));
    }
  }

  .file-size {
    font-size: 12px;
    color: var(--muted-foreground);
    padding: 2px 0;

    .file-ext {
      font-weight: 500;
      margin-left: 4px;
      color: var(--primary);

      html.dark & {
        color: hsl(var(--primary));
      }
    }

    html.dark & {
      color: hsl(var(--muted-foreground));
    }
  }
}

.share-content {
  .share-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .share-link-title {
    color: var(--primary);
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .share-desc {
    margin: 8px 0;
    color: var(--muted-foreground);
    white-space: pre-wrap;
    word-break: break-word;
  }

  .share-link {
    margin: 8px 0;
  }

  .share-url {
    color: var(--muted-foreground);
    font-size: 13px;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
      color: var(--primary);
    }
  }
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

// 卡片类型样式
.text-memo {
  border-top: 2px solid #1890ff;
  background-color: rgba(24, 144, 255, 0.05);

  html.dark & {
    background-color: hsl(var(--background));
    border-left: 2px solid #1890ff;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
}

.image-memo {
  border-top: 2px solid #52c41a;
  background-color: rgba(82, 196, 26, 0.05);

  html.dark & {
    background-color: hsl(var(--background));
    border-left: 2px solid #52c41a;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
}

.file-memo {
  border-top: 2px solid #faad14;
  background-color: rgba(250, 173, 20, 0.05);

  html.dark & {
    background-color: hsl(var(--background));
    border-left: 2px solid #faad14;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
}

.share-memo {
  border-top: 2px solid #722ed1;
  background-color: rgba(114, 46, 209, 0.05);

  html.dark & {
    background-color: hsl(var(--background));
    border-left: 2px solid #722ed1;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
}

/* Markdown样式 */
.markdown-content {
  :deep(p) {
    margin-bottom: 8px;
  }

  :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
    margin-top: 16px;
    margin-bottom: 8px;
    font-weight: 600;
  }

  :deep(h1) {
    font-size: 1.5em;
  }

  :deep(h2) {
    font-size: 1.4em;
  }

  :deep(h3) {
    font-size: 1.3em;
  }

  :deep(h4) {
    font-size: 1.2em;
  }

  :deep(h5) {
    font-size: 1.1em;
  }

  :deep(h6) {
    font-size: 1em;
  }

  :deep(ul), :deep(ol) {
    padding-left: 20px;
    margin-bottom: 8px;
  }

  :deep(li) {
    margin-bottom: 4px;
  }

  :deep(a) {
    color: #1890ff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  :deep(code) {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;

    html.dark & {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  :deep(pre) {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 8px;
    border-radius: 4px;
    overflow-x: auto;
    margin-bottom: 8px;

    html.dark & {
      background-color: rgba(255, 255, 255, 0.1);
    }

    code {
      background-color: transparent;
      padding: 0;
    }
  }

  :deep(blockquote) {
    border-left: 4px solid #ddd;
    padding-left: 16px;
    margin-left: 0;
    margin-right: 0;
    color: #666;

    html.dark & {
      border-left-color: #444;
      color: #aaa;
    }
  }

  :deep(table) {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 16px;

    th, td {
      border: 1px solid #ddd;
      padding: 6px 8px;

      html.dark & {
        border-color: #444;
      }
    }

    th {
      background-color: rgba(0, 0, 0, 0.05);
      font-weight: 600;

      html.dark & {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }

    tr:nth-child(even) {
      background-color: rgba(0, 0, 0, 0.02);

      html.dark & {
        background-color: rgba(255, 255, 255, 0.05);
      }
    }
  }
}
</style>
