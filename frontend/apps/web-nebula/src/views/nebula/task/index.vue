<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { Space, Button, Typography, Flex, Tag, Popover } from 'ant-design-vue';
import { RefreshIcon, EyeIcon, MinusIcon, PlusIcon } from '@vben/icons';
import { TaskApi } from '#/api/task';
import { apiService } from '#/services/api';
import { useActionService } from '#/services/action';
import TaskStatusTag from './components/TaskStatusTag.vue';
import TaskExecutionTimeline from './components/TaskExecutionTimeline.vue';
import dayjs from 'dayjs';
import type { VxeGridProps, VxeGridListeners } from '#/adapter/vxe-table';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { VbenIcon } from '@vben-core/shadcn-ui';
import { parseCron, getNextExecutionDescription } from '#/utils/cronParser';


import ViewContainer from '../../../components/ViewContainer.vue';
import ViewBody from '../../../components/ViewBody.vue';

defineOptions({ name: 'TaskList' });

const router = useRouter();
const action = useActionService();

// 组件引用
const dataCardRef = ref(null);

// 任务详情缓存
const taskDetailsCache = ref<Map<string, TaskApi.TaskInfoWithLogs>>(new Map());

// 分页信息
const currentPage = ref(1);
const pageSize = ref(13);

// 解析 schedule 数据的辅助函数
const parseScheduleData = (schedule: any) => {
  // 如果是字符串，尝试解析为JSON
  if (typeof schedule === 'string') {
    // 如果包含空格，可能是直接的cron表达式
    if (schedule.includes(' ') && !schedule.includes('[') && !schedule.includes('{')) {
      return { type: 'cron', value: schedule };
    }

    // 尝试解析JSON字符串
    try {
      const parsed = JSON.parse(schedule);
      return parseScheduleData(parsed); // 递归处理解析后的数据
    } catch (e) {
      // 尝试处理Python格式的字符串（单引号转双引号）
      try {
        // 将Python格式转换为JSON格式
        const jsonString = schedule
          .replace(/'/g, '"')  // 单引号转双引号
          .replace(/True/g, 'true')  // Python True -> JSON true
          .replace(/False/g, 'false')  // Python False -> JSON false
          .replace(/None/g, 'null');  // Python None -> JSON null

        const parsed = JSON.parse(jsonString);
        return parseScheduleData(parsed); // 递归处理解析后的数据
      } catch (e2) {
        return { type: 'interval', value: schedule };
      }
    }
  }

  // 如果是数组，取第一个元素
  if (Array.isArray(schedule) && schedule.length > 0) {
    return parseScheduleData(schedule[0]);
  }

  // 如果是对象，检查是否包含cron字段
  if (typeof schedule === 'object' && schedule !== null) {
    if (schedule.cron) {
      return { type: 'cron', value: schedule.cron };
    }
    if (schedule.interval) {
      return { type: 'interval', value: schedule.interval };
    }
  }

  return null;
};

// 获取任务的 cron 表达式
const getTaskCronExpression = (row: TaskApi.TaskInfo): string | null => {
  if (row.task_type !== TaskApi.TaskType.SCHEDULER) {
    return null;
  }

  const schedule = row.labels?.schedule;
  if (!schedule) {
    return null;
  }

  const parsed = parseScheduleData(schedule);
  return parsed?.type === 'cron' ? parsed.value : null;
};

// 格式化任务类型
const formatTaskType = (taskType?: TaskApi.TaskType) => {
  if (!taskType) {
    return { text: '通用', color: 'default' };
  }

  switch (taskType) {
    case TaskApi.TaskType.SCHEDULER:
      return { text: '计划', color: 'blue' };
    case TaskApi.TaskType.WORKER:
      return { text: '通用', color: 'default' };
    default:
      return { text: '通用', color: 'default' };
  }
};

// 表单配置
const formOptions = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Select',
      componentProps: {
        placeholder: '任务状态',
        options: [
          { label: '全部', value: '' },
          { label: '等待中', value: TaskApi.TaskStatus.PENDING },
          { label: '执行中', value: TaskApi.TaskStatus.RUNNING },
          { label: '成功', value: TaskApi.TaskStatus.SUCCESS },
          { label: '失败', value: TaskApi.TaskStatus.FAILED },
          { label: '重试中...', value: TaskApi.TaskStatus.RETRYING },
          { label: '正等待重试', value: TaskApi.TaskStatus.WAITING_RETRY },
          { label: '已取消', value: TaskApi.TaskStatus.CANCELLED },
        ],
      },
      fieldName: 'status',
      label: '任务状态',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '任务类型',
        options: [
          { label: '全部', value: '' },
          { label: '计划', value: TaskApi.TaskType.SCHEDULER },
          { label: '通用', value: TaskApi.TaskType.WORKER },
        ],
      },
      fieldName: 'task_type',
      label: '任务类型',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '搜索关键词',
      },
      fieldName: 'keywords',
      label: '关键词',
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
  },
  // 是否在字段值改变时提交表单
  submitOnChange: false,
  // 按下回车时是否提交表单
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<TaskApi.TaskInfo> = {
  id: 'task-table-v2',
  columns: [
    { type: 'expand', title: '', width: 50, fixed: 'left', resizable: false, slots: { content: 'expand_content', icon: 'expand_icon' } },
    { title: '序号', field: 'index', width: 50, slots: { default: 'index' } },
    {
      field: 'task_id',
      title: '任务ID',
      sortable: true,
      slots: { default: 'task_id' },
    },
    {
      field: 'task_name',
      title: '任务名称',
      width: 400,
      sortable: true,
      slots: { default: 'task_name' },
    },
    {
      field: 'task_type',
      title: '任务类型',
      width: 100,
      sortable: true,
      slots: { default: 'task_type' },
    },
    {
      field: 'description',
      title: '任务描述',
      sortable: true,
      slots: { default: 'description' },
    },
    {
      field: 'status',
      title: '状态',
      width: 120,
      sortable: true,
      slots: { default: 'status' },
    },
    {
      field: 'next_retry_at',
      title: '下次重试',
      sortable: true,
      slots: { default: 'next_retry_at' },
    },
    {
      field: 'started_at',
      title: '开始时间',
      sortable: true,
      slots: { default: 'started_at' },
    },
    {
      field: 'finished_at',
      title: '完成时间',
      sortable: true,
      slots: { default: 'finished_at' },
    },
    {
      field: 'retry_count',
      title: '重试',
      width: 70,
      sortable: true,
      slots: { default: 'retry_count' },
    },
    {
      field: 'action',
      title: '操作',
      width: 120,
      slots: { default: 'action' },
    },
  ],
  keepSource: true,
  showOverflow: true,
  height: 'auto',
  autoResize: true,
  border:false,
  rowConfig: {
    isHover: true,
  },
  expandConfig: {
    trigger: 'cell',
    lazy: false,
    showIcon: false,
  },
  sortConfig: {
    trigger: 'cell',
    remote: true,
  },
  pagerConfig: {
    pageSize: 12,
    pageSizes: [12, 25, 50, 100],
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    zoom: true,
    // @ts-ignore - vxe-table类型定义不完整
    search: true,
  },
  // @ts-ignore - vxe-table类型定义不完整
  customConfig: {
    storage: true,
    checkMethod: ({ column }) => {
      // 排除序号列和展开列
      return column.field !== 'index' && column.type !== 'expand';
    }
  },
  proxyConfig: {
    ajax: {
      query: async ({ page, sort }, formValues) => {
        // 更新分页信息
        currentPage.value = page.currentPage;
        pageSize.value = page.pageSize;

        const params: TaskApi.TaskListParams = {
          skip: (page.currentPage - 1) * page.pageSize,
          limit: page.pageSize,
        };

        // 添加筛选条件（从表单值获取）
        if (formValues?.status) {
          params.status = formValues.status;
        }
        if (formValues?.task_type) {
          params.task_type = formValues.task_type;
        }
        if (formValues?.keywords) {
          params.keywords = formValues.keywords;
        }

        // 处理排序
        if (sort && sort.field) {
          // 后续可添加排序参数支持
        }

        try {
          const result = await action.submit(
            apiService.task.list,
            params,
            {
              showNotify: false,
            }
          );
          return {
            items: result.items || [],
            total: result.total || 0,
          };
        } catch (error) {
          console.error('获取任务列表失败', error);
          return {
            items: [],
            total: 0,
          };
        }
      },
    },
  },
};

// 表格事件
const gridEvents: VxeGridListeners<TaskApi.TaskInfo> = {
  cellClick: ({ column, row }) => {
    if (column.field === 'action') {
      viewTaskDetail(row.task_id);
    }
  },
  cellDblclick: async ({ row, $table }) => {
    // 双击切换展开/收起执行情况
    const expandRecords = $table.getRowExpandRecords();
    const isExpanded = expandRecords.includes(row);

    if (!isExpanded) {
      // 展开：先获取任务详情，再展开
      await fetchTaskDetail(row.task_id);
      $table.setRowExpand(row, true);
    } else {
      // 收起
      $table.setRowExpand(row, false);
    }
  },
  toggleRowExpand: async ({ row, expanded }) => {
    if (expanded) {
      // 展开时获取任务详情
      await fetchTaskDetail(row.task_id);
    }
  }
};

// 查看任务详情
const viewTaskDetail = (taskId: string) => {
  router.push(`/task/${taskId}`);
};

// 获取任务详情（用于展开行）
const fetchTaskDetail = async (taskId: string) => {
  if (taskDetailsCache.value.has(taskId)) {
    return taskDetailsCache.value.get(taskId);
  }

  try {
    const result = await action.submit(
      apiService.task.get,
      {
        task_id: taskId,
        include_logs: true,
      },
      {
        showNotify: false,
      }
    );

    const taskDetail = result as TaskApi.TaskInfoWithLogs;
    taskDetailsCache.value.set(taskId, taskDetail);
    return taskDetail;
  } catch (error) {
    console.error('获取任务详情失败:', error);
    return null;
  }
};

// 创建表格组件和API
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
  gridEvents,
  showSearchForm: true,
  tableTitle: "任务监控",
});

// 组件挂载时获取任务列表
onMounted(() => {
  gridApi.reload();
});
</script>

<template>
  <Flex vertical class="view-container">
    <ViewContainer ref="dataCardRef" variant="page">
      <!-- 内容区域 -->
      <template #body>
        <ViewBody
          :extraPadding="16"
          :minHeight="300"
        >
          <Grid>
            <template #toolbar-tools>
              <Button
                type="text"
                class="refresh-btn"
                @click="gridApi.reload()"
                title="刷新"
              >
                <template #icon><RefreshIcon /></template>
              </Button>
            </template>

            <!-- 序号列 -->
            <template #index="{ $rowIndex }">
              {{ (currentPage - 1) * pageSize + $rowIndex + 1 }}
            </template>

            <!-- 任务ID列 -->
            <template #task_id="{ row }">
              <Typography.Text
                :copyable="{ text: row.task_id }"
                :ellipsis="true"
                :content="row.task_id"
              />
            </template>

            <!-- 任务名称列 -->
            <template #task_name="{ row }">
              {{ row.task_name.replace('nebula.core.tasks.', '') }}
            </template>

            <!-- 任务类型列 -->
            <template #task_type="{ row }">
              <Popover
                v-if="getTaskCronExpression(row)"
                placement="right"
                trigger="hover"
                :mouseEnterDelay="0.3"
                :mouseLeaveDelay="0.1"
                overlayClassName="schedule-popover-overlay"
              >
                <template #content>
                  <div class="schedule-popover">
                    <div class="schedule-content">
                      <div class="schedule-item">
                        <div class="schedule-label">执行规则</div>
                        <div class="schedule-value">{{ parseCron(getTaskCronExpression(row)!) }}</div>
                      </div>
                      <div class="schedule-item">
                        <div class="schedule-label">下次执行</div>
                        <div class="schedule-value next-time">{{ getNextExecutionDescription(getTaskCronExpression(row)!) }}</div>
                      </div>
                      <div class="schedule-item">
                        <div class="schedule-label">Cron 表达式</div>
                        <div class="schedule-value cron-expression">{{ getTaskCronExpression(row) }}</div>
                      </div>
                    </div>
                  </div>
                </template>
                <Tag :color="formatTaskType(row.task_type).color" class="schedule-tag">
                  {{ formatTaskType(row.task_type).text }}
                </Tag>
              </Popover>
              <Tag v-else :color="formatTaskType(row.task_type).color">
                {{ formatTaskType(row.task_type).text }}
              </Tag>
            </template>

            <!-- 任务描述列 -->
            <template #description="{ row }">
              <Typography.Paragraph
                v-if="row.description"
                :ellipsis="{ rows: 2, expandable: true, symbol: '展开' }"
                :title="row.description"
                :content="row.description"
                style="margin: 0;"
              />
              <span v-else>-</span>
            </template>

            <!-- 状态列 -->
            <template #status="{ row }">
              <TaskStatusTag :status="row.status" />
            </template>

            <!-- 下次重试列 -->
            <template #next_retry_at="{ row }">
              <template v-if="row.status === TaskApi.TaskStatus.WAITING_RETRY && row.next_retry_at">
                {{ dayjs(row.next_retry_at).format('MM-DD HH:mm:ss') }}
              </template>
              <template v-else>-</template>
            </template>

            <!-- 开始时间列 -->
            <template #started_at="{ row }">
              <template v-if="row.started_at">
                {{ dayjs(row.started_at).format('MM-DD HH:mm:ss') }}
              </template>
              <template v-else>-</template>
            </template>

            <!-- 完成时间列 -->
            <template #finished_at="{ row }">
              <template v-if="row.finished_at">
                {{ dayjs(row.finished_at).format('MM-DD HH:mm:ss') }}
              </template>
              <template v-else>-</template>
            </template>

            <!-- 重试次数列 -->
            <template #retry_count="{ row }">
              <template v-if="row.status === TaskApi.TaskStatus.WAITING_RETRY && row.retry_count > 0">
                {{ row.retry_count - 1 }}
              </template>
              <template v-else>{{ row.retry_count }}</template>
            </template>

            <!-- 操作列 -->
            <template #action="{ row }">
              <Space>
                <Button type="link" size="small" @click="viewTaskDetail(row.task_id)" title="查看详情">
                  <EyeIcon />
                </Button>
              </Space>
            </template>

            <!-- 展开图标 -->
            <template #expand_icon="{ row, $table }">
              <div class="flex items-center justify-center w-full h-full">
              <VbenIcon
                :icon="$table.getRowExpandRecords().includes(row) ? 'uil:minus-circle' : 'uil:plus-circle'"
                class="transition-transform duration-800 rotate-90 text-gray-500"
                :class="{ 'rotate-0': $table.getRowExpandRecords().includes(row) }"
                style="cursor: pointer; font-size: 18px;"
              />
              </div>

            </template>

            <!-- 展开行内容 -->
            <template #expand_content="{ row }">
              <div class="expand-content">
                <div class="expand-header">
                  <h3 class="expand-title">执行情况</h3>
                </div>
                <TaskExecutionTimeline
                  :task-id="row.task_id"
                />
              </div>
            </template>
          </Grid>
        </ViewBody>
      </template>
    </ViewContainer>
  </Flex>
</template>

<style lang="less" scoped>
.view-container {
  height: 100%;
  width: 100%;
}

.refresh-btn {
  padding: 4px 8px;
}

// 增强 vxe-table 与父容器的层次感
:deep(.vxe-grid) {
  border: 0.5px solid var(--border-color, #e5e7eb);
  border-radius: 6px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  background: var(--component-background, #ffffff);
  overflow: hidden;
}

.expand-content {
  // background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  margin: 20px 26px 18px 26px;
  padding: 0;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  backdrop-filter: blur(8px);
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 6px 0 0 6px;
    box-shadow: 0 0 6px rgba(59, 130, 246, 0.3);
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 12px;
    pointer-events: none;
  }
}

.expand-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 8px 20px;
  position: relative;
  z-index: 1;
}

.expand-title {
  margin: 0;
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 6px;

  &::before {
    content: '';
    width: 3px;
    height: 12px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 2px;
  }
}

/* 计划任务 Tag 样式 */
.schedule-tag {
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.schedule-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

/* 计划规则 Popover 样式 */
.schedule-popover {
  min-width: 200px;
  max-width: 240px;
  padding: 0;
  border-radius: 6px;
  overflow: hidden;
  background: white;
  border: 1px solid #e2e8f0;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.schedule-content {
  padding: 12px;
}

.schedule-item {
  display: flex;
  flex-direction: column;
  gap: 3px;
  margin-bottom: 8px;
}

.schedule-item:last-child {
  margin-bottom: 0;
}

.schedule-label {
  font-size: 11px;
  font-weight: 500;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.schedule-value {
  font-size: 13px;
  font-weight: 500;
  color: #1e293b;
  line-height: 1.3;
  padding: 4px 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.schedule-value.next-time {
  color: #059669;
  background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
  border-color: #a7f3d0;
}

.schedule-value.cron-expression {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
  font-size: 11px;
  color: #7c3aed;
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
  border-color: #c4b5fd;
}

/* 全局 Popover 样式 */
:global(.schedule-popover-overlay) {
  z-index: 1050;
}

:global(.schedule-popover-overlay .ant-popover-content) {
  padding: 0;
}

:global(.schedule-popover-overlay .ant-popover-inner) {
  padding: 0;
  border-radius: 6px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}
</style>