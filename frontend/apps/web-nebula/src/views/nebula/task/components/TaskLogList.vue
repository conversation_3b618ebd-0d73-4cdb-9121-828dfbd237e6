<script lang="ts" setup>
import { ref, computed, watch, h, reactive } from 'vue';
import { Table, Tag, Typography, Select, Input, Space, Button } from 'ant-design-vue';
import { TaskApi } from '#/api/task';
import { SearchIcon, RefreshIcon } from '@vben/icons';
import dayjs from 'dayjs';

defineOptions({ name: 'TaskLogList' });

const props = defineProps<{
  logs: TaskApi.TaskLog[];
  loading?: boolean;
}>();

const emit = defineEmits<{
  (e: 'refresh'): void;
}>();

// 日志级别筛选
const selectedLevel = ref<TaskApi.LogLevel | ''>('');
// 日志内容搜索
const searchText = ref('');

// 分页状态
const pagination = reactive({
  current: 1,
  pageSize: 10,
});

// 表格列定义
const columns = [
  {
    title: '时间',
    dataIndex: 'time',
    key: 'time',
    width: 200, // 增加宽度以容纳更长的时间字符串
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('HH:mm:ss.SSS') : '-';
    },
  },
  {
    title: '级别',
    dataIndex: 'level',
    key: 'level',
    width: 100,
    customRender: ({ text }: { text: TaskApi.LogLevel }) => {
      let color = 'default';
      switch (text) {
        case 'DEBUG':
          color = 'cyan';
          break;
        case 'INFO':
          color = 'blue';
          break;
        case 'WARNING':
          color = 'warning';
          break;
        case 'ERROR':
          color = 'error';
          break;
      }
      return h(Tag, { color }, { default: () => text });
    },
  },
  {
    title: '重试索引',
    dataIndex: 'retry_index',
    key: 'retry_index',
    width: 100,
  },
  {
    title: '步骤',
    dataIndex: 'step',
    key: 'step',
    width: 150,
    customRender: ({ text }: { text: string }) => {
      // 处理特殊的step值
      if (!text || text === 'N/A' || text === 'null' || text === 'undefined') {
        return '-';
      }
      return text;
    },
  },
  {
    title: '消息',
    dataIndex: 'message',
    key: 'message',
    customRender: ({ text }: { text: string }) => {
      return h(
        Typography.Paragraph,
        {
          ellipsis: { rows: 3, expandable: true, symbol: '展开' },
          style: { marginBottom: '0' }
        },
        { default: () => text }
      );
    },
  },
];

// 筛选后的日志
const filteredLogs = computed(() => {
  // 确保logs是数组
  let result = Array.isArray(props.logs) ? [...props.logs] : [];

  // 确保每条日志记录都有必要的字段
  result = result.map(log => {
    // 兼容处理：如果有timestamp字段但没有time字段，则使用timestamp字段的值
    const timeValue = log.time || log.timestamp || new Date().toISOString();

    return {
      id: log.id || `${log.task_id || ''}-${log.retry_index || 0}-${timeValue}`,
      task_id: log.task_id || '',
      retry_index: log.retry_index || 0,
      order_no: log.order_no || 0, // 添加 order_no 字段
      time: timeValue,
      level: log.level || 'INFO',
      message: log.message || '',
      // 处理特殊的step值
      step: (log.step === 'N/A' || log.step === 'null' || log.step === 'undefined') ? '' : (log.step || ''),
      extra: log.extra || {}
    };
  });

  // 按级别筛选
  if (selectedLevel.value) {
    result = result.filter(log => log.level === selectedLevel.value);
  }

  // 按内容搜索
  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase();
    result = result.filter(log => {
      const messageMatch = log.message.toLowerCase().includes(searchLower);
      // 处理特殊的step值
      const stepMatch = log.step &&
                       log.step !== 'N/A' &&
                       log.step !== 'null' &&
                       log.step !== 'undefined' &&
                       log.step.toLowerCase().includes(searchLower);
      return messageMatch || stepMatch;
    });
  }

  // 按 order_no 排序（如果存在），否则按时间排序
  return result.sort((a, b) => {
    // 优先使用 order_no 字段排序
    if (a.order_no !== undefined && b.order_no !== undefined) {
      return b.order_no - a.order_no; // 降序排列，最新的日志在前面
    }

    // 如果没有 order_no 字段，则按时间排序
    const timeA = new Date(a.time).getTime();
    const timeB = new Date(b.time).getTime();
    return timeB - timeA; // 降序排列，最新的日志在前面
  });
});

// 刷新日志
const handleRefresh = () => {
  emit('refresh');
};

// 处理分页变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
};
</script>

<template>
  <div>
    <div class="mb-4 flex justify-between">
      <Space>
        <Select
          v-model:value="selectedLevel"
          placeholder="日志级别"
          style="width: 120px"
          allow-clear
        >
          <Select.Option value="">全部</Select.Option>
          <Select.Option value="DEBUG">DEBUG</Select.Option>
          <Select.Option value="INFO">INFO</Select.Option>
          <Select.Option value="WARNING">WARNING</Select.Option>
          <Select.Option value="ERROR">ERROR</Select.Option>
        </Select>
        <Input
          v-model:value="searchText"
          placeholder="搜索日志内容"
          style="width: 200px"
          allow-clear
        >
          <template #prefix>
            <SearchIcon />
          </template>
        </Input>
      </Space>
      <Button type="primary" @click="handleRefresh" :loading="props.loading">
        <template #icon><RefreshIcon /></template>
        刷新
      </Button>
    </div>
    <Table
      :columns="columns"
      :data-source="filteredLogs"
      :pagination="{
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: filteredLogs.length,
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '50', '100'],
        showTotal: (total) => `共 ${total} 条记录`
      }"
      :row-key="(record) => record.id || `${record.task_id}-${record.retry_index}-${record.time}`"
      size="small"
      :loading="props.loading"
      @change="handleTableChange"
    />
  </div>
</template>
