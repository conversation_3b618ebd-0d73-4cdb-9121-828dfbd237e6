<script lang="ts" setup>
import { computed } from 'vue';
import { Tag } from 'ant-design-vue';
import { TaskApi } from '#/api/task';

defineOptions({ name: 'TaskStatusTag' });

const props = defineProps<{
  status: TaskApi.TaskStatus;
}>();

// 根据状态获取颜色
const statusColor = computed(() => {
  switch (props.status) {
    case TaskApi.TaskStatus.PENDING:
      return 'blue';
    case TaskApi.TaskStatus.RUNNING:
      return 'processing';
    case TaskApi.TaskStatus.SUCCESS:
      return 'success';
    case TaskApi.TaskStatus.FAILED:
      return 'error';
    case TaskApi.TaskStatus.RETRYING:
      return 'warning';
    case TaskApi.TaskStatus.WAITING_RETRY:
      return 'orange';  // 使用橙色表示等待重试
    case TaskApi.TaskStatus.CANCELLED:
      return 'default';
    default:
      return 'default';
  }
});

// 根据状态获取显示文本
const statusText = computed(() => {
  switch (props.status) {
    case TaskApi.TaskStatus.PENDING:
      return '等待中';
    case TaskApi.TaskStatus.RUNNING:
      return '执行中';
    case TaskApi.TaskStatus.SUCCESS:
      return '成功';
    case TaskApi.TaskStatus.FAILED:
      return '失败';
    case TaskApi.TaskStatus.RETRYING:
      return '重试中';
    case TaskApi.TaskStatus.WAITING_RETRY:
      return '等待重试';
    case TaskApi.TaskStatus.CANCELLED:
      return '已取消';
    default:
      return '未知';
  }
});
</script>

<template>
  <Tag :color="statusColor">{{ statusText }}</Tag>
</template>
