<script lang="ts" setup>
import { ref, computed, watch, reactive, onMounted, onUnmounted, h } from 'vue';
import { Table, Tag, Button, Space, Select, Input, Divider, Empty, Spin } from 'ant-design-vue';
import { SearchIcon, RefreshIcon } from '@vben/icons';
import { TaskApi } from '#/api/task';
import { apiService } from '#/services/api';
import { useActionService } from '#/services/action';
import TaskStatusTag from './TaskStatusTag.vue';
import dayjs from 'dayjs';

defineOptions({ name: 'TaskExecutionView' });

const props = defineProps<{
  taskId: string;
  attempts: TaskApi.TaskAttempt[];
}>();

const emit = defineEmits<{
  (e: 'refresh'): void;
}>();

// 扩展 TaskLog 类型，添加 type 字段
interface ExtendedTaskLog extends TaskApi.TaskLog {
  type?: 'separator' | 'text-separator' | 'stars' | 'at-separator' | 'json-data' |
         'contains-separator' | 'contains-stars' | 'contains-at' | 'contains-json' | 'normal';
}

// 状态
const action = useActionService();
const logs = ref<ExtendedTaskLog[]>([]);
const loading = reactive({
  logs: false,
  polling: false,
});
const selectedRetryIndex = ref<number | null>(null);
const selectedLevel = ref<TaskApi.LogLevel | ''>('');
const searchText = ref('');
const autoScroll = ref(true);
const autoRefresh = ref(true);
const refreshInterval = ref(5000); // 5秒刷新一次
const logContainerRef = ref<HTMLElement | null>(null);
const pollingTimer = ref<number | null>(null);
const expandedRowKeys = ref<number[]>([]); // 当前展开的行

// 执行尝试表格列定义
const attemptColumns = [
  {
    title: '重试索引',
    dataIndex: 'retry_index',
    key: 'retry_index',
    width: 100,
  },
  {
    title: '开始时间',
    dataIndex: 'started_at',
    key: 'started_at',
    width: 180,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss.SSS') : '-';
    },
  },
  {
    title: '结束时间',
    dataIndex: 'finished_at',
    key: 'finished_at',
    width: 180,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss.SSS') : '-';
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    customRender: ({ text }: { text: TaskApi.TaskStatus }) => {
      return h(TaskStatusTag, { status: text });
    },
  },
  {
    title: '执行耗时',
    dataIndex: 'elapsed_ms',
    key: 'elapsed_ms',
    width: 120,
    customRender: ({ text }: { text: number }) => {
      if (!text) return '-';
      // 格式化耗时
      if (text < 1000) {
        return `${text}ms`;
      } else if (text < 60000) {
        return `${(text / 1000).toFixed(2)}s`;
      } else {
        const minutes = Math.floor(text / 60000);
        const seconds = ((text % 60000) / 1000).toFixed(2);
        return `${minutes}m ${seconds}s`;
      }
    },
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    customRender: ({ record }: { record: TaskApi.TaskAttempt }) => {
      return h(Button, {
        type: 'link',
        onClick: (e) => {
          e.stopPropagation(); // 阻止事件冒泡
          selectRetryIndex(record.retry_index);
        },
      }, { default: () => '查看日志' });
    },
  },
];

// 排序后的尝试记录
const sortedAttempts = computed(() => {
  if (!props.attempts || !Array.isArray(props.attempts)) {
    return [];
  }
  return [...props.attempts].sort((a, b) => b.retry_index - a.retry_index);
});

// 筛选后的日志
const filteredLogs = computed(() => {
  // 确保logs是数组
  let result = Array.isArray(logs.value) ? [...logs.value] : [];

  // 按重试索引筛选
  if (selectedRetryIndex.value !== null) {
    result = result.filter(log => log.retry_index === selectedRetryIndex.value);
  }

  // 按级别筛选
  if (selectedLevel.value) {
    result = result.filter(log => log.level === selectedLevel.value);
  }

  // 按内容搜索
  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase();
    result = result.filter(log => {
      const messageMatch = log.message.toLowerCase().includes(searchLower);
      const stepMatch = log.step &&
                       log.step !== 'N/A' &&
                       log.step !== 'null' &&
                       log.step !== 'undefined' &&
                       log.step.toLowerCase().includes(searchLower);
      return messageMatch || stepMatch;
    });
  }

  // 处理特殊行：各种分隔线、JSON数据等
  result = result.map(log => {
    const message = log.message || '';

    // 检查是否是纯分隔线（全是破折号的行）
    if (/^-{3,}$/.test(message.trim())) {
      return { ...log, type: 'separator' };
    }

    // 检查是否是带文本的分隔线（如 ------exit_task_logger------）
    if (/^-{3,}[a-zA-Z0-9_]+[^-]*-{3,}$/.test(message.trim())) {
      return { ...log, type: 'text-separator' };
    }

    // 检查是否是星号行（全是星号的行）
    if (/^\*{3,}$/.test(message.trim())) {
      return { ...log, type: 'stars' };
    }

    // 检查是否是波浪线/AT符号行
    if (/^[@]{3,}$/.test(message.trim())) {
      return { ...log, type: 'at-separator' };
    }

    // 检查是否包含分隔线（连续的破折号）
    if (message.includes('--------------------')) {
      return { ...log, type: 'contains-separator' };
    }

    // 检查是否包含星号行（连续的星号）
    if (message.includes('********************')) {
      return { ...log, type: 'contains-stars' };
    }

    // 检查是否包含波浪线/AT符号行
    if (message.includes('@@@@@@@@@@@@@@@@@@@')) {
      return { ...log, type: 'contains-at' };
    }

    // 检查是否是JSON数据（以{开头并以}结尾）
    if ((message.trim().startsWith('{') && message.trim().endsWith('}')) ||
        (message.trim().startsWith("{'") && message.trim().endsWith("'}"))) {
      return { ...log, type: 'json-data' };
    }

    // 检查是否包含JSON数据
    if ((message.includes('{') && message.includes('}')) ||
        (message.includes("{'") && message.includes("'}"))) {
      return { ...log, type: 'contains-json' };
    }

    return { ...log, type: 'normal' };
  });

  // 按 order_no 排序（如果存在），否则按时间排序
  return result.sort((a, b) => {
    // 优先使用 order_no 字段排序
    if (a.order_no !== undefined && b.order_no !== undefined) {
      return a.order_no - b.order_no; // 升序排列，保持日志的原始顺序
    }

    // 如果没有 order_no 字段，则按时间排序（从旧到新，模拟终端输出）
    const timeA = new Date(a.time).getTime();
    const timeB = new Date(b.time).getTime();
    return timeA - timeB;
  });
});

// 获取日志
const fetchLogs = async () => {
  if (!props.taskId) return;

  try {
    loading.logs = true;

    const result = await action.submit(
      apiService.task.getLogs,
      {
        task_id: props.taskId,
        limit: 1000, // 获取更多日志
      },
      {
        showNotify: false,
      }
    );

    if (result && result.items) {
      // 处理日志数据
      const processedLogs = result.items.map((log: any, index: number) => {
        // 兼容处理：如果有timestamp字段但没有time字段，则使用timestamp字段的值
        const timeValue = log.time || log.timestamp || new Date().toISOString();

        return {
          id: log.id || `${log.task_id || ''}-${log.retry_index || 0}-${timeValue}-${index}`,
          task_id: log.task_id || '',
          retry_index: log.retry_index || 0,
          order_no: log.order_no || 0, // 添加 order_no 字段
          time: timeValue,
          level: log.level || 'INFO',
          name: log.name || '', // 添加name字段
          message: log.message || '',
          step: (log.step === 'N/A' || log.step === 'null' || log.step === 'undefined') ? '' : (log.step || ''),
          extra: log.extra || {}
        };
      });

      logs.value = processedLogs;

      // 如果启用了自动滚动，滚动到底部
      if (autoScroll.value) {
        scrollToBottom();
      }
    } else {
      logs.value = [];
    }
  } catch (error) {
    console.error('Error fetching logs:', error);
  } finally {
    loading.logs = false;
  }
};

// 轮询获取最新日志
const startPolling = () => {
  stopPolling(); // 先停止现有的轮询

  if (autoRefresh.value) {
    pollingTimer.value = window.setInterval(() => {
      if (!loading.logs && !loading.polling) {
        loading.polling = true;
        fetchLogs().finally(() => {
          loading.polling = false;
        });
      }
    }, refreshInterval.value);
  }
};

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value !== null) {
    window.clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }
};

// 选择重试索引
const selectRetryIndex = (retryIndex: number) => {
  console.log('选择重试索引:', retryIndex);

  // 设置选中状态
  selectedRetryIndex.value = retryIndex;

  // 获取日志
  fetchLogs();

  // 展开对应的行
  const isExpanded = expandedRowKeys.value.includes(retryIndex);
  if (!isExpanded) {
    expandedRowKeys.value = [retryIndex];
  }
};

// 处理展开/收起事件
const handleExpandChange = (expanded: boolean, record: TaskApi.TaskAttempt) => {
  console.log('展开/收起事件:', expanded, record.retry_index);

  if (expanded) {
    // 如果展开，则设置展开行
    expandedRowKeys.value = [record.retry_index];
    console.log('展开行:', expandedRowKeys.value);

    // 同时选中该行
    selectedRetryIndex.value = record.retry_index;
    fetchLogs();
  } else {
    // 如果收起，则清空展开行
    expandedRowKeys.value = [];
    console.log('收起行:', expandedRowKeys.value);

    // 保持选中状态不变
  }
};

// 获取指定重试索引的日志
const getRetryLogs = (record: TaskApi.TaskAttempt): ExtendedTaskLog[] => {
  return filteredLogs.value.filter(log => log.retry_index === record.retry_index);
};

// 刷新日志
const handleRefresh = () => {
  fetchLogs();
  emit('refresh');
};

// 切换自动刷新
const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value;
  if (autoRefresh.value) {
    startPolling();
  } else {
    stopPolling();
  }
};

// 注意：自动滚动功能已移除，因为日志现在嵌入在表格中

// 滚动到底部
const scrollToBottom = () => {
  if (logContainerRef.value) {
    setTimeout(() => {
      if (logContainerRef.value) {
        logContainerRef.value.scrollTop = logContainerRef.value.scrollHeight;
      }
    }, 100);
  }
};

// 重置筛选条件
const resetFilter = () => {
  selectedLevel.value = '';
  searchText.value = '';
  selectedRetryIndex.value = null;
  expandedRowKeys.value = [];
};

// 注意：renderExpandedRow 函数已移除，改为使用 #expandedRowRender 插槽

// 自动展开第一个有状态的执行记录
const autoExpandFirstAttempt = () => {
  if (props.attempts && props.attempts.length > 0 && selectedRetryIndex.value === null) {
    // 按重试索引从大到小排序，找到第一个有状态的执行记录
    const sortedAttempts = [...props.attempts].sort((a, b) => b.retry_index - a.retry_index);

    // 优先级：失败 > 成功 > 重试中 > 执行中 > 其他
    const statusPriority = {
      'failed': 1,
      'success': 2,
      'retrying': 3,
      'running': 4
    };

    // 找到第一个有优先状态的执行记录
    const priorityAttempt = sortedAttempts.find(attempt =>
      statusPriority[attempt.status as keyof typeof statusPriority]
    );

    const targetAttempt = priorityAttempt || sortedAttempts[0];

    if (targetAttempt) {
      selectedRetryIndex.value = targetAttempt.retry_index;
      // 默认展开这个执行记录
      expandedRowKeys.value = [targetAttempt.retry_index];

      // 获取对应的日志
      fetchLogs();
    }
  }
};

// 监听taskId变化
watch(() => props.taskId, (newId) => {
  if (newId) {
    fetchLogs();
  }
});

// 监听attempts变化
watch(() => props.attempts, (newAttempts) => {
  if (newAttempts && newAttempts.length > 0) {
    autoExpandFirstAttempt();
  }
}, { immediate: true });

// 组件挂载时获取日志
onMounted(() => {
  fetchLogs();
  startPolling();

  // 延迟执行自动展开，确保 props.attempts 已经加载
  setTimeout(() => {
    autoExpandFirstAttempt();
  }, 500);
});

// 组件卸载时停止轮询
onUnmounted(() => {
  stopPolling();
});
</script>

<template>
  <div class="task-execution-view">
    <!-- 执行尝试列表 -->
    <div class="attempts-section">
      <div class="section-header">
        <Space>
          <Select
            v-model:value="selectedLevel"
            placeholder="日志级别"
            style="width: 120px"
            allow-clear
          >
            <Select.Option value="">全部</Select.Option>
            <Select.Option value="DEBUG">DEBUG</Select.Option>
            <Select.Option value="INFO">INFO</Select.Option>
            <Select.Option value="WARNING">WARNING</Select.Option>
            <Select.Option value="ERROR">ERROR</Select.Option>
          </Select>
          <Input
            v-model:value="searchText"
            placeholder="搜索日志内容"
            style="width: 200px"
            allow-clear
          >
            <template #prefix>
              <SearchIcon />
            </template>
          </Input>
          <Button @click="resetFilter">重置</Button>
          <Button
            :type="autoRefresh ? 'primary' : 'default'"
            @click="toggleAutoRefresh"
            title="自动刷新日志"
          >
            自动刷新
          </Button>
          <Button
            type="primary"
            @click="handleRefresh"
            :loading="loading.logs || loading.polling"
            title="手动刷新日志"
          >
            <template #icon><RefreshIcon /></template>
            刷新
          </Button>
        </Space>
      </div>

      <Table
        :columns="attemptColumns"
        :data-source="sortedAttempts"
        :pagination="false"
        :row-key="(record) => record.retry_index"
        size="small"
        :row-class-name="(record) => record.retry_index === selectedRetryIndex ? 'selected-row' : ''"
        :expanded-row-keys="expandedRowKeys"
        @expand="handleExpandChange"
        :expandRowByClick="true"
      >
        <!-- 使用插槽渲染展开内容 -->
        <template #expandedRowRender="{ record }">
          <div class="expanded-logs-container">
            <div class="expanded-logs-header">
              <div class="expanded-logs-title">重试索引 {{ record.retry_index }} 的日志记录</div>
              <div class="expanded-logs-count">共 {{ getRetryLogs(record).length }} 条记录</div>
            </div>
            <div class="expanded-logs-content">
              <template v-for="(log, index) in getRetryLogs(record)" :key="index">
                <!-- 纯分隔线 -->
                <div v-if="log.type === 'separator'" class="log-separator">
                  {{ log.message }}
                </div>

                <!-- 带文本的分隔线 -->
                <div v-else-if="log.type === 'text-separator'" class="log-text-separator">
                  {{ log.message }}
                </div>

                <!-- 星号行 -->
                <div v-else-if="log.type === 'stars'" class="log-stars">
                  {{ log.message }}
                </div>

                <!-- AT符号行 -->
                <div v-else-if="log.type === 'at-separator'" class="log-at-separator">
                  {{ log.message }}
                </div>

                <!-- JSON数据行 -->
                <div v-else-if="log.type === 'json-data'" class="log-json-data">
                  {{ log.message }}
                </div>

                <!-- 包含分隔线的行 -->
                <div v-else-if="log.type === 'contains-separator'" class="log-line log-contains-separator">
                  <span class="log-time">{{ dayjs(log.time).format('HH:mm:ss.SSS') }}</span>
                  <span class="log-level-container">
                    <span :class="['log-level', `level-${log.level.toLowerCase()}`]">[{{ log.level }}]</span>
                    <span v-if="log.name" class="log-name">{{ log.name }}</span>
                  </span>
                  <span class="log-step">[{{ log.step || '-' }}]</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>

                <!-- 包含星号的行 -->
                <div v-else-if="log.type === 'contains-stars'" class="log-line log-contains-stars">
                  <span class="log-time">{{ dayjs(log.time).format('HH:mm:ss.SSS') }}</span>
                  <span class="log-level-container">
                    <span :class="['log-level', `level-${log.level.toLowerCase()}`]">[{{ log.level }}]</span>
                    <span v-if="log.name" class="log-name">{{ log.name }}</span>
                  </span>
                  <span class="log-step">[{{ log.step || '-' }}]</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>

                <!-- 包含AT符号的行 -->
                <div v-else-if="log.type === 'contains-at'" class="log-line log-contains-at">
                  <span class="log-time">{{ dayjs(log.time).format('HH:mm:ss.SSS') }}</span>
                  <span class="log-level-container">
                    <span :class="['log-level', `level-${log.level.toLowerCase()}`]">[{{ log.level }}]</span>
                    <span v-if="log.name" class="log-name">{{ log.name }}</span>
                  </span>
                  <span class="log-step">[{{ log.step || '-' }}]</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>

                <!-- 包含JSON数据的行 -->
                <div v-else-if="log.type === 'contains-json'" class="log-line log-contains-json">
                  <span class="log-time">{{ dayjs(log.time).format('HH:mm:ss.SSS') }}</span>
                  <span class="log-level-container">
                    <span :class="['log-level', `level-${log.level.toLowerCase()}`]">[{{ log.level }}]</span>
                    <span v-if="log.name" class="log-name">{{ log.name }}</span>
                  </span>
                  <span class="log-step">[{{ log.step || '-' }}]</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>

                <!-- 普通日志行 -->
                <div v-else class="log-line">
                  <span class="log-time">{{ dayjs(log.time).format('HH:mm:ss.SSS') }}</span>
                  <span class="log-level-container">
                    <span :class="['log-level', `level-${log.level.toLowerCase()}`]">[{{ log.level }}]</span>
                    <span v-if="log.name" class="log-name">{{ log.name }}</span>
                  </span>
                  <span class="log-step">[{{ log.step || '-' }}]</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>
              </template>
            </div>
            <Empty v-if="getRetryLogs(record).length === 0" description="无日志记录" />
          </div>
        </template>
      </Table>

      <div v-if="loading.logs" class="loading-indicator">
        <Spin />
        <span>加载日志中...</span>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.task-execution-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 600px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    margin: 0;
  }
}

.attempts-section {
  margin-bottom: 16px;
  flex: 1;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;

  span {
    margin-left: 8px;
  }
}

/* 展开行样式 */
.expanded-logs-container {
  padding: 16px;
  background-color: #fdf6e3; /* 浅米色背景，来自plist */
  border: 1px solid #93a1a1; /* 浅灰色边框 */
  margin: 8px 0;
  border-radius: 2px;
}

.expanded-logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #93a1a1; /* 浅灰色边框 */
}

.expanded-logs-title {
  font-weight: bold;
  color: #657b83; /* 深灰蓝色文本，来自plist */
}

.expanded-logs-count {
  color: #93a1a1; /* 浅灰色，来自plist */
  font-size: 12px;
}

.expanded-logs-content {
  max-height: 400px;
  overflow-y: auto;
  padding: 8px 12px;
  background: #fdf6e3;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.3;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #93a1a1 #eee8d5;

  /* Webkit 滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
    background: #eee8d5;
  }

  &::-webkit-scrollbar-track {
    background: #eee8d5;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #93a1a1;
    border-radius: 4px;
    border: 1px solid #eee8d5;

    &:hover {
      background: #839496;
    }

    &:active {
      background: #657b83;
    }
  }
}

.expanded-logs-empty {
  padding: 16px;
  text-align: center;
  color: #93a1a1; /* 浅灰色，来自plist */
  font-style: italic;
}

.terminal-container {
  flex: 1;
  overflow: auto;
  border: 1px solid #93a1a1; /* 浅灰色边框 */
  border-radius: 0; /* 移除圆角 */
  background-color: #fdf6e3; /* 浅米色背景，来自plist */
  padding: 2px; /* 更小的内边距 */
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.3;
  color: #657b83; /* 深灰蓝色文本，来自plist */
  min-height: 300px;
  max-height: 600px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.terminal {
  padding: 1px; /* 更小的内边距 */
}

.log-line {
  white-space: pre-wrap;
  word-break: break-all;
  margin-bottom: 1px; /* 更小的行间距 */
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
  font-weight: 400;
  line-height: 1.1; /* 更小的行高 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.log-time {
  color: #93a1a1;
  margin-right: 8px;
  font-weight: 400;
  min-width: 80px;
  font-size: 11px;
}

.log-level-container {
  display: inline-flex;
  min-width: 120px; /* 更小的最小宽度 */
  margin-right: 6px;
}

.log-level {
  margin-right: 8px;
  min-width: 50px;
  font-weight: normal;
  font-size: 11px;

  &.level-debug {
    color: #2aa198;
  }

  &.level-info {
    color: #859900;
  }

  &.level-warning {
    color: #b58900;
  }

  &.level-error {
    color: #dc322f;
  }
}

.log-name {
  margin-right: 8px;
  min-width: 60px;
  font-weight: normal;
  font-size: 11px;
  color: #268bd2;
}

.log-retry {
  color: #657b83; /* 深灰蓝色，来自plist Foreground Color */
  margin-right: 6px;
}



.log-step {
  color: #657b83; /* 深灰蓝色，来自plist Foreground Color */
  margin-right: 6px;
  font-size: 12px; /* 调整字体大小 */
}

.log-message {
  flex: 1;
  color: #657b83;
  word-break: break-word;
  font-size: 12px;
}

/* 纯分隔线 */
.log-separator {
  color: #93a1a1; /* 浅灰色，来自plist */
  text-align: left;
  padding: 0;
  font-weight: normal;
  margin: 1px 0;
}

/* 带文本的分隔线 */
.log-text-separator {
  color: #93a1a1; /* 浅灰色，来自plist */
  text-align: left;
  padding: 0;
  font-weight: normal;
  margin: 1px 0;
}

/* 星号分隔线 */
.log-stars {
  color: #b58900; /* 黄色，来自plist Ansi 3 Color */
  text-align: left;
  padding: 0;
  font-weight: normal;
  margin: 1px 0;
}

/* AT符号分隔线 */
.log-at-separator {
  color: #2aa198; /* 青色，来自plist Ansi 6 Color */
  text-align: left;
  padding: 0;
  font-weight: normal;
  margin: 1px 0;
}

/* 包含分隔线的行 */
.log-contains-separator {
  .log-message {
    color: #93a1a1; /* 浅灰色，来自plist */
    font-weight: normal;
  }
}

/* 包含星号的行 */
.log-contains-stars {
  .log-message {
    color: #b58900; /* 黄色，来自plist Ansi 3 Color */
    font-weight: normal;
  }
}

/* 包含AT符号的行 */
.log-contains-at {
  .log-message {
    color: #2aa198; /* 青色，来自plist Ansi 6 Color */
    font-weight: normal;
  }
}

/* JSON数据行 */
.log-json-data {
  color: #cb4b16; /* 橙色，来自plist Ansi 9 Color */
  font-weight: normal;
  margin: 1px 0;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 包含JSON数据的行 */
.log-contains-json {
  .log-message {
    color: #cb4b16; /* 橙色，来自plist Ansi 9 Color */
    font-weight: normal;
  }
}



.selected-row {
  background-color: #e6f7ff;
}

:deep(.ant-empty) {
  color: #657b83; /* 深灰蓝色，来自plist Foreground Color */
  margin: 32px 0;

  .ant-empty-image {
    opacity: 0.6;
  }

  .ant-empty-description {
    color: #657b83; /* 深灰蓝色，来自plist Foreground Color */
  }
}

:deep(.ant-spin-container) {
  height: 100%;
}

:deep(.ant-spin) {
  max-height: none;
}

:deep(.ant-table-row) {
  cursor: pointer;
}
</style>