<script lang="ts" setup>
import { computed, h } from 'vue';
import { Table, Typography } from 'ant-design-vue';
import { TaskApi } from '#/api/task';
import TaskStatusTag from './TaskStatusTag.vue';
import dayjs from 'dayjs';

defineOptions({ name: 'TaskAttemptList' });

const props = defineProps<{
  attempts: TaskApi.TaskAttempt[];
}>();

// 表格列定义
const columns = [
  {
    title: '重试索引',
    dataIndex: 'retry_index',
    key: 'retry_index',
    width: 100,
  },
  {
    title: '开始时间',
    dataIndex: 'started_at',
    key: 'started_at',
    width: 180,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
  {
    title: '结束时间',
    dataIndex: 'finished_at',
    key: 'finished_at',
    width: 180,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    customRender: ({ text }: { text: TaskApi.TaskStatus }) => {
      return h(TaskStatusTag, { status: text });
    },
  },
  {
    title: '执行耗时',
    dataIndex: 'elapsed_ms',
    key: 'elapsed_ms',
    width: 120,
    customRender: ({ text }: { text: number }) => {
      if (!text) return '-';
      // 格式化耗时
      if (text < 1000) {
        return `${text}ms`;
      } else if (text < 60000) {
        return `${(text / 1000).toFixed(2)}s`;
      } else {
        const minutes = Math.floor(text / 60000);
        const seconds = ((text % 60000) / 1000).toFixed(2);
        return `${minutes}m ${seconds}s`;
      }
    },
  },
  {
    title: '错误信息',
    dataIndex: 'error_message',
    key: 'error_message',
    customRender: ({ text }: { text: string }) => {
      if (!text) return '-';
      return h(
        Typography.Paragraph,
        {
          ellipsis: { rows: 2, expandable: true, symbol: '展开' },
          style: { marginBottom: '0' }
        },
        { default: () => text }
      );
    },
  },
];

// 排序后的尝试记录
const sortedAttempts = computed(() => {
  if (!props.attempts || !Array.isArray(props.attempts)) {
    return [];
  }
  return [...props.attempts].sort((a, b) => b.retry_index - a.retry_index);
});
</script>

<template>
  <div>
    <Table
      :columns="columns"
      :data-source="sortedAttempts"
      :pagination="false"
      :row-key="(record) => record.retry_index"
      size="small"
    />
  </div>
</template>
