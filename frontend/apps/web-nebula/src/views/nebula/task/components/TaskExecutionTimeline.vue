<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { Spin, Empty } from 'ant-design-vue';
import { TaskApi } from '#/api/task';
import { apiService } from '#/services/api';
import { useActionService } from '#/services/action';
import TaskStatusTag from './TaskStatusTag.vue';
import dayjs from 'dayjs';

defineOptions({ name: 'TaskExecutionTimeline' });

const props = defineProps<{
  taskId: string;
  attempts?: TaskApi.TaskAttempt[];
}>();

const action = useActionService();

// 状态管理
const loading = ref(false);
const taskDetail = ref<TaskApi.TaskInfoWithLogs | null>(null);
const selectedRetryIndex = ref<number | null>(null);

// 计算属性：排序后的执行尝试
const sortedAttempts = computed(() => {
  const attempts = taskDetail.value?.attempts || props.attempts || [];
  return [...attempts].sort((a, b) => b.retry_index - a.retry_index); // 最新的在前
});

// 计算属性：选中的重试记录的日志
const selectedLogs = computed(() => {
  if (selectedRetryIndex.value === null) return [];
  return getAllLogs(selectedRetryIndex.value);
});

// 获取任务详情（包含执行尝试和日志）
const fetchTaskDetail = async () => {
  if (!props.taskId) return;

  try {
    loading.value = true;
    const result = await action.submit(
      apiService.task.get,
      {
        task_id: props.taskId,
        include_logs: true,
      },
      {
        showNotify: false,
      }
    );
    taskDetail.value = result as TaskApi.TaskInfoWithLogs;
    // 默认选中第一个重试记录
    if (sortedAttempts.value.length > 0 && sortedAttempts.value[0]) {
      selectedRetryIndex.value = sortedAttempts.value[0].retry_index;
    }
  } catch (error) {
    console.error('获取任务详情失败:', error);
  } finally {
    loading.value = false;
  }
};

// 格式化执行耗时
const formatDuration = (elapsedMs: number) => {
  if (!elapsedMs) return '-';
  if (elapsedMs < 1000) {
    return `${elapsedMs}ms`;
  } else if (elapsedMs < 60000) {
    return `${(elapsedMs / 1000).toFixed(2)}s`;
  } else {
    const minutes = Math.floor(elapsedMs / 60000);
    const seconds = ((elapsedMs % 60000) / 1000).toFixed(2);
    return `${minutes}m ${seconds}s`;
  }
};

// 选择重试记录
const selectRetryIndex = (retryIndex: number) => {
  selectedRetryIndex.value = retryIndex;
};

// 检查是否选中
const isSelected = (retryIndex: number) => {
  return selectedRetryIndex.value === retryIndex;
};

// 获取状态对应的颜色
const getStatusColor = (status: TaskApi.TaskStatus) => {
  switch (status) {
    case TaskApi.TaskStatus.SUCCESS:
      return '#52c41a'; // 绿色
    case TaskApi.TaskStatus.FAILED:
      return '#ff4d4f'; // 红色
    case TaskApi.TaskStatus.RUNNING:
      return '#1890ff'; // 蓝色
    case TaskApi.TaskStatus.PENDING:
      return '#faad14'; // 橙色
    case TaskApi.TaskStatus.RETRYING:
      return '#722ed1'; // 紫色
    case TaskApi.TaskStatus.WAITING_RETRY:
      return '#fa8c16'; // 橙红色
    case TaskApi.TaskStatus.CANCELLED:
      return '#8c8c8c'; // 灰色
    default:
      return '#d9d9d9'; // 默认灰色
  }
};

// 获取状态对应的图标
const getStatusIcon = (status: TaskApi.TaskStatus) => {
  switch (status) {
    case TaskApi.TaskStatus.SUCCESS:
      return '✓';
    case TaskApi.TaskStatus.FAILED:
      return '✗';
    case TaskApi.TaskStatus.RUNNING:
      return '⟳';
    case TaskApi.TaskStatus.PENDING:
      return '⏳';
    case TaskApi.TaskStatus.RETRYING:
      return '↻';
    case TaskApi.TaskStatus.WAITING_RETRY:
      return '⏱';
    case TaskApi.TaskStatus.CANCELLED:
      return '⊘';
    default:
      return '?';
  }
};

// 获取指定重试索引的全部日志
const getAllLogs = (retryIndex: number) => {
  if (!taskDetail.value?.logs) return [];

  return taskDetail.value.logs
    .filter(log => log.retry_index === retryIndex)
    .sort((a, b) => {
      // 首先按order_no排序
      if (a.order_no !== b.order_no) {
        return a.order_no - b.order_no;
      }
      // 如果order_no相同，则按时间排序作为备用
      return new Date(a.time).getTime() - new Date(b.time).getTime();
    });
};

// 格式化重试索引显示文本
const formatRetryIndex = (retryIndex: number) => {
  return retryIndex === 0 ? '首次执行' : `重试 #${retryIndex}`;
};

// 获取卡片的样式类
const getCardClass = (attempt: TaskApi.TaskAttempt) => {
  const classes = [];
  if (isSelected(attempt.retry_index)) {
    classes.push('selected');
  }
  if (attempt.retry_index === 0) {
    classes.push('first-execution');
  }
  if (attempt.status === TaskApi.TaskStatus.SUCCESS) {
    classes.push('success-status');
  }
  return classes;
};

// 处理日志容器的滚轮事件
const handleLogsWheel = (event: WheelEvent) => {
  const target = event.currentTarget as HTMLElement;
  const hasScrollbar = target.scrollHeight > target.clientHeight;

  // 如果没有滚动条，不阻止事件冒泡
  if (!hasScrollbar) {
    return;
  }

  // 如果有滚动条，检查是否已经滚动到边界
  const isAtTop = target.scrollTop === 0;
  const isAtBottom = target.scrollTop + target.clientHeight >= target.scrollHeight;

  // 如果向上滚动且已经在顶部，或向下滚动且已经在底部，不阻止事件冒泡
  if ((event.deltaY < 0 && isAtTop) || (event.deltaY > 0 && isAtBottom)) {
    return;
  }

  // 其他情况阻止事件冒泡
  event.stopPropagation();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchTaskDetail();
});
</script>

<template>
  <div class="task-execution-timeline">
    <Spin :spinning="loading">
      <div v-if="sortedAttempts.length > 0" class="timeline-layout">
        <!-- 左侧时间轴区域 -->
        <div class="timeline-left">
          <div class="timeline">
            <div
              v-for="(attempt, index) in sortedAttempts"
              :key="attempt.retry_index"
              class="timeline-item"
              :class="{
                'is-last': index === sortedAttempts.length - 1,
                'is-selected': isSelected(attempt.retry_index)
              }"
            >
              <!-- 时间轴线条 -->
              <div class="timeline-line" :style="{ backgroundColor: getStatusColor(attempt.status) }"></div>

              <!-- 时间轴节点 -->
              <div
                class="timeline-node"
                :style="{
                  backgroundColor: getStatusColor(attempt.status),
                  borderColor: getStatusColor(attempt.status)
                }"
                @click="selectRetryIndex(attempt.retry_index)"
              >
                <span class="timeline-icon">{{ getStatusIcon(attempt.status) }}</span>
              </div>

              <!-- 时间轴内容 -->
              <div class="timeline-content">
                <div
                  class="timeline-card"
                  :class="getCardClass(attempt)"
                  @click="selectRetryIndex(attempt.retry_index)"
                >
                  <div class="timeline-header">
                    <div class="timeline-title">
                      <span class="retry-index">{{ formatRetryIndex(attempt.retry_index) }}</span>
                      <TaskStatusTag :status="attempt.status" />
                    </div>
                    <div class="timeline-duration">
                      {{ formatDuration(attempt.elapsed_ms || 0) }}
                    </div>
                  </div>

                  <div class="timeline-meta">
                    <div class="meta-item">
                      <span class="meta-label">开始:</span>
                      <span class="meta-value">
                        {{ attempt.started_at ? dayjs(attempt.started_at).format('MM-DD HH:mm:ss') : '-' }}
                      </span>
                    </div>
                  </div>

                  <div class="timeline-end-time">
                    <div class="meta-item">
                      <span class="meta-label">结束:</span>
                      <span class="meta-value">
                        {{ attempt.finished_at ? dayjs(attempt.finished_at).format('MM-DD HH:mm:ss') : '-' }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧日志区域 -->
        <div class="timeline-right">
          <div v-if="selectedRetryIndex !== null" class="logs-panel">
            <div class="logs-header">
              <h4>{{ formatRetryIndex(selectedRetryIndex) }} 执行日志</h4>
              <span class="logs-count">{{ selectedLogs.length }} 条记录</span>
            </div>
            <div class="logs-content" @wheel="handleLogsWheel">
              <div
                v-for="(log, logIndex) in selectedLogs"
                :key="logIndex"
                class="log-item"
              >
                <span class="log-time">{{ dayjs(log.time).format('HH:mm:ss.SSS') }}</span>
                <span :class="['log-level', `level-${log.level.toLowerCase()}`]">
                  [{{ log.level }}]
                </span>
                <span v-if="log.name" class="log-name">[{{ log.name }}]</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
              <Empty v-if="selectedLogs.length === 0" description="无日志记录" size="small" />
            </div>
          </div>
          <div v-else class="logs-placeholder">
            <Empty description="请选择一个重试记录查看日志" size="small" />
          </div>
        </div>
      </div>
      <Empty v-else description="暂无执行记录" size="small" />
    </Spin>
  </div>
</template>

<style lang="less" scoped>
.task-execution-timeline {
  padding: 16px 20px;
  height: 500px;
  overflow: hidden;
  position: relative; /* 确保子元素的定位正确 */
}

.timeline-layout {
  display: flex;
  height: 100%;
  gap: 16px;
}

.timeline-left {
  flex: 0 0 320px;
  overflow-y: auto; /* 改为auto，只在需要时显示滚动条 */
  padding: 2px 0 16px 8px;

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;

  /* Webkit 滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
    background: #f1f5f9;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    border: 1px solid #e2e8f0;

    &:hover {
      background: #94a3b8;
    }

    &:active {
      background: #64748b;
    }
  }
}

.timeline-right {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.timeline {
  position: relative;
  padding-left: 32px;
}

.timeline-item {
  position: relative;
  margin-bottom: 16px;

  &:not(.is-last) .timeline-line {
    height: calc(100% + 16px);
  }

  &.is-last .timeline-line {
    height: 0;
  }
}

.timeline-line {
  position: absolute;
  left: -20px;
  top: 32px;
  width: 2px;
  background-color: #e5e7eb;
  transition: background-color 0.3s ease;
}

.timeline-node {
  position: absolute;
  left: -32px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #e5e7eb;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 2;

  &:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.timeline-icon {
  font-size: 12px;
  font-weight: bold;
  color: #ffffff;
}

.timeline-content {
  margin-left: 16px;
}

.timeline-card {
  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 6px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    border-color: #d1d5db;
  }

  &.success-status {
    background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
    border-color: #b7eb8f;
  }

  &.first-execution {
    .retry-index {
      color: #1677ff;
      font-weight: 700;
    }
  }

  &.selected {
    border-color: #1890ff !important;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  }
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.timeline-title {
  display: flex;
  align-items: center;
  gap: 6px;
}

.retry-index {
  font-weight: 600;
  font-size: 12px;
  color: #374151;
}

.timeline-duration {
  font-size: 11px;
  font-weight: 500;
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.timeline-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.timeline-end-time {
  margin-top: 2px;
  display: flex;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 11px;
}

.meta-label {
  color: #6b7280;
  font-weight: 500;
}

.meta-value {
  color: #374151;
  font-weight: 400;
}

/* 右侧日志面板 */
.logs-panel {
  height: 460px !important; /* 固定高度 */
  display: flex;
  flex-direction: column;
  background: #fdf6e3;
  border-radius: 6px;
  overflow: hidden;
}

.logs-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  background: #eee8d5;
  border-bottom: 1px solid #d3cbb7;
}

.logs-header h4 {
  margin: 0;
  font-size: 12px;
  font-weight: 500;
  color: #586e75;
}

.logs-count {
  font-size: 11px;
  color: #586e75;
}

.logs-content {
  height: 400px !important; /* 强制设置固定高度 */
  max-height: 400px !important;
  overflow-y: auto !important; /* 改为auto，只在需要时显示滚动条 */
  overflow-x: hidden !important;
  padding: 8px 12px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.3;
  background: #fdf6e3;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* 确保滚动行为正确 */
  scroll-behavior: smooth;
  overscroll-behavior: contain; /* 防止滚动传播到父元素 */

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #93a1a1 #eee8d5;

  /* Webkit 滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
    background: #eee8d5;
  }

  &::-webkit-scrollbar-track {
    background: #eee8d5;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #93a1a1;
    border-radius: 4px;
    border: 1px solid #eee8d5;

    &:hover {
      background: #839496;
    }

    &:active {
      background: #657b83;
    }
  }
}

.log-item {
  display: flex;
  margin-bottom: 2px;
  color: #657b83;
  padding: 2px 0;
}

.log-time {
  color: #93a1a1;
  margin-right: 8px;
  font-weight: 400;
  min-width: 80px;
  font-size: 11px;
}

.log-level {
  margin-right: 8px;
  min-width: 50px;
  font-weight: normal;
  font-size: 11px;

  &.level-debug {
    color: #2aa198;
  }

  &.level-info {
    color: #859900;
  }

  &.level-warning {
    color: #b58900;
  }

  &.level-error {
    color: #dc322f;
  }
}

.log-name {
  margin-right: 8px;
  min-width: 60px;
  font-weight: normal;
  font-size: 11px;
  color: #268bd2;
}

.log-message {
  flex: 1;
  color: #657b83;
  word-break: break-word;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timeline-layout {
    flex-direction: column;
    gap: 12px;
  }

  .timeline-left {
    flex: 0 0 200px;
    border-bottom: 1px solid #e5e7eb;
    padding: 12px 0 12px 8px;
  }

  .timeline-right {
    flex: 1;
  }

  .timeline {
    padding-left: 24px;
  }

  .timeline-node {
    left: -24px;
    width: 20px;
    height: 20px;
  }

  .timeline-line {
    left: -14px;
  }

  .timeline-card {
    padding: 4px 6px;
  }

  .meta-item {
    font-size: 10px;
  }

  .timeline-meta {
    gap: 8px;
  }

  .retry-index {
    font-size: 11px;
  }

  .timeline-duration {
    font-size: 10px;
    padding: 1px 4px;
  }

  .logs-content {
    padding: 6px 8px;
    font-size: 11px;
    line-height: 1.2;
  }

  .log-time {
    min-width: 65px;
    font-size: 10px;
    margin-right: 6px;
  }

  .log-level {
    min-width: 40px;
    font-size: 10px;
    margin-right: 6px;
  }

  .log-name {
    min-width: 50px;
    font-size: 10px;
    margin-right: 6px;
  }

  .log-message {
    font-size: 11px;
  }

  .logs-header {
    padding: 6px 8px;
  }

  .logs-header h4 {
    font-size: 12px;
  }

  .logs-count {
    font-size: 10px;
  }
}
</style>
