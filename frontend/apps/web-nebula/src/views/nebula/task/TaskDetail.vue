<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Card, Button, Typography, Empty, Spin, Tag, Tabs } from 'ant-design-vue';
import { ArrowLeftIcon, RefreshIcon } from '@vben/icons';
import { TaskApi } from '#/api/task';
import { apiService } from '#/services/api';
import { useActionService } from '#/services/action';
import TaskStatusTag from './components/TaskStatusTag.vue';
import TaskExecutionView from './components/TaskExecutionView.vue';
import { parseCron, getNextExecutionDescription } from '#/utils/cronParser';
import dayjs from 'dayjs';

defineOptions({ name: 'TaskDetail' });

const route = useRoute();
const router = useRouter();
const action = useActionService();

// 获取任务ID
const taskId = ref(route.params.id as string);

// 任务详情数据
const taskInfo = ref<TaskApi.TaskInfoWithLogs | null>(null);

// 加载状态
const loading = {
  info: ref(false),
};

// 获取任务详情
const fetchTaskInfo = async (includeLogs = true) => {
  if (!taskId.value) return;

  try {
    const result = await action.submit(
      apiService.task.get,
      {
        task_id: taskId.value,
        include_logs: includeLogs,
      },
      {
        loading: loading.info,
        showNotify: false,
      }
    );

    taskInfo.value = result as TaskApi.TaskInfoWithLogs;

    // 任务信息获取成功
  } catch (error) {
    console.error('Error fetching task info:', error);
  }
};



// 返回列表页
const goBack = () => {
  router.push('/task');
};

// 刷新任务详情
const refreshTaskInfo = () => {
  fetchTaskInfo(true);
};



// 格式化JSON显示
const formatJSON = (json: any) => {
  if (!json) return '-';
  try {
    return JSON.stringify(json, null, 2);
  } catch (e) {
    return String(json);
  }
};

// 获取任务重试状态
const getRetryStatus = () => {
  if (!taskInfo.value?.labels) {
    return { text: '禁用', color: 'default' };
  }

  const retryOnError = taskInfo.value.labels.retry_on_error;

  // 如果字段不存在，默认为禁用
  if (retryOnError === undefined || retryOnError === null) {
    return { text: '禁用', color: 'default' };
  }

  // 处理布尔值
  if (typeof retryOnError === 'boolean') {
    return retryOnError
      ? { text: '启用', color: 'success' }
      : { text: '禁用', color: 'default' };
  }

  // 处理字符串值
  if (typeof retryOnError === 'string') {
    const lowerValue = retryOnError.toLowerCase();
    if (lowerValue === 'true' || lowerValue === '1' || lowerValue === 'yes' || lowerValue === 'on') {
      return { text: '启用', color: 'success' };
    } else if (lowerValue === 'false' || lowerValue === '0' || lowerValue === 'no' || lowerValue === 'off') {
      return { text: '禁用', color: 'default' };
    }
  }

  // 处理数字值
  if (typeof retryOnError === 'number') {
    return retryOnError > 0
      ? { text: '启用', color: 'success' }
      : { text: '禁用', color: 'default' };
  }

  // 其他情况默认为禁用
  return { text: '禁用', color: 'default' };
};



// 解析cron表达式为通俗文本（使用新的解析器）
const parseCronExpression = (cron: string): string => {
  return parseCron(cron);
};

// 解析 schedule 数据的辅助函数
const parseScheduleData = (schedule: any) => {

  // 如果是字符串，尝试解析为JSON
  if (typeof schedule === 'string') {
    // 如果包含空格，可能是直接的cron表达式
    if (schedule.includes(' ') && !schedule.includes('[') && !schedule.includes('{')) {
      return { type: 'cron', value: schedule };
    }

    // 尝试解析JSON字符串
    try {
      const parsed = JSON.parse(schedule);
      return parseScheduleData(parsed); // 递归处理解析后的数据
    } catch (e) {
      // 尝试处理Python格式的字符串（单引号转双引号）
      try {
        // 将Python格式转换为JSON格式
        const jsonString = schedule
          .replace(/'/g, '"')  // 单引号转双引号
          .replace(/True/g, 'true')  // Python True -> JSON true
          .replace(/False/g, 'false')  // Python False -> JSON false
          .replace(/None/g, 'null');  // Python None -> JSON null

        const parsed = JSON.parse(jsonString);
        return parseScheduleData(parsed); // 递归处理解析后的数据
      } catch (e2) {
        return { type: 'interval', value: schedule };
      }
    }
  }

  // 如果是数组格式的 schedule（Taskiq 格式）
  if (Array.isArray(schedule) && schedule.length > 0) {
    const scheduleItem = schedule[0];
    if (scheduleItem && scheduleItem.cron) {
      return { type: 'cron', value: scheduleItem.cron };
    }
  }

  // 如果是对象格式
  if (schedule && typeof schedule === 'object' && schedule.cron) {
    console.log('识别为对象cron表达式:', schedule.cron);
    return { type: 'cron', value: schedule.cron };
  }

  // 如果是数字，当作间隔时间处理
  if (typeof schedule === 'number') {
    console.log('识别为数字间隔时间:', schedule);
    return { type: 'interval', value: schedule };
  }

  // 其他情况
  console.log('无法识别的schedule格式:', schedule);
  return { type: 'unknown', value: schedule };
};

// 获取计划任务描述
const getScheduleRule = () => {
  if (taskInfo.value?.task_type !== TaskApi.TaskType.SCHEDULER) {
    return null;
  }

  // 从labels中获取调度规则信息
  const labels = taskInfo.value?.labels;
  if (!labels) return null;

  const schedule = labels.schedule || labels.cron || labels.interval;
  if (!schedule) return null;

  const parsed = parseScheduleData(schedule);

  switch (parsed.type) {
    case 'cron':
      return parseCronExpression(parsed.value);
    case 'interval':
      return `执行间隔: ${parsed.value}`;
    default:
      return `调度规则: ${JSON.stringify(parsed.value)}`;
  }
};

// 获取下次执行时间
const getNextExecutionTime = () => {
  if (taskInfo.value?.task_type !== TaskApi.TaskType.SCHEDULER) {
    return null;
  }

  // 从labels中获取调度规则信息
  const labels = taskInfo.value?.labels;
  if (!labels) return null;

  const schedule = labels.schedule || labels.cron || labels.interval;
  if (!schedule) return null;

  const parsed = parseScheduleData(schedule);

  if (parsed.type === 'cron') {
    try {
      return getNextExecutionDescription(parsed.value);
    } catch (error) {
      console.error('计算下次执行时间失败:', error);
      return '计算失败';
    }
  }

  // 对于间隔时间，暂时不计算下次执行时间
  return null;
};



// 组件挂载时获取任务详情
onMounted(() => {
  fetchTaskInfo(true);
});

// 监听路由参数变化
watch(
  () => route.params.id,
  (newId) => {
    if (newId && newId !== taskId.value) {
      taskId.value = newId as string;
      fetchTaskInfo(true);
    }
  }
);
</script>

<template>
  <div>
    <Card :bordered="false">


      <Spin :spinning="loading.info.value">
        <div v-if="taskInfo" class="task-detail-content">
          <!-- 两列布局 -->
          <div class="two-column-layout">
            <!-- 左列：基本信息、参数和结果 -->
            <div class="left-column">
              <!-- 基本信息 -->
              <div class="task-info-container">
                <div class="task-info-header">
                  <h3>基本信息</h3>
                  <div class="task-id">
                    ID: <Typography.Text copyable>{{ taskInfo.task_id }}</Typography.Text>
                  </div>
                </div>

                <div class="task-info-grid">
                  <div class="task-info-item">
                    <span class="label">任务名称:</span>
                    <span class="value">{{ taskInfo.task_name }}</span>
                  </div>
                  <div class="task-info-item">
                    <span class="label">状态:</span>
                    <span class="value"><TaskStatusTag :status="taskInfo.status" /></span>
                  </div>
                  <div class="task-info-item">
                    <span class="label">重试次数:</span>
                    <span class="value">{{ taskInfo.retry_count }}</span>
                  </div>
                  <div class="task-info-item">
                    <span class="label">任务重试:</span>
                    <span class="value">
                      <Tag :color="getRetryStatus().color">{{ getRetryStatus().text }}</Tag>
                    </span>
                  </div>
                  <div v-if="getScheduleRule()" class="task-info-item">
                    <span class="label">计划任务:</span>
                    <div class="value schedule-info">
                      <div class="schedule-rule">
                        {{ getScheduleRule() }}
                        <span v-if="getNextExecutionTime()" class="next-execution">（{{ getNextExecutionTime() }}）</span>
                      </div>
                    </div>
                  </div>
                  <div class="task-info-item">
                    <span class="label">创建时间:</span>
                    <span class="value">{{ taskInfo.created_at ? dayjs(taskInfo.created_at).format('YYYY-MM-DD HH:mm:ss') : '-' }}</span>
                  </div>
                  <div class="task-info-item">
                    <span class="label">开始时间:</span>
                    <span class="value">{{ taskInfo.started_at ? dayjs(taskInfo.started_at).format('YYYY-MM-DD HH:mm:ss') : '-' }}</span>
                  </div>
                  <div class="task-info-item">
                    <span class="label">完成时间:</span>
                    <span class="value">{{ taskInfo.finished_at ? dayjs(taskInfo.finished_at).format('YYYY-MM-DD HH:mm:ss') : '-' }}</span>
                  </div>
                </div>

                <div class="task-info-row" v-if="taskInfo.description">
                  <span class="label">描述:</span>
                  <div class="value">
                    <Typography.Paragraph
                      :ellipsis="{ rows: 2, expandable: true, symbol: '展开' }"
                      :content="taskInfo.description"
                    />
                  </div>
                </div>

                <div class="task-info-row" v-if="taskInfo.error_message">
                  <span class="label">错误信息:</span>
                  <div class="value">
                    <Typography.Paragraph
                      type="danger"
                      :ellipsis="{ rows: 2, expandable: true, symbol: '展开' }"
                      :content="taskInfo.error_message"
                    />
                  </div>
                </div>
              </div>

              <!-- 参数和结果区域 -->
              <div v-if="taskInfo.params || taskInfo.result" class="params-result-section">
                <Tabs size="small" type="card">
                  <Tabs.TabPane key="params" tab="参数">
                    <div v-if="taskInfo.params" class="json-content">
                      <pre><code>{{ formatJSON(taskInfo.params) }}</code></pre>
                    </div>
                    <Empty v-else description="无参数" size="small" />
                  </Tabs.TabPane>

                  <Tabs.TabPane key="result" tab="结果">
                    <div v-if="taskInfo.result" class="json-content">
                      <pre><code>{{ formatJSON(taskInfo.result) }}</code></pre>
                    </div>
                    <Empty v-else description="无结果" size="small" />
                  </Tabs.TabPane>
                </Tabs>
              </div>
            </div>

            <!-- 右列：执行情况 -->
            <div class="right-column">
              <div class="execution-section">
                <TaskExecutionView
                  :taskId="taskId"
                  :attempts="taskInfo.attempts"
                  @refresh="refreshTaskInfo"
                />
              </div>
            </div>
          </div>
        </div>
        <Empty v-else description="未找到任务信息" />
      </Spin>
    </Card>
  </div>
</template>

<style scoped>
pre {
  background-color: #fdf6e3;
  color: #657b83;
  padding: 16px;
  border-radius: 0;
  border: none;
  overflow: auto;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 任务信息容器 */
.task-info-container {
  background-color: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

/* 任务信息头部 */
.task-info-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 10px;
}

.task-info-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #262626;
}

.task-id {
  font-size: 12px;
  color: #8c8c8c;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
  word-break: break-all;
  line-height: 1.4;
}

/* 任务信息网格布局 */
.task-info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 6px;
  margin-bottom: 8px;
}

/* 任务信息项 */
.task-info-item {
  display: flex;
  align-items: baseline;
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
}

.task-info-item:last-child {
  border-bottom: none;
}

.task-info-row {
  display: flex;
  margin-bottom: 6px;
  padding: 4px 0;
}

.label {
  color: #666;
  font-size: 14px;
  margin-right: 8px;
  white-space: nowrap;
  min-width: 70px;
}

.value {
  color: #333;
  font-size: 14px;
  flex: 1;
  word-break: break-all;
  word-wrap: break-word;
  min-width: 0;
}

.schedule-rule {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
  background: #f0f9ff;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #bae6fd;
  color: #0369a1;
  font-size: 12px;
  display: inline-block;
}

.next-execution {
  font-family: inherit;
  background: transparent;
  padding: 0;
  color: #666;
  font-size: 11px;
  font-weight: normal;
  opacity: 0.8;
  margin-left: 6px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .two-column-layout {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .right-column {
    order: -1; /* 在小屏幕上将执行情况移到上方 */
  }
}

/* 两列布局 */
.task-detail-content {
  height: 100%;
}

.two-column-layout {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 24px;
  height: 100%;
  min-height: 600px;
}

.left-column {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.right-column {
  display: flex;
  flex-direction: column;
}

/* 参数和结果区域 */
.params-result-section {
  flex: 1;
  background: #fff;
  overflow: hidden;
}

.params-result-section :deep(.ant-tabs) {
  height: 100%;
}

.params-result-section :deep(.ant-tabs-content-holder) {
  height: calc(100% - 40px);
}

.params-result-section :deep(.ant-tabs-tabpane) {
  height: 100%;
  padding: 0px;
}

.json-content {
  height: 100%;
  max-height: 280px;
  overflow-y: auto;
  border: none;
  border-radius: 0;
  background: #fdf6e3;

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #93a1a1 #eee8d5;

  /* Webkit 滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
    background: #eee8d5;
  }

  &::-webkit-scrollbar-track {
    background: #eee8d5;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #93a1a1;
    border-radius: 4px;
    border: 1px solid #eee8d5;

    &:hover {
      background: #839496;
    }

    &:active {
      background: #657b83;
    }
  }
}

.json-content pre {
  margin: 0;
  padding: 8px;
  background-color: #fdf6e3;
  color: #657b83;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.4;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 执行与日志区域 */
.execution-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}


</style>
