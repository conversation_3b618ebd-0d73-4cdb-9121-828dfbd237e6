<template>
  <Layout class="cloudflare-container">
    <!-- 域名管理卡片 -->
    <Card title="域名管理" class="domains-card" :bordered="true" :hoverable="true">
      <template #extra>
        <Space>
          <Button type="default" @click="dialogs.accountSettings = true">
            设置账户
          </Button>
          <Button type="primary" @click="syncDomains" :loading="loading.sync.value">
            <template #icon><RefreshIcon /></template>
            同步数据
          </Button>
        </Space>
      </template>

      <Spin :spinning="loading.main.value || loading.sync.value || loading.validate.value">
        <Table
          v-if="domainsList.length > 0"
          :dataSource="domainsList"
          :columns="domainColumns"
          :rowKey="(record: Domain) => record.id"
          size="small"
          :expandedRowKeys="expandedRowKeys"
          @expand="handleExpandChange"
          :pagination="false"
        >
        <!-- 域名状态 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <Tag :color="record.status === 'active' ? 'success' : 'warning'" size="small">
              {{ record.status }}
            </Tag>
          </template>

          <!-- 记录数量 -->
          <template v-if="column.key === 'records'">
            {{ record.dns ? record.dns.length : 0 }}条
          </template>

          <!-- 操作按钮 -->
          <template v-if="column.key === 'operation'">
            <!-- 移动端显示圆形按钮，只有图标 -->
            <Button
              v-if="isMobile"
              type="primary"
              size="small"
              shape="circle"
              @click.stop="showAddRecordDialog(record)"
            >
              <template #icon><UploadIcon /></template>
            </Button>
            <!-- 桌面端显示常规按钮，有图标和文本 -->
            <Button
              v-else
              type="primary"
              size="small"
              @click.stop="showAddRecordDialog(record)"
            >
              <template #icon><UploadIcon /></template>
              添加DNS
            </Button>
          </template>
        </template>

        <!-- DNS记录表格作为展开内容 -->
        <template #expandedRowRender="{ record }">
          <Flex vertical class="expanded-dns-container" style="margin-left: -36px; width: calc(100% + 36px);">
            <Table
              :dataSource="record.dnsRecords || []"
              :loading="loading.records.value"
              :rowKey="(dnsRecord: any) => dnsRecord.sub"
              :columns="dnsColumns"
              size="small"
              :bordered="false"
              :row-class-name="() => ''"
              :pagination="false"
            >
              <!-- 添加sub字段的显示模板 -->
              <template #bodyCell="{ column, record: dnsRecord }">
                <template v-if="column.key === 'sub'">
                  <Tooltip :title="dnsRecord.sub === record.name ? '@' : dnsRecord.sub">
                    <Typography.Text class="name-cell">{{ dnsRecord.sub === record.name ? '@' : dnsRecord.sub }}</Typography.Text>
                  </Tooltip>
                </template>

                <!-- DNS记录类型 -->
                <template v-if="column.key === 'type'">
                  <Tag :color="getRecordTagColor(dnsRecord.type)">{{ dnsRecord.type }}</Tag>
                </template>

                <!-- 代理状态 -->
                <template v-if="column.key === 'proxied'">
                  <Tag
                    :color="dnsRecord.proxied ? 'success' : 'default'"
                    @click="onProxyStatusChange(record, dnsRecord)"
                    :style="{ cursor: dnsRecord.isEditing && !dnsRecord.loading ? 'pointer' : 'default' }"
                  >
                    {{ dnsRecord.proxied ? '已代理' : '未代理' }}
                  </Tag>
                </template>

                <!-- 内容字段 -->
                <template v-if="column.key === 'content'">
                  <template v-if="dnsRecord.isEditing">
                    <Input
                      v-model:value="dnsRecord.content"
                      placeholder="记录内容"
                      class="dns-input"
                      :disabled="dnsRecord.loading"
                    />
                  </template>
                  <template v-else>
                    <Tooltip :title="dnsRecord.content">
                      <Typography.Text class="content-cell">{{ dnsRecord.content }}</Typography.Text>
                    </Tooltip>
                  </template>
                </template>

                <!-- TTL字段 -->
                <template v-if="column.key === 'ttl'">
                  <template v-if="dnsRecord.isEditing">
                    <InputNumber
                      v-model:value="dnsRecord.ttl"
                      :min="1"
                      :step="60"
                      class="dns-number-input"
                      :style="{ width: '100%' }"
                      :disabled="dnsRecord.loading"
                    />
                  </template>
                  <template v-else>
                    {{ dnsRecord.ttl || '-' }}
                  </template>
                </template>

                <!-- 优先级字段 -->
                <template v-if="column.key === 'priority'">
                  <template v-if="dnsRecord.isEditing && dnsRecord.type === 'MX'">
                    <InputNumber
                      v-model:value="dnsRecord.priority"
                      :min="0"
                      :step="1"
                      class="dns-number-input"
                      :style="{ width: '100%' }"
                      :disabled="dnsRecord.loading"
                    />
                  </template>
                  <template v-else>
                    {{ dnsRecord.type === 'MX' ? dnsRecord.priority : '-' }}
                  </template>
                </template>

                <!-- 操作按钮 -->
                <template v-if="column.key === 'operation'">
                  <Space class="dns-operation-buttons">
                    <!-- 编辑按钮 -->
                    <template v-if="!dnsRecord.isEditing">
                      <!-- 移动端显示圆形按钮，只有图标 -->
                      <Button
                        v-if="isMobile"
                        type="primary"
                        size="small"
                        shape="circle"
                        @click="startEditing(dnsRecord)"
                      >
                        <template #icon><EditIcon /></template>
                      </Button>
                      <!-- 桌面端显示常规按钮，有图标和文本 -->
                      <Button
                        v-else
                        type="primary"
                        size="small"
                        @click="startEditing(dnsRecord)"
                      >
                        <template #icon><EditIcon /></template>
                        编辑
                      </Button>

                      <!-- 删除按钮 -->
                      <!-- 移动端显示圆形按钮，只有图标 -->
                      <Button
                        v-if="isMobile"
                        danger
                        size="small"
                        shape="circle"
                        @click="deleteRecord(record, dnsRecord)"
                      >
                        <template #icon><DeleteIcon /></template>
                      </Button>
                      <!-- 桌面端显示常规按钮，有图标和文本 -->
                      <Button
                        v-else
                        danger
                        size="small"
                        @click="deleteRecord(record, dnsRecord)"
                      >
                        <template #icon><DeleteIcon /></template>
                        删除
                      </Button>
                    </template>

                    <template v-else>
                      <!-- 保存按钮 -->
                      <!-- 移动端显示圆形按钮，只有图标 -->
                      <Button
                        v-if="isMobile"
                        type="primary"
                        size="small"
                        shape="circle"
                        @click="saveRecordChange(record, dnsRecord)"
                        :loading="dnsRecord.loading"
                        :disabled="dnsRecord.loading"
                      >
                        <template #icon><SaveIcon /></template>
                      </Button>
                      <!-- 桌面端显示常规按钮，有图标和文本 -->
                      <Button
                        v-else
                        type="primary"
                        size="small"
                        @click="saveRecordChange(record, dnsRecord)"
                        :loading="dnsRecord.loading"
                        :disabled="dnsRecord.loading"
                      >
                        <template #icon><SaveIcon /></template>
                        保存
                      </Button>

                      <!-- 取消按钮 -->
                      <!-- 移动端显示圆形按钮，只有图标 -->
                      <Button
                        v-if="isMobile"
                        size="small"
                        shape="circle"
                        @click="cancelEdit(dnsRecord)"
                      >
                        <template #icon><CloseIcon /></template>
                      </Button>
                      <!-- 桌面端显示常规按钮，有图标和文本 -->
                      <Button
                        v-else
                        size="small"
                        @click="cancelEdit(dnsRecord)"
                      >
                        <template #icon><CloseIcon /></template>
                        取消
                      </Button>
                    </template>
                  </Space>
                </template>
              </template>
            </Table>

            <Empty v-if="!loading.records.value && (!record.dnsRecords || record.dnsRecords.length === 0)" description="暂无DNS记录" />
          </Flex>
        </template>
        </Table>

        <Empty v-else-if="!loading.main.value && !loading.sync.value && !loading.validate.value" description="暂无域名数据，请先同步域名" />
      </Spin>
    </Card>

    <!-- 账户设置抽屉 -->
    <Drawer
      v-model:open="dialogs.accountSettings"
      title="Cloudflare账户设置"
      :placement="drawerPlacement"
      :height="isMobile ? drawerSize : undefined"
      :width="!isMobile ? drawerSize : undefined"
      :maskClosable="false"
      @close="dialogs.accountSettings = false"
    >
      <template #extra>
        <Space>
          <Button @click="dialogs.accountSettings = false">取消</Button>
          <Button type="primary" @click="saveConfig" :disabled="loading.save.value || loading.sync.value || loading.validate.value">保存</Button>
        </Space>
      </template>
      <Flex vertical class="drawer-content" :style="{ padding: isMobile ? '8px 12px' : '16px' }">
        <Spin :spinning="loading.save.value || loading.sync.value || loading.validate.value" tip="正在处理，请等待...">
          <Form
            ref="formRef"
            :model="configForm"
            :rules="rules"
            @finish="onConfigFormSubmit"
            layout="vertical"
            class="drawer-form"
          >
            <Form.Item label="Account ID" name="accountId" required>
              <Input
                v-model:value="configForm.accountId"
                placeholder="请输入Cloudflare账户ID"
                allow-clear
                :disabled="loading.save.value || loading.sync.value || loading.validate.value"
              />
            </Form.Item>

            <Form.Item label="API密钥" name="apiKey" required>
              <Input
                v-model:value="configForm.apiKey"
                type="password"
                placeholder="请输入Cloudflare API密钥"
                allow-clear
                :disabled="loading.save.value || loading.sync.value || loading.validate.value"
              />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="default" @click="validateApiConfig" :disabled="loading.save.value || loading.sync.value || loading.validate.value">
                  验证API
                </Button>
                <Button type="primary" @click="syncDomains" :loading="loading.sync.value" :disabled="loading.save.value || loading.validate.value">
                  同步数据
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Spin>
      </Flex>
    </Drawer>

    <!-- 添加DNS记录抽屉 -->
    <Drawer
      v-model:open="dialogs.addRecord"
      title="添加DNS记录"
      :placement="drawerPlacement"
      :height="isMobile ? drawerSize : undefined"
      :width="!isMobile ? drawerSize : undefined"
      :maskClosable="false"
      destroyOnClose
      @close="cancelDialog"
    >
      <template #extra>
        <Space>
          <Button @click="cancelDialog">取消</Button>
          <Button type="primary" :disabled="loading.addRecord.value" @click="handleAddRecordOk">确定</Button>
        </Space>
      </template>
      <Flex vertical class="drawer-content" :style="{ padding: isMobile ? '8px 12px' : '16px' }">
        <Spin :spinning="loading.addRecord.value" tip="正在提交...">
          <Form
            ref="recordFormRef"
            :model="recordForm"
            :rules="recordRules"
            layout="vertical"
            class="drawer-form"
          >
        <Form.Item label="名称" name="sub" required>
          <Input
            v-model:value="recordForm.sub"
            placeholder="输入子域名(例如: www)"
            allow-clear
            :disabled="loading.addRecord.value"
          />
        </Form.Item>
        <Form.Item label="类型" name="type" required>
          <Select v-model:value="recordForm.type" style="width: 100%" :disabled="loading.addRecord.value">
            <Select.Option value="A">A (指向IPv4地址)</Select.Option>
            <Select.Option value="AAAA">AAAA (指向IPv6地址)</Select.Option>
            <Select.Option value="CNAME">CNAME (指向另一个域名)</Select.Option>
            <Select.Option value="TXT">TXT (文本记录)</Select.Option>
            <Select.Option value="MX">MX (邮件服务器)</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label="内容" name="content" required>
          <Input
            v-model:value="recordForm.content"
            placeholder="IP地址或域名"
            allow-clear
            :disabled="loading.addRecord.value"
          />
        </Form.Item>
        <Form.Item label="TTL" name="ttl" required>
          <InputNumber v-model:value="recordForm.ttl" :min="1" :step="60" class="full-width-input" :disabled="loading.addRecord.value" />
          <Typography.Text class="form-help-text" type="secondary">时间单位：秒，推荐值：60、300、600、900、等</Typography.Text>
        </Form.Item>
        <Form.Item label="优先级" name="priority" v-if="recordForm.type === 'MX'" required>
          <InputNumber v-model:value="recordForm.priority" :min="0" :step="1" class="full-width-input" :disabled="loading.addRecord.value" />
          <Typography.Text class="form-help-text" type="secondary">仅对MX记录有效，值越小优先级越高</Typography.Text>
        </Form.Item>
        <Form.Item label="代理" name="proxied">
          <Switch v-model:checked="recordForm.proxied" :disabled="loading.addRecord.value" />
          <Typography.Text class="form-help-text" type="secondary">开启后将通过Cloudflare代理访问</Typography.Text>
        </Form.Item>
          </Form>
        </Spin>
      </Flex>
    </Drawer>
  </Layout>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed } from 'vue';
import { Card, Space, Button, Table, Tag, Tooltip, Input, InputNumber, Empty, Drawer, Spin, Form, Select, Switch, Modal, Typography, Layout, Flex } from 'ant-design-vue';
import { useActionService } from '#/services/action';
import { apiService } from '#/services/api';
import { notifyUtils } from '#/utils/notify';
import type { FormInstance } from 'ant-design-vue/es/form';
import { useWindowSize } from '@vueuse/core';
import {
  RefreshIcon,
  UploadIcon,
  EditIcon,
  DeleteIcon,
  SaveIcon,
  CloseIcon
} from '@vben/icons';

// 定义接口
interface DnsRecord {
  sub: string;
  type: string;
  content: string;
  ttl: number;
  proxied: boolean;
  priority?: number;
  record_id?: string;
  isEditing?: boolean;
  _originalData?: string;
  loading?: boolean;
}

interface Domain {
  id: string;
  name: string;
  status: string;
  dns: DnsRecord[];
  dnsRecords: DnsRecord[];
}

// 获取action服务
const actionService = useActionService();

// 表单引用
const formRef = ref<FormInstance>();
const recordFormRef = ref<FormInstance>();

// 加载状态 - 使用ref而不是reactive，与原始JavaScript项目保持一致
const loading = {
  save: ref(false),
  main: ref(false),
  sync: ref(false),
  validate: ref(false),
  records: ref(false),
  addRecord: ref(false),
  deleteRecord: ref(false),
  saveRecord: ref(false)
};


// 表单数据
const configForm = reactive({
  accountId: '',
  apiKey: ''
});

// 表单验证规则
const rules = {
  accountId: [{ required: true, message: '请输入账户ID', trigger: 'blur' }],
  apiKey: [{ required: true, message: '请输入API密钥', trigger: 'blur' }]
};

// 域名列表数据
const domainsList = ref<Domain[]>([]);
const expandedRowKeys = ref<string[]>([]);

// 对话框状态
const dialogs = reactive({
  addRecord: false,
  accountSettings: false // 账户设置抽屉状态
});

// DNS记录表单
const recordForm = reactive({
  sub: '',
  type: 'A',
  content: '',
  ttl: 60,
  priority: 10,
  proxied: false
});

// DNS记录表单验证规则
const recordRules = {
  sub: [{ required: true, message: '请输入记录名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择记录类型', trigger: 'blur' }],
  content: [
    { required: true, message: '请输入记录内容', trigger: 'blur' },
    { validator: (_rule: any, value: string) => {
        if (!value || value.trim() === '') {
          return Promise.reject('记录内容不能为空');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  ttl: [
    { required: true, message: 'TTL值不能为空', trigger: 'blur' },
    { validator: (_rule: any, value: number) => {
        if (!validateTTL(value)) {
          return Promise.reject('TTL值必须是1或者大于等于60');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  priority: [
    { required: true, message: '优先级不能为空', trigger: 'blur' },
    { validator: (_rule: any, value: number) => {
        if (recordForm.type === 'MX' && !validatePriority(value)) {
          return Promise.reject('MX记录的优先级必须大于等于0');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ]
};

// 当前操作的域名和记录
const currentDomainForRecord = ref<Domain | null>(null);
const recordToDelete = ref<DnsRecord | null>(null);

// 使用VueUse的useWindowSize获取窗口尺寸
const { width } = useWindowSize();

// 根据窗口宽度判断是否为移动端
const isMobile = computed(() => width.value < 768);

// 动态计算抽屉的位置和尺寸
const drawerPlacement = computed(() => isMobile.value ? 'top' : 'right');
const drawerSize = computed(() => isMobile.value ? 700 : 400); // 高度或宽度

// 表格列定义
const domainColumns = [
  { title: '域名', dataIndex: 'name', key: 'name', width: 200 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '记录数', dataIndex: 'records', key: 'records', width: 100 },
  { title: '操作', dataIndex: 'operation', key: 'operation', width: 180, fixed: 'right' as const }
];

const dnsColumns = [
  { title: '名称', dataIndex: 'sub', key: 'sub', width: 80, align: 'center' as const },
  { title: '类型', dataIndex: 'type', key: 'type', width: 70, align: 'center' as const },
  { title: '内容', dataIndex: 'content', key: 'content', width: 160, align: 'center' as const },
  { title: 'TTL', dataIndex: 'ttl', key: 'ttl', width: 70, align: 'center' as const },
  { title: '优先级', dataIndex: 'priority', key: 'priority', width: 70, align: 'center' as const },
  { title: '代理状态', dataIndex: 'proxied', key: 'proxied', width: 80, align: 'center' as const },
  { title: '操作', dataIndex: 'operation', key: 'operation', width: 120, fixed: 'right' as const, align: 'center' as const }
];

// 生命周期钩子
onMounted(() => {
  getCloudflareConfig();
});

onBeforeUnmount(() => {
  // 清理所有相关的任务回调
  console.log("清理所有相关的任务回调");
  // 这里不需要像原来的代码那样清理，因为我们使用的是新的action服务
});

// 方法定义
async function saveConfig() {
  try {
    await formRef.value?.validate();

    // 手动设置loading状态
    loading.save.value = true;

    try {
      await actionService.submit(
        apiService.cloudflare.saveConfig,
        {
          body: {
            account_id: configForm.accountId.trim(),
            api_key: configForm.apiKey.trim()
          }
        },
        {
          // 不使用loading参数，手动管理loading状态
          successMsg: '账户设置保存成功',
          onSuccess: () => {
            getCloudflareConfig();
            dialogs.accountSettings = false; // 保存成功后关闭抽屉
          }
        }
      );
    } finally {
      // 无论成功失败都重置loading状态
      loading.save.value = false;
    }
  } catch (error) {
    // 错误已在action中处理
    loading.save.value = false;
  }
}

async function getCloudflareConfig() {
  try {
    const result = await actionService.submit(
      apiService.cloudflare.getFullAccountInfo,
      {},
      {
        loading: loading.main,
        showNotify: false
      }
    );

    if (result.configured) {
      configForm.accountId = result.account_id || '';
      configForm.apiKey = result.api_key || '';

      if (result.domain_list && typeof result.domain_list === 'object') {
        domainsList.value = Object.entries(result.domain_list).map(([name, info]: [string, any]) => ({
          id: info.zone_id,
          name,
          status: info.status,
          dns: info.dns?.map((record: any) => ({ ...record, isEditing: false, isSaving: false })) || [],
          dnsRecords: info.dns?.map((record: any) => ({ ...record, isEditing: false, isSaving: false })) || []
        }));

        if (domainsList.value.length > 0 && domainsList.value[0]?.id) {
          expandedRowKeys.value = [domainsList.value[0].id];
        }
      } else {
        domainsList.value = [];
      }
    } else {
      domainsList.value = [];
    }
  } catch (error) {
    // 错误已在action中处理
  }
}

async function validateApiConfig() {

    await formRef.value?.validate();
    await actionService.submit(
        apiService.cloudflare.validateApiKey,
        {
          body: {
            account_id: configForm.accountId,
            api_key: configForm.apiKey
          }
        },
        {
          // 不使用loading参数，手动管理loading状态
          loading: loading.validate,
          onSuccess: () => {
            // 验证成功后获取域名列表
            getCloudflareConfig();
            // 验证成功后不关闭抽屉，允许用户继续操作
          }
        }
      );
}

async function syncDomains() {
  await formRef.value?.validate();
  await actionService.submit(
      apiService.cloudflare.syncDomains,
      {
        body: {
          account_id: configForm.accountId,
          api_key: configForm.apiKey
        },
      },
      {
        loading: loading.main,
        onSuccess: () => getCloudflareConfig()
      }
    );
}

function showAddRecordDialog(domain: Domain) {
  currentDomainForRecord.value = domain;
  dialogs.addRecord = true;

  // 重置表单
  recordForm.sub = '';
  recordForm.type = 'A';
  recordForm.content = '';
  recordForm.ttl = 60;
  recordForm.priority = 10;
  recordForm.proxied = false;
}

// 添加DNS记录按钮处理函数
function handleAddRecordOk() {
  if (!recordFormRef.value) return;

  // 使用VBen表单验证
  recordFormRef.value.validate()
    .then(() => {
      // 验证通过后提交请求
      const domainName = currentDomainForRecord.value?.name || '';
      // 构建记录名称用于通知（如果需要使用）
      // const recordName = recordForm.sub ? `${recordForm.sub}.${domainName}` : domainName;

      return actionService.submit(
        apiService.cloudflare.createDnsRecord,
        {
          domainId: domainName,
          body: {
            sub: recordForm.sub,
            type: recordForm.type,
            content: recordForm.content.trim(),
            ttl: recordForm.ttl,
            priority: recordForm.type === 'MX' ? recordForm.priority : undefined,
            proxied: recordForm.proxied
          }
        },
        {
          loading: loading.addRecord,
          successMsg: 'DNS记录添加成功',
          onSuccess: () => {
            // 关闭对话框
            dialogs.addRecord = false;

            // 刷新数据
            getCloudflareConfig();

            // 添加通知
            // notificationService.addNotification({
            //   message: `成功添加 ${recordForm.type} 记录 "${recordName}" 指向 "${recordForm.content}"`,
            //   from: 'Cloudflare',
            //   type: 'success'
            // });
          }
        }
      );
    })
    .catch(() => {
      // 表单验证失败已在表单组件中处理
    })
    .finally(() => {
    });
}

function deleteRecord(domain: Domain, record: DnsRecord) {
  if (!domain || !record) return;
  let recordName = (record.sub === domain.name ? '@' : record.sub + ".") + domain.name;

  Modal.confirm({
    title: '删除DNS记录',
    content: `确定要删除记录 ${recordName} 吗？此操作不可撤销。`,
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
        currentDomainForRecord.value = domain;
        recordToDelete.value = record;
        await actionService.submit(
          apiService.cloudflare.deleteDnsRecord,
          {
            domainId: domain.name,
            recordId: record.record_id
          },
          {
            loading: loading.saveRecord,
            successMsg: 'DNS记录已删除',
            onSuccess: () => {
              // 刷新数据
              getCloudflareConfig();

              // 添加通知
              // notificationService.addNotification({
              //   message: `成功删除 ${record.type} 记录 "${recordName}"`,
              //   from: 'Cloudflare',
              //   type: 'error'
              // });
            }
          }
        );
        return true;
    }
  });
}

function cancelDialog() {
  dialogs.addRecord = false;
  recordToDelete.value = null;
  currentDomainForRecord.value = null;
}

function startEditing(record: DnsRecord) {
  record._originalData = JSON.stringify({
    content: record.content,
    ttl: record.ttl,
    priority: record.priority,
    proxied: record.proxied
  });
  record.isEditing = true;
}

function cancelEdit(record: DnsRecord) {
  if (record._originalData) {
    const originalData = JSON.parse(record._originalData);
    Object.assign(record, originalData);
  }
  record.isEditing = false;
  record.loading = false; // 确保取消编辑时重置loading状态
  delete record._originalData;
}

function onProxyStatusChange(_domain: Domain, record: DnsRecord) {
  if (record.isEditing) {
    record.proxied = !record.proxied;
  }
}

async function saveRecordChange(domain: Domain, record: DnsRecord) {
  if (!domain || !record) return;

  // 验证各个字段的有效性
  let isValid = true;

  // 验证内容字段
  if (!record.content || record.content.trim() === '') {
    notifyUtils.error('验证失败', '记录内容不能为空');
    isValid = false;
  }

  // 验证TTL字段
  if (!validateTTL(record.ttl)) {
    notifyUtils.error('验证失败', 'TTL值必须是1或者大于等于60');
    isValid = false;
  }

  // 验证MX记录的优先级
  if (record.type === 'MX' && !validatePriority(record.priority)) {
    notifyUtils.error('验证失败', 'MX记录的优先级必须大于等于0');
    isValid = false;
  }

  if (!isValid) return;

  try {
    // 设置行级loading状态
    record.loading = true;

    // 构建记录名称用于通知（如果需要使用）
    // let recordName = (record.sub === domain.name ? '@' : record.sub + ".") + domain.name;

    await actionService.submit(
      apiService.cloudflare.updateDnsRecord,
      {
        domainId: domain.name,
        recordId: record.record_id,
        body: {
          sub: record.sub,
          type: record.type,
          content: record.content,
          ttl: record.ttl,
          priority: record.priority || 10,
          proxied: record.proxied || false
        }
      },
      {
        // 不使用全局loading状态，而是使用行级loading
        onSuccess: () => {
          record.isEditing = false;
          delete record._originalData;
          record.loading = false;

          // 添加通知
          // notificationService.addNotification({
          //   message: `成功更新 ${record.type} 记录 "${recordName}" 指向 "${record.content}"`,
          //   from: 'Cloudflare',
          //   type: 'info'
          // });
        },
        onError: () => {
          record.loading = false;
        }
      }
    );
  } catch (error) {
    // 错误已在action中处理
    record.loading = false;
  }
}

function onConfigFormSubmit() {
  // 调用saveConfig函数，它会处理loading状态和抽屉关闭
  saveConfig();
}

function handleExpandChange(expanded: boolean, record: Domain) {
  if (expanded) {
    expandedRowKeys.value = [record.id];
  } else {
    expandedRowKeys.value = [];
  }
}

function getRecordTagColor(type: string) {
  const colors: Record<string, string> = {
    A: 'blue',
    AAAA: 'green',
    CNAME: 'orange',
    MX: 'red',
    TXT: 'cyan'
  };
  return colors[type] || 'default';
}

function validateTTL(val: number) {
  return val === 1 || val >= 60;
}

function validatePriority(val?: number) {
  return val !== undefined && val >= 0;
}
</script>

<style lang="less" scoped>
.cloudflare-container {
  width: 100%;
  .domains-card {
    margin-bottom: 24px;
  }

  .account-card {
    margin-bottom: 24px;
  }

  .expanded-dns-container {
    padding: 8px 0 8px 0;
    background-color: transparent;
    border-radius: 4px;
  }

  .dns-input {
    width: 100%;
    min-width: 120px;
  }

  .content-cell {
    display: inline-block;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .name-cell {
    display: inline-block;
    max-width: 70px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .dns-number-input {
    width: 100%;
    min-width: 80px;
  }

  :deep(.ant-input-number) {
    width: 100%;
  }

  .full-width-input {
    width: 100% !important;
  }

  .form-row {
    display: flex;
    margin-bottom: 16px;
    align-items: center;
  }

  .form-label {
    width: 100px;
    text-align: right;
    padding-right: 12px;

    &.required::before {
      content: '*';
      color: #ff4d4f;
      margin-right: 4px;
    }
  }

  .form-content {
    flex: 1;
    display: flex;
    align-items: center;

    .account-input {
      flex: 1;
      margin-right: 8px;
    }
  }

  :deep(.ant-table-cell) {
    vertical-align: middle;
    text-align: center;
    padding: 8px 4px;
  }

  :deep(.ant-btn) {
    margin-right: 8px;

    &:last-child {
      margin-right: 0;
    }
  }

  :deep(.dns-operation-buttons) {
    justify-content: center;
    text-align: center;

    .ant-btn {
      margin: 0 4px;
    }
  }

  :deep(.form-help-text) {
    font-size: 12px;
    margin-top: 4px;
    display: block;
  }

  :deep(.drawer-content) {
    height: 100%;
    overflow-y: auto;
    width: 100%;
  }

  .drawer-form {
    max-width: 600px;
    margin: 0 auto;
  }

  :deep(.ant-drawer-header) {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.ant-drawer-body) {
    padding: 0;
  }

  :deep(.ant-drawer-content-wrapper) {
    transition: all 0.3s ease-in-out;
  }

  /* 已移除整行加载样式 */

  /* 展开区域样式 */
  :deep(.expanded-dns-container) {
    width: 100%;
  }

  // 移动端样式
  @media (max-width: 767px) {
    .drawer-form {
      padding: 0 12px;
    }
  }

  // PC端样式
  @media (min-width: 768px) {
    .drawer-form {
      padding: 0 24px;
    }
  }

  // 圆形按钮图标居中样式
  :deep(.ant-btn-circle) {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.ant-btn-circle .anticon) {
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
  }
}
</style>
