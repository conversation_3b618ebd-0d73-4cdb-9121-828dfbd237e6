<template>
  <a-modal
    :open="open"
    title="临时访问链接"
    :footer="null"
    @ok="handleClose"
    @cancel="handleClose"
    @update:open="(val: boolean) => emit('update:open', val)"
    width="500px"
  >
    <template v-if="currentFile">
      <div class="presigned-url-content">
        <p>以下是文件 <strong>{{ currentFile.filename }}</strong> 的临时访问链接，有效期 {{ presignedUrlExpiry }} 秒:</p>

        <a-input-group compact>
          <a-input
            :value="presignedUrl"
            readonly
            style="width: calc(100% - 80px)"
          />
          <a-button type="primary" @click="copyToClipboard(presignedUrl)">复制</a-button>
        </a-input-group>

        <div class="expires-control">
          <span>有效期：</span>
          <a-radio-group :value="localPresignedUrlExpiry" @change="handleExpiryChange">
            <a-radio-button :value="600">10分钟</a-radio-button>
            <a-radio-button :value="3600">1小时</a-radio-button>
            <a-radio-button :value="86400">1天</a-radio-button>
            <a-radio-button :value="604800">7天</a-radio-button>
          </a-radio-group>
          <a-button size="small" @click="handleRefresh" type="primary">刷新链接</a-button>
        </div>

        <div class="dialog-footer">
          <a-button type="primary" @click="handleClose">关闭</a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { notifyUtils } from '#/utils/notify';

// 定义组件属性
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  currentFile: {
    type: Object,
    default: null
  },
  presignedUrl: {
    type: String,
    default: ''
  },
  presignedUrlExpiry: {
    type: Number,
    default: 3600
  }
});

// 定义组件事件
const emit = defineEmits(['update:open', 'update:presignedUrlExpiry', 'refresh']);

// 本地状态
const localPresignedUrlExpiry = ref(props.presignedUrlExpiry);

// 监听属性变化
watch(() => props.presignedUrlExpiry, (newVal) => {
  localPresignedUrlExpiry.value = newVal;
});

// 处理关闭
const handleClose = () => {
  emit('update:open', false);
};

// 处理有效期变化
const handleExpiryChange = (e: { target: { value: number } }) => {
  emit('update:presignedUrlExpiry', e.target.value);
};

// 处理刷新
const handleRefresh = () => {
  emit('refresh');
};

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  if (!text || text === '加载中...' || text === '获取失败') {
    notifyUtils.warning('复制失败', '无有效链接可复制');
    return;
  }

  try {
    await navigator.clipboard.writeText(text);
    notifyUtils.success('复制成功', '链接已复制到剪贴板');
  } catch (err) {
    // 如果navigator.clipboard不可用，使用传统方法
    fallbackCopyToClipboard(text);
  }
};

// 兼容旧浏览器的复制方法
const fallbackCopyToClipboard = (text: string) => {
  try {
    const tempInput = document.createElement('input');
    document.body.appendChild(tempInput);
    tempInput.value = text;
    tempInput.select();
    // 注意：document.execCommand已经弃用，但在某些浏览器中仍然可用
    document.execCommand('copy');
    document.body.removeChild(tempInput);

    notifyUtils.success('复制成功', '链接已复制到剪贴板');
  } catch (err) {
    console.error('复制失败:', err);
    notifyUtils.error('复制失败', '请手动复制链接');
  }
};
</script>

<style lang="less" scoped>
.presigned-url-content {
  p {
    margin-bottom: 16px;
  }

  .expires-control {
    margin-top: 16px;
    display: flex;
    align-items: center;

    span {
      margin-right: 8px;
    }

    .ant-radio-group {
      margin-right: 16px;
    }
  }

  .dialog-footer {
    margin-top: 24px;
    text-align: right;
  }
}
</style>
