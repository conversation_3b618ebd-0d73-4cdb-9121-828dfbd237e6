<template>
  <a-card :title="props.storageType === 'temporary' ? '临时文件列表' : '永久文件列表'" class="files-card" :bordered="true">
    <template #extra>
      <a-space>
        <a-input
          v-model:value="searchKeyword"
          placeholder="搜索文件名"
          allow-clear
          @clear="searchFiles"
          @press-enter="searchFiles"
        >
          <template #suffix>
            <SearchIcon @click="searchFiles" />
          </template>
        </a-input>
        <a-button type="primary" @click="searchFiles" :loading="loading.list.value">
          刷新列表
        </a-button>
      </a-space>
    </template>

    <a-spin :spinning="loading.list.value">
      <a-table
        v-if="fileList.length > 0"
        :dataSource="fileList"
        :columns="fileColumns"
        :pagination="false"
        :row-key="(record) => record.object_name"
        size="middle"
      >
        <!-- 文件信息列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'file_info'">
            <div class="file-info">
              <FileIcon />
              <div class="file-info-detail">
                <div class="file-name">{{ record.filename }}</div>
                <div class="file-meta">
                  <span>{{ fileUtils.formatFileSize(record.size) }}</span>
                  <span class="file-time">{{ fileUtils.formatTime(record.upload_time || record.updated_at) }}</span>
                  <!-- 显示临时文件过期时间 -->
                  <span v-if="record.expires_at" class="file-expires-at">
                    <a-tag color="warning" size="small">
                      {{ fileUtils.formatExpiryTime(record.expires_at) }}后过期
                    </a-tag>
                  </span>
                </div>
              </div>
            </div>
          </template>

          <template v-else-if="column.key === 'file_type'">
            <a-tag :color="fileUtils.getFileTypeColor(record.filename)">
              {{ fileUtils.getFileExtension(record.filename).toUpperCase() }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'operation'">
            <a-space>
              <a-tooltip title="查看/下载">
                <a-button type="primary" shape="circle" size="small" @click="previewFile(record)">
                  <template #icon><EyeIcon /></template>
                </a-button>
              </a-tooltip>
              <a-tooltip title="获取临时链接">
                <a-button type="warning" shape="circle" size="small" @click="getPresignedUrl(record)">
                  <template #icon><LinkIcon /></template>
                </a-button>
              </a-tooltip>
              <a-tooltip title="删除文件">
                <a-button type="danger" shape="circle" size="small" @click="confirmDeleteFile(record)">
                  <template #icon><DeleteIcon /></template>
                </a-button>
              </a-tooltip>
            </a-space>
          </template>
        </template>
      </a-table>

      <a-empty v-else-if="!loading.list.value" description="暂无文件，请上传文件" />
    </a-spin>

    <!-- 加载更多按钮 -->
    <div v-if="fileList.length > 0 && hasMoreFiles" class="load-more-container">
      <a-button :loading="isLoadingMore" @click="loadMoreFiles" block>
        {{ isLoadingMore ? '正在加载...' : '加载更多' }}
      </a-button>
      <div class="file-count-info">已加载 {{ fileList.length }} 个文件</div>
    </div>

    <!-- 预签名URL对话框 -->
    <presigned-url-dialog
      v-model:open="dialogs.presignedUrl"
      :current-file="currentFile"
      :presigned-url="presignedUrl"
      :presigned-url-expiry="presignedUrlExpiry"
      @update:presigned-url-expiry="presignedUrlExpiry = $event"
      @refresh="refreshPresignedUrl"
    />

    <!-- 删除确认对话框 -->
    <delete-confirm-dialog
      v-model:open="dialogs.deleteFile"
      :current-file="currentFile"
      :loading="loading.delete.value"
      @confirm="deleteFile"
    />

    <!-- 图片预览对话框 -->
    <a-modal
      v-model:open="dialogs.imagePreview"
      title="图片预览"
      :footer="null"
      width="90%"
      centered
      :bodyStyle="{ padding: '0', background: 'rgba(0, 0, 0, 0.05)' }"
      :maskStyle="{ background: 'rgba(0, 0, 0, 0.7)' }"
    >
      <div class="image-preview-container">
        <img :src="previewImageUrl" alt="图片预览" class="preview-image" />
      </div>
    </a-modal>
  </a-card>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { useActionService } from '#/services/action';
import { apiService } from '#/services/api';
import { notifyUtils } from '#/utils/notify';
import { fileUtils } from '#/utils/file';
import PresignedUrlDialog from './PresignedUrlDialog.vue';
import DeleteConfirmDialog from './DeleteConfirmDialog.vue';
import { FileIcon, SearchIcon, EyeIcon, LinkIcon, DeleteIcon } from '@vben/icons';

// 定义组件事件
const emit = defineEmits(['refresh']);

// 获取action服务
const actionService = useActionService();

// 文件列表相关
const fileList = ref<any[]>([]);
const searchKeyword = ref('');
const continuationToken = ref(''); // 分页令牌
const hasMoreFiles = ref(true); // 是否还有更多文件
const isLoadingMore = ref(false); // 是否正在加载更多

// 每次加载的文件数量
const pageSize = ref(100);

// 文件列表列定义
const fileColumns = [
  { title: '文件信息', key: 'file_info', dataIndex: 'filename', width: '40%' },
  { title: '类型', key: 'file_type', dataIndex: 'filename', width: '10%' },
  { title: '对象路径', key: 'object_path', dataIndex: 'object_name', width: '30%', ellipsis: true },
  { title: '操作', key: 'operation', dataIndex: 'object_name', width: '20%', fixed: 'right' }
];

// 对话框相关
const dialogs = reactive({
  presignedUrl: false,
  deleteFile: false,
  imagePreview: false,
});

// 加载状态 - 使用ref而不是reactive，与原始JavaScript项目保持一致
const loading = {
  list: ref(false),
  delete: ref(false),
  presignedUrl: ref(false),
};

// 文件操作相关
interface FileItem {
  object_name: string;
  filename: string;
  size: number;
  url?: string;
  upload_time?: string | number;
  updated_at?: string | number;
  expires_at?: string | number;
  [key: string]: any; // 允许其他属性
}

const currentFile = ref<Record<string, any> | null>(null);
const presignedUrl = ref('');
const presignedUrlExpiry = ref(3600);
const previewImageUrl = ref('');

// 存储类型属性
const props = defineProps({
  storageType: {
    type: String,
    default: 'permanent',
    validator: (value: string) => ['permanent', 'temporary'].includes(value)
  }
});

// 根据存储类型设置当前存储桶
const currentBucket = computed(() => {
  return props.storageType === 'temporary' ? 'temporary' : 'nebula';
});

// 生命周期钩子
onMounted(() => {
  console.log('组件挂载，开始获取文件列表');
  getFileList();
});

// 调试输出文件列表状态
watch(fileList, (newVal) => {
  // console.log('文件列表变化:', newVal);
}, { deep: true });

// 监听bucket变化
watch(currentBucket, () => {
  getFileList();
});

// 获取文件列表
const getFileList = async (isRefresh = true) => {
  try {
    // 如果是刷新，则显示全局加载状态，否则显示加载更多状态
    if (isRefresh) {
      loading.list.value = true;
      // 刷新时重置分页令牌
      continuationToken.value = '';
    } else {
      isLoadingMore.value = true;
    }

    // 无论是哪种存储模式，都使用相同的bucket，使用temp_only参数区分
    const bucketParam = 'nebula'; // 统一使用nebula桶
    const isTemp = currentBucket.value === 'temporary';

    console.log('获取文件列表，存储类型:', props.storageType, '是否临时:', isTemp);

    // 准备请求参数
    const requestParams: any = {
      bucketName: bucketParam,
      search: searchKeyword.value || '',
      max_keys: pageSize.value,
      temp_only: isTemp
    };

    // 如果有分页令牌且不是刷新，则添加到请求参数中
    if (continuationToken.value && !isRefresh) {
      requestParams.continuation_token = continuationToken.value;
    }

    await actionService.submit(
      apiService.storage.getFileList,
      requestParams,
      {
        loading: isRefresh ? loading.list : undefined,
        showNotify: false,
        errorTitle: '获取文件列表',
        onSuccess: (data: any) => {
          // console.log('获取文件列表响应:', data);
          if (data && Array.isArray(data.files)) {
            // 获取文件列表
            let files = data.files;

            // 处理文件数据，确保每个文件对象都有filename和url字段
            files = files.map((file: any) => {
              // 如果没有filename，从对象名称中提取
              if (!file.filename && file.object_name) {
                file.filename = file.object_name.split('/').pop() || '未命名文件';
              }

              // 如果没有url字段，但有object_name，则构造一个预签名URL
              if (!file.url && file.object_name) {
                // 先使用一个占位符，在需要时再获取真正的URL
                file.url = '#';
              }

              return file;
            });

            // 按时间倒序排列
            files = sortFilesByTime(files);

            // 如果是刷新，则替换文件列表，否则追加
            if (isRefresh) {
              fileList.value = files;
            } else {
              fileList.value = [...fileList.value, ...files];
            }

            // 保存下一页的分页令牌
            continuationToken.value = data.next_token || '';

            // 判断是否还有更多文件
            hasMoreFiles.value = !!data.next_token;

            // console.log('当前文件数:', fileList.value.length, '分页令牌:', continuationToken.value);
          } else {
            console.warn('文件列表为空或格式不正确');
            if (isRefresh) {
              fileList.value = [];
            }
            continuationToken.value = '';
            hasMoreFiles.value = false;
          }
        }
      }
    );
  } catch (error) {
    if (isRefresh) {
      fileList.value = [];
    }
    continuationToken.value = '';
    hasMoreFiles.value = false;
  } finally {
    if (isRefresh) {
      loading.list.value = false;
    } else {
      isLoadingMore.value = false;
    }
  }
};

// 加载更多文件
const loadMoreFiles = async () => {
  if (!hasMoreFiles.value || isLoadingMore.value) return;
  await getFileList(false);
};


// 预览文件
const previewFile = async (file: FileItem) => {
  if (!file) {
    notifyUtils.warning('预览失败', '无效的文件对象');
    return;
  }

  // 如果URL是占位符，则获取预签名URL
  if (!file.url || file.url === '#') {
    try {
      // 使用统一的bucket
      const bucketParam = 'nebula';

      const response = await actionService.submit(
        apiService.storage.getPresignedUrl,
        {
          objectName: file.object_name,
          expiresIn: 3600, // 1小时
          bucketName: bucketParam
        },
        {
          loading: loading.presignedUrl,
          showNotify: false
        }
      );

      if (response && response.url) {
        file.url = response.url;
      } else {
        notifyUtils.warning('预览失败', '无法获取文件URL');
        return;
      }
    } catch (error: any) {
      notifyUtils.error('预览失败', error.message || '无法获取文件URL');
      return;
    }
  }

  // 检查文件类型
  const fileExt = fileUtils.getFileExtension(file.filename).toLowerCase();
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];

  if (imageExts.includes(fileExt)) {
    // 图片文件，使用模态框预览
    previewImageUrl.value = file.url || '';
    dialogs.imagePreview = true;
  } else {
    // 非图片文件，直接在新窗口打开
    window.open(file.url, '_blank');
  }
};

// 获取预签名URL
const getPresignedUrl = async (file: FileItem) => {
  if (!file) return;

  currentFile.value = file;
  presignedUrl.value = '加载中...';
  dialogs.presignedUrl = true;

  await refreshPresignedUrl();
};

// 刷新预签名URL
const refreshPresignedUrl = async () => {
  if (!currentFile.value) return;

  try {
    loading.presignedUrl.value = true;

    // 使用统一的bucket
    const bucketParam = 'nebula';

    await actionService.submit(
      apiService.storage.getPresignedUrl,
      {
        objectName: currentFile.value.object_name,
        expiresIn: presignedUrlExpiry.value,
        bucketName: bucketParam
      },
      {
        loading: loading.presignedUrl,
        showNotify: false,
        onSuccess: (data: any) => {
          if (data && data.url) {
            presignedUrl.value = data.url;
          } else {
            notifyUtils.warning('警告', '生成临时链接返回格式错误');
            presignedUrl.value = '获取失败';
          }
        }
      }
    );
  } catch (error: any) {
    notifyUtils.error('获取临时链接失败', error.message || '无法获取临时链接');
    presignedUrl.value = '获取失败';
  } finally {
    loading.presignedUrl.value = false;
  }
};

// 确认删除文件
const confirmDeleteFile = (file: FileItem) => {
  currentFile.value = file;
  dialogs.deleteFile = true;
};

// 删除文件
const deleteFile = async () => {
  if (!currentFile.value) return;

  try {
    loading.delete.value = true;

    // 使用统一的bucket
    const bucketParam = 'nebula';

    await actionService.submit(
      apiService.storage.deleteFile,
      {
        objectName: currentFile.value.object_name,
        bucketName: bucketParam
      },
      {
        loading: loading.delete,
        successMsg: '文件删除成功',
        successTitle: '删除成功',
        onSuccess: () => {
          dialogs.deleteFile = false;
          getFileList();
        }
      }
    );
  } catch (error: any) {
    notifyUtils.error('删除文件失败', error.message || '无法删除文件');
  } finally {
    loading.delete.value = false;
  }
};



// 搜索文件
const searchFiles = () => {
  // 重置分页令牌并重新加载
  getFileList(true);
};

// 按时间倒序排序文件列表
const sortFilesByTime = (files: any[]) => {
  if (!files || !Array.isArray(files)) return [];

  // 复制数组以避免修改原数组
  return [...files].sort((a, b) => {
    // 获取时间字段，优先使用updated_at，其次是upload_time
    const timeA = a.updated_at || a.upload_time || 0;
    const timeB = b.updated_at || b.upload_time || 0;

    // 倒序排列（近期在前）
    return new Date(timeB).getTime() - new Date(timeA).getTime();
  });
};

// 暴露组件方法给父组件
defineExpose({
  getFileList
});
</script>

<style lang="less" scoped>
.files-card {
  margin-bottom: 20px;
}

.file-info {
  display: flex;
  align-items: flex-start;

  .anticon {
    margin-top: 4px;
    margin-right: 8px;
    font-size: 16px;
  }
}

.file-info-detail {
  flex: 1;

  .file-name {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .file-meta {
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;

    .file-time {
      margin-left: 8px;
    }

    .file-expires-at {
      margin-left: 8px;
    }
  }
}

.load-more-container {
  margin-top: 16px;
  text-align: center;
  padding: 8px 0;
}

.file-count-info {
  margin-top: 8px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 20px;
}

.preview-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
