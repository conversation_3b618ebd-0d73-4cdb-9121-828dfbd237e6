<template>
  <div class="upload-card">
    <div class="upload-card-header">
      <div class="upload-description">请选择要上传的文件并填写相关信息</div>
      <a-button type="default" @click="resetUploadForm" :disabled="loading.upload">
        重置
      </a-button>
    </div>

    <!-- 根据存储类型显示对应的表单 -->
    <div v-if="!props.isTemp">
      <a-form
        ref="uploadForm"
        :model="uploadData"
        :rules="uploadRules"
        @finish="handleUpload"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        class="upload-form"
      >
          <a-form-item label="存储路径" name="path">
            <a-input
              v-model:value="uploadData.path"
              placeholder="选填，例如: images/"
              allow-clear
              :disabled="loading.upload"
            />
          </a-form-item>

          <a-form-item label="文件选择" name="file" required>
            <a-upload
              v-model:file-list="uploadData.file"
              :disabled="loading.upload"
              :multiple="false"
              :max-count="1"
              :show-upload-list="true"
              :before-upload="beforeUpload"
              :custom-request="() => {}"
              list-type="picture"
              :drag="true"
              class="full-width-upload"
            >
              <div class="upload-drag-area">
                <p class="upload-drag-icon">
                  <UploadIcon />
                </p>
                <p class="upload-drag-text">点击或拖拽文件到此区域上传</p>
                <p class="upload-tip">支持任意类型文件，单个文件最大 20MB</p>
              </div>
            </a-upload>
          </a-form-item>

          <a-form-item :wrapper-col="{ span: 24 }">
            <a-button type="primary" html-type="submit" :loading="loading.upload" block>
              上传至永久存储
            </a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 临时存储表单 -->
      <div v-if="props.isTemp">
        <a-form
          ref="uploadTempForm"
          :model="uploadData"
          :rules="uploadRules"
          @finish="handleUploadTemp"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 20 }"
          class="upload-form"
        >
          <a-form-item label="存储路径" name="path">
            <a-input
              v-model:value="uploadData.path"
              placeholder="选填，例如: images/"
              allow-clear
              :disabled="loading.upload"
            />
          </a-form-item>

          <a-form-item label="过期时间" name="expiresIn">
            <a-radio-group v-model:value="uploadData.expiresIn">
              <a-radio-button :value="3600">1小时</a-radio-button>
              <a-radio-button :value="86400">1天</a-radio-button>
              <a-radio-button :value="604800">7天</a-radio-button>
              <a-radio-button :value="2592000">30天</a-radio-button>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="文件选择" name="file" required>
            <a-upload
              v-model:file-list="uploadData.file"
              :disabled="loading.upload"
              :multiple="false"
              :max-count="1"
              :show-upload-list="true"
              :before-upload="beforeUpload"
              :custom-request="() => {}"
              list-type="picture"
              :drag="true"
              class="full-width-upload"
            >
              <div class="upload-drag-area">
                <p class="upload-drag-icon">
                  <UploadIcon />
                </p>
                <p class="upload-drag-text">点击或拖拽文件到此区域上传</p>
                <p class="upload-tip">支持任意类型文件，单个文件最大 20MB</p>
              </div>
            </a-upload>
          </a-form-item>

          <a-form-item :wrapper-col="{ span: 24 }">
            <a-button type="primary" html-type="submit" :loading="loading.upload" block>
              上传至临时存储
            </a-button>
          </a-form-item>
        </a-form>
      </div>

    <!-- 上传进度条 -->
    <div v-if="loading.upload && uploadProgress > 0" class="upload-progress-container">
      <a-progress :percent="uploadProgress" :stroke-color="{ from: '#108ee9', to: '#87d068' }" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import { useActionService } from '#/services/action';
import { apiService } from '#/services/api';
import { notifyUtils } from '#/utils/notify';
import { UploadIcon } from '@vben/icons';

// 定义组件属性
const props = defineProps({
  onSuccess: {
    type: Function,
    default: () => {}
  },
  isTemp: {
    type: Boolean,
    default: false
  }
});

// 定义组件事件
const emit = defineEmits(['refresh']);

// 获取action服务
const actionService = useActionService();

// 上传相关数据
const uploadType = ref(props.isTemp ? 'temp' : 'normal');
const uploadForm = ref();
const uploadTempForm = ref();
const uploadProgress = ref(0);

// 组件挂载时根据属性设置默认上传类型
watch(() => props.isTemp, (isTemp) => {
  uploadType.value = isTemp ? 'temp' : 'normal';
  console.log('切换上传类型为:', uploadType.value);
}, { immediate: true });

// 加载状态
const loading = reactive({
  upload: false
});

// 上传数据
const uploadData = reactive({
  path: '',
  file: [],
  expiresIn: 86400 // 默认过期时间1天
});

// 上传规则
const uploadRules = {
  file: [{ required: true, message: '请选择要上传的文件', trigger: 'change' }]
};

// 监听上传类型变化
watch(uploadType, () => {
  // 切换上传类型时重置表单
  resetUploadForm();
});

// 上传前检查文件
const beforeUpload = (file: File) => {
  // 检查文件大小（20MB限制）
  const isLt20M = file.size / 1024 / 1024 < 20;
  if (!isLt20M) {
    notifyUtils.error('文件过大', '文件大小不能超过20MB');
    return false;
  }
  return false; // 阻止自动上传
};

// 处理永久存储上传
const handleUpload = async () => {
  if (!uploadData.file || uploadData.file.length === 0) {
    notifyUtils.error('上传失败', '请选择要上传的文件');
    return;
  }

  // 使用any类型避免TypeScript的类型检查问题
  const fileItem = uploadData.file[0] as any;
  const file = fileItem?.originFileObj;
  if (!file) {
    notifyUtils.error('上传失败', '无法获取文件数据');
    return;
  }

  loading.upload = true;
  uploadProgress.value = 20; // 设置初始进度

  try {
    // 永久存储直接使用uploadFile API
    const formData = new FormData();
    formData.append('file', file);

    // 如果指定了路径，添加到formData
    if (uploadData.path) {
      formData.append('path', uploadData.path);
    }

    // 使用永久存储API
    await actionService.submit(
      apiService.storage.uploadFile,
      {
        bucketName: 'nebula',
        body: formData
      },
      {
        loading: { value: loading.upload },
        successMsg: '文件上传成功',
        successTitle: '上传成功',
        onSuccess: () => {
          resetUploadForm();
          emit('refresh'); // 通知父组件刷新文件列表
        }
      }
    );

    uploadProgress.value = 100;
  } catch (error: any) {
    notifyUtils.error('上传失败', error.message || '上传操作失败');
  } finally {
    loading.upload = false;
    uploadProgress.value = 0;
  }
};

// 处理临时存储上传
const handleUploadTemp = async () => {
  if (!uploadData.file || uploadData.file.length === 0) {
    notifyUtils.error('上传失败', '请选择要上传的文件');
    return;
  }

  // 使用any类型避免TypeScript的类型检查问题
  const fileItem = uploadData.file[0] as any;
  const file = fileItem?.originFileObj;
  if (!file) {
    notifyUtils.error('上传失败', '无法获取文件数据');
    return;
  }

  loading.upload = true;
  uploadProgress.value = 20; // 设置初始进度

  try {
    // 使用临时存储API
    const formData = new FormData();
    formData.append('file', file);

    // 如果指定了路径，添加到formData
    if (uploadData.path) {
      formData.append('path', uploadData.path);
    }

    // 添加过期时间
    formData.append('expires_in', uploadData.expiresIn.toString());

    await actionService.submit(
      apiService.storage.uploadTempFile,
      {
        bucketName: 'nebula',
        body: formData
      },
      {
        loading: { value: loading.upload },
        successMsg: '临时文件上传成功',
        successTitle: '上传成功',
        onSuccess: () => {
          resetUploadForm();
          emit('refresh'); // 通知父组件刷新文件列表
        }
      }
    );

    uploadProgress.value = 100;
  } catch (error: any) {
    notifyUtils.error('上传失败', error.message || '上传操作失败');
  } finally {
    loading.upload = false;
    uploadProgress.value = 0;
  }
};

// 重置上传表单
const resetUploadForm = () => {
  uploadData.path = '';
  uploadData.file = [];
  uploadData.expiresIn = 86400; // 重置为默认过期时间1天
  uploadProgress.value = 0;
};
</script>

<style lang="less" scoped>
.upload-card {
  margin-bottom: 0;
  padding: 0 8px;
}

.upload-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.upload-description {
  margin: 0;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.upload-tabs {
  margin-top: 10px;
}

/* 上传相关样式已移至公共样式文件 */

.upload-progress-container {
  margin-top: 16px;
  margin-bottom: 8px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-upload-list) {
  margin-top: 16px;
}

:deep(.ant-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.ant-form-item-control-input) {
  min-height: auto;
}

:deep(.ant-upload-drag) {
  height: auto;
}

:deep(.ant-btn-block) {
  width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  max-width: 100% !important;
}

:deep(.ant-form-item-control-input-content) {
  display: flex;
}

:deep(.ant-form-item-control-input-content .ant-btn) {
  flex: 1;
}

.upload-form {
  width: 100%;
}
</style>
