<template>
  <a-modal
    :open="open"
    title="删除文件"
    @ok="handleConfirm"
    @cancel="handleCancel"
    @update:open="(val: boolean) => emit('update:open', val)"
    :confirmLoading="loading"
    okText="确认删除"
    cancelText="取消"
    okType="danger"
  >
    <template v-if="currentFile">
      <p>确定要删除文件 <strong>{{ currentFile.filename }}</strong> 吗？此操作不可撤销。</p>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
// 定义组件属性
defineProps({
  open: {
    type: Boolean,
    default: false
  },
  currentFile: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// 定义组件事件
const emit = defineEmits(['update:open', 'confirm']);

// 处理确认
const handleConfirm = () => {
  emit('confirm');
};

// 处理取消
const handleCancel = () => {
  emit('update:open', false);
};
</script>
