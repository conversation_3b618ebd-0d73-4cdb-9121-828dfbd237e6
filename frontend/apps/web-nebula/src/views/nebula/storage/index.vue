<template>
  <div class="p-4">
    <div class="storage-container">
      <!-- 存储类型提示和操作按钮 -->
      <div class="storage-header">
        <a-button type="primary" @click="showUploadDrawer">
          <template #icon><UploadIcon /></template>
          上传{{ isTemporaryStorage ? '临时' : '永久' }}文件
        </a-button>

        <a-tag :color="isTemporaryStorage ? 'orange' : 'blue'" class="storage-type-tag">
          {{ isTemporaryStorage ? '当前模式: 临时存储' : '当前模式: 永久存储' }}
        </a-tag>
      </div>

      <!-- 文件上传抽屉 -->
      <a-drawer
        :title="isTemporaryStorage ? '上传临时文件' : '上传永久文件'"
        :placement="drawerPlacement"
        :height="isMobile ? drawerSize : undefined"
        :width="!isMobile ? drawerSize : undefined"
        :open="uploadDrawerVisible"
        @close="closeUploadDrawer"
        :bodyStyle="{ paddingBottom: isMobile ? '24px' : '80px', paddingTop: '12px' }"
        :headerStyle="{ borderBottom: '1px solid #f0f0f0', paddingBottom: '12px' }"
        destroyOnClose
      >
        <upload-card @refresh="refreshFileListAndCloseDrawer" :is-temp="isTemporaryStorage" />

        <template #footer>
          <div class="drawer-footer">
            <a-button @click="closeUploadDrawer">关闭</a-button>
          </div>
        </template>
      </a-drawer>

      <!-- 文件列表卡片 -->
      <file-list-card ref="fileListRef" :storage-type="storageType" />
    </div>
  </div>
</template>

<script lang="ts" setup>
// 存储管理页面
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import UploadCard from './components/UploadCard.vue';
import FileListCard from './components/FileListCard.vue';
import { useWindowSize } from '@vueuse/core';
import { UploadIcon } from '@vben/icons';

// 获取路由信息
const route = useRoute();

// 根据路由路径判断存储类型
const isTemporaryStorage = computed(() => {
  return route.path.includes('/storage/temporary');
});

// 存储类型
const storageType = computed(() => {
  return isTemporaryStorage.value ? 'temporary' : 'permanent';
});

// 文件列表引用
const fileListRef = ref<{ getFileList: () => void } | null>(null);

// 使用VueUse的useWindowSize获取窗口尺寸
const { width } = useWindowSize();

// 根据窗口宽度判断是否为移动端
const isMobile = computed(() => width.value < 768);

// 动态计算抽屉的位置和尺寸
const drawerPlacement = computed(() => isMobile.value ? 'top' : 'right');
const drawerSize = computed(() => isMobile.value ? 700 : 400); // 高度或宽度

// 上传抽屉相关状态
const uploadDrawerVisible = ref(false);

// 显示上传抽屉
const showUploadDrawer = () => {
  uploadDrawerVisible.value = true;
};

// 关闭上传抽屉
const closeUploadDrawer = () => {
  uploadDrawerVisible.value = false;
};

// 刷新文件列表
const refreshFileList = () => {
  if (fileListRef.value) {
    fileListRef.value.getFileList();
  }
};

// 刷新文件列表并关闭抽屉
const refreshFileListAndCloseDrawer = () => {
  refreshFileList();
  closeUploadDrawer();
};

// 监听路由变化
watch(
  () => route.path,
  () => {
    console.log('路由变化，当前存储类型:', storageType.value);
    if (fileListRef.value) {
      fileListRef.value.getFileList();
    }
  }
);

// 组件挂载时初始化
onMounted(() => {
  console.log('存储管理页面挂载，当前存储类型:', storageType.value);
});
</script>

<style lang="less" scoped>
.storage-container {
  width: 100%;
}

.storage-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.storage-type-tag {
  font-size: 14px;
  padding: 4px 8px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px 16px;
  border-top: 1px solid #f0f0f0;
  background: #fff;
}

:deep(.ant-drawer-header) {
  padding: 16px 24px;
}

:deep(.ant-drawer-title) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.ant-drawer-content-wrapper) {
  transition: all 0.3s ease-in-out;
}

// 移动端样式
@media (max-width: 767px) {
  .storage-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .storage-type-tag {
    align-self: flex-start;
  }
}

// PC端样式
@media (min-width: 768px) {
  .storage-header {
    flex-direction: row;
    align-items: center;
  }
}
</style>
