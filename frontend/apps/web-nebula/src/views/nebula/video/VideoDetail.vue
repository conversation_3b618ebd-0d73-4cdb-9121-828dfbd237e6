<template>
  <View>
    <ViewContainer ref="dataCardRef">
      <!-- 头部区域 -->
      <template #header>
        <ViewHeader ref="dataHeaderRef">
          <template #title>
            <span v-if="video">{{ video.title || '未命名视频' }}</span>
            <span v-else>加载中...</span>
          </template>
        </ViewHeader>
      </template>

      <!-- 内容区域 -->
      <template #body>
        <ViewBody
          :extraPadding="16"
          :minHeight="300"
        >
          <Spin :spinning="loading.detail.value" tip="加载中...">
            <template v-if="video">
              <Flex vertical class="video-detail-container" :gap="16">
                <!-- 视频播放器 -->
                <Flex class="video-detail-player-container">
                  <Flex class="video-detail-player" justify="center" align="center">
                    <video
                      class="video-detail-player-element"
                      :poster="FILE_SERVER_PREFIX + '/' + video.cover_path"
                      controls
                      preload="metadata"
                    >
                      <source :src="FILE_SERVER_PREFIX + '/' + video.video_path" type="video/mp4">
                      您的浏览器不支持 HTML5 视频。
                    </video>
                  </Flex>
                </Flex>

                <!-- 视频信息 -->
                <Flex vertical class="video-detail-info">
                  <Flex class="video-detail-meta">
                    <Typography.Text class="meta-label">上传时间：</Typography.Text>
                    <Typography.Text class="meta-value">{{ formatDate(video.updated_at) }}</Typography.Text>
                  </Flex>

                  <Flex class="video-detail-meta">
                    <Typography.Text class="meta-label">视频大小：</Typography.Text>
                    <Typography.Text class="meta-value">{{ formatSize(video.video_size) }}</Typography.Text>
                  </Flex>

                  <Flex class="video-detail-meta" v-if="video.duration">
                    <Typography.Text class="meta-label">时长：</Typography.Text>
                    <Typography.Text class="meta-value">{{ formatDuration(video.duration) }}</Typography.Text>
                  </Flex>

                  <Flex class="video-detail-meta" v-if="video.category">
                    <Typography.Text class="meta-label">分类：</Typography.Text>
                    <Typography.Text class="meta-value">{{ video.category }}</Typography.Text>
                  </Flex>

                  <!-- 视频标签 -->
                  <Flex vertical>
                    <Typography.Text class="meta-label">标签：</Typography.Text>
                    <Flex wrap="wrap" gap="small" class="tags-container">
                      <Tag
                        v-for="tag in video.tags"
                        :key="tag"
                        color="blue"
                        class="tag-item"
                        closable
                        @close="removeTag(tag)"
                      >
                        {{ tag }}
                      </Tag>
                    </Flex>
                    <Flex gap="small" class="tag-edit-container">
                      <Input
                        v-model:value="newTag"
                        placeholder="输入新标签"
                        class="tag-input"
                        @press-enter="addTag"
                      />
                      <Button type="primary" size="small" @click="addTag">添加</Button>
                    </Flex>
                  </Flex>

                  <!-- 视频概述 -->
                  <Flex vertical v-if="video.overview" class="video-detail-overview">
                    <Typography.Text class="meta-label">概述：</Typography.Text>
                    <Typography.Paragraph class="overview-content">{{ video.overview }}</Typography.Paragraph>
                  </Flex>

                  <!-- 视频亮点 -->
                  <Flex vertical class="video-detail-highlights" v-if="video.highlights && video.highlights.length > 0">
                    <Typography.Text class="meta-label">亮点：</Typography.Text>
                    <List :split="true" size="small">
                      <List.Item v-for="(highlight, hIndex) in video.highlights" :key="hIndex">
                        <template #default>
                          <Flex align="start" class="highlight-item">
                            <StarIcon class="highlight-icon" />
                            <Typography.Text class="highlight-text">{{ highlight }}</Typography.Text>
                          </Flex>
                        </template>
                      </List.Item>
                    </List>
                  </Flex>

                  <!-- 视频字幕 -->
                  <Flex vertical class="video-detail-subtitles" v-if="video.subtitles_chunks && video.subtitles_chunks.length > 0">
                    <Collapse>
                      <Collapse.Panel key="1" header="查看字幕">
                        <List :split="false" size="small">
                          <List.Item v-for="(chunk, index) in video.subtitles_chunks" :key="index">
                            <Typography.Paragraph class="subtitles-chunk">{{ chunk }}</Typography.Paragraph>
                          </List.Item>
                        </List>
                      </Collapse.Panel>
                    </Collapse>
                  </Flex>
                </Flex>
              </Flex>
            </template>
            <Empty v-else description="未找到视频数据" />
          </Spin>
        </ViewBody>
      </template>
    </ViewContainer>
  </View>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Button, Input, Tag, List, Collapse, Flex, Typography, Spin, Empty } from 'ant-design-vue';
import { useActionService } from '#/services/action';
import { apiService } from '#/services/api';
import { notifyUtils } from '#/utils/notify';
import { useTabs } from '@vben/hooks';

import ViewContainer from '../../../components/ViewContainer.vue';
import ViewHeader from '../../../components/ViewHeader.vue';
import ViewBody from '../../../components/ViewBody.vue';

// 导入 Iconify 图标
import { StarIcon } from '@vben/icons';
const { setTabTitle } = useTabs();

setTabTitle("loading...")

// 获取路由和路由器
const route = useRoute();
const router = useRouter();

// 组件引用
const dataCardRef = ref(null);
const dataHeaderRef = ref(null);

// 资源服务器前缀
const FILE_SERVER_PREFIX = 'https://z.fee.red/video';

// 视频数据
const video = ref<any>(null);

// 新标签
const newTag = ref('');

// 加载状态
const loading = {
  detail: ref(false)
};

// 获取action服务
const actionService = useActionService();

// 生命周期钩子
onMounted(() => {
  // 从路由参数获取视频ID
  const videoId = route.params.id;
  if (videoId) {
    fetchVideoDetail(videoId as string);
  } else {
    notifyUtils.error('参数错误', '未找到视频ID');
    goBack();
  }
});

// 处理标题省略
const truncateTitle = (title: string, maxLength: number = 12) => {
  if (!title) return '视频详情';
  return title.length > maxLength ? title.substring(0, maxLength) + '...' : title;
};

// 获取视频详情
const fetchVideoDetail = async (id: string) => {
  await actionService.submit(
    apiService.video.get,
    { id },
    {
      loading: loading.detail,
      showNotify: false,
      onSuccess: (response: any) => {
        video.value = response;

        // 获取视频标题并处理省略
        const videoTitle = response.title || '视频详情';
        const truncatedTitle = truncateTitle(videoTitle);

        // 更新页面标题
        document.title = `${videoTitle} - Nebula`;

        // 获取标签页管理系统并更新标签页标题
        setTabTitle(truncatedTitle);
      },
      onError: () => {
        notifyUtils.error('获取失败', '无法获取视频详情');
      }
    }
  );
};

// 返回列表页
const goBack = () => {
  router.push('/video');
};

// 添加标签
const addTag = async () => {
  if (!newTag.value.trim() || !video.value) {
    return;
  }

  try {
    await actionService.submit(
      apiService.video.addTags,
      {
        id: video.value.id,
        body: [newTag.value.trim()]
      },
      {
        successMsg: '标签添加成功',
        errorMsg: '标签添加失败',
        onSuccess: () => {
          // 清空输入框
          newTag.value = '';
          // 刷新视频详情
          fetchVideoDetail(video.value.id);
        }
      }
    );
  } catch (error) {
    // 错误已在action中处理
  }
};

// 移除标签
const removeTag = async (tag: string) => {
  if (!video.value) {
    return;
  }

  try {
    await actionService.submit(
      apiService.video.removeTags,
      {
        id: video.value.id,
        body: [tag]
      },
      {
        successMsg: '标签移除成功',
        errorMsg: '标签移除失败',
        onSuccess: () => {
          // 刷新视频详情
          fetchVideoDetail(video.value.id);
        }
      }
    );
  } catch (error) {
    // 错误已在action中处理
  }
};

// 格式化日期
const formatDate = (dateString: string | number | Date) => {
  if (!dateString) return '未知时间';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '未知时间';

  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 格式化文件大小
const formatSize = (bytes: number) => {
  if (!bytes || isNaN(bytes)) return '未知大小';

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`;
};

// 格式化时长
const formatDuration = (seconds: number) => {
  if (!seconds || isNaN(seconds)) return '未知时长';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
};
</script>

<style lang="less" scoped>
.video-detail-container {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.video-detail-player-container {
  width: 100%;
}

.video-detail-player {
  width: 100%;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
  aspect-ratio: 16/9;
}

.video-detail-player-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.video-detail-overview {
  margin: 8px 0;
}

.video-detail-info {
  padding: 8px;
  background-color: var(--background-color);
  border-radius: 8px;
  width: 100%;
}

.video-detail-title {
  margin-bottom: 16px !important;
  line-height: 1.4 !important;
}

.video-detail-meta {
  margin-bottom: 6px;
}

.meta-label {
  font-weight: 500 !important;
  min-width: 80px;
  color: rgba(0, 0, 0, 0.65) !important;
}

.meta-value {
  flex: 1;
}

.tags-container {
  margin: 8px 0;
}

.tag-item {
  margin-right: 0;
}

.tag-edit-container {
  margin-top: 8px;
}

.tag-input {
  flex: 1;
}


.overview-content {
  margin-top: 8px !important;
  white-space: pre-line;
  line-height: 1.6;
}

.video-detail-highlights {
  margin: 0px 0;
}

.highlight-item {
  margin-bottom: 4px;
}

.highlight-icon {
  color: #faad14;
  margin-right: 8px;
  font-size: 14px;
  margin-top: 3px;
}

.highlight-text {
  flex: 1;
  line-height: 1.5;
}

.video-detail-subtitles {
  margin: 8px 0;
}

.subtitles-content {
  white-space: pre-line;
  line-height: 1.6;
  max-height: 200px;
  overflow-y: auto;
}

.subtitles-chunk {
  margin-bottom: 8px !important;
  line-height: 1.6;
  white-space: pre-line;
}

/* 响应式布局 */
@media screen and (min-width: 768px) {
  .video-detail-container {
    flex-direction: row !important;
    align-items: flex-start;
    height: calc(100vh - 200px); /* 设置一个合适的高度，减去头部和其他元素的高度 */
    min-height: 500px; /* 设置最小高度 */
  }

  .video-detail-player-container {
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .video-detail-player {
    width: 100%;
    height: 100%;
  }

  .video-detail-info {
    width: 50%;
    height: 100%;
    max-height: 100%;
    overflow-y: auto; /* 添加垂直滚动条 */
    scrollbar-width: thin; /* Firefox */
    &::-webkit-scrollbar {
      width: 6px; /* Chrome, Safari, Edge */
    }
    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 3px;
    }
  }
}
</style>
