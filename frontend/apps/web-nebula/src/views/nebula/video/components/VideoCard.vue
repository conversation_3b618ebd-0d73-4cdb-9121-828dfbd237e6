<template>
  <Flex vertical class="video-waterfall">
    <Flex v-if="loading" justify="center" align="center" class="loading-container">
      <Spin :spinning="true" tip="加载中..." />
    </Flex>
    <Flex v-else-if="videoList.length === 0" justify="center" align="center" class="empty-container">
      <Empty description="暂无视频数据" />
    </Flex>
    <Flex v-else class="waterfall-wrapper">
      <Flex vertical v-for="(video, index) in videoList" :key="video.id" class="waterfall-item video-card" :class="video.platform ? `platform-${video.platform.toLowerCase()}` : ''">
        <!-- 卡片头部 -->
        <Flex justify="space-between" align="center" class="video-card-header">
          <Flex gap="12px" class="video-meta">
            <Typography.Text class="video-date" :title="formatDate(video.updated_at)">{{ formatter.relativeTime(video.updated_at) }}</Typography.Text>
            <Typography.Text class="video-size">{{ formatSize(video.video_size) }}</Typography.Text>
            <Tag :color="getPlatformColor(video)" class="platform-tag">{{ getPlatformName(video) }}</Tag>
          </Flex>
          <Flex gap="4px" justify="flex-end" class="video-actions">
            <Button type="text" size="medium" class="action-btn view-btn" @click="viewVideoDetails(video)" title="查看详情">
              <template #icon><DetailIcon style="font-size: 18px;" /></template>
            </Button>
            <Button type="text" size="medium" class="action-btn source-btn" @click="showVideoSource(video)" title="来源">
              <template #icon><SourceIcon style="font-size: 18px;" /></template>
            </Button>
            <Button type="text" size="medium" class="action-btn analyze-btn" @click="confirmAnalyzeVideo(video)" title="AI分析">
              <template #icon><RobotIcon style="font-size: 18px;" /></template>
            </Button>
            <Button type="text" size="medium" class="action-btn delete-btn" @click="confirmDeleteVideo(video)" title="删除">
              <template #icon><TrashIcon style="font-size: 18px;" /></template>
            </Button>
          </Flex>
        </Flex>

        <!-- 视频播放器 -->
        <div class="video-wrapper">
          <video
            class="video-player"
            :poster="FILE_SERVER_PREFIX + '/' + video.cover_path"
            preload="none"
            controls
            @click.stop
          >
            <source :src="FILE_SERVER_PREFIX + '/' + video.video_path" type="video/mp4">
            您的浏览器不支持 HTML5 视频。
          </video>
          <div class="video-play-button" @click="playVideo($event)">
            <Play />
          </div>
        </div>

        <!-- 视频信息 -->
        <Flex vertical class="video-info">
          <Typography.Text strong class="video-title" :title="video.title">{{ video.title || '未命名视频' }}</Typography.Text>

          <!-- 视频描述 -->
          <Flex vertical class="video-desc" v-if="video.overview">
            <div class="desc-divider"></div>
            <Typography.Paragraph class="desc-text">{{ video.overview }}</Typography.Paragraph>
          </Flex>

          <!-- 标签 -->
          <Flex wrap="wrap" gap="4px" class="video-tags" v-if="video.tags && video.tags.length > 0">
            <Tag
              v-for="tag in video.tags"
              :key="tag"
              :color="getTagColor(tag)"
              size="small"
              class="clickable-tag"
              @click="filterByTag(tag)"
            >
              {{ tag }}
            </Tag>
          </Flex>
        </Flex>
      </Flex>
    </Flex>

    <!-- 分页组件已移至父组件 -->
  </Flex>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, h } from 'vue';
import { useRouter } from 'vue-router';
import { Flex, Spin, Empty, Button, Tag, Typography, Modal } from 'ant-design-vue';
import { openExternalLink } from '#/utils/link';
import { formatter } from '#/utils';

// 导入 Iconify 图标
import {
  DetailIcon,
  MoreIcon,
  PlayIcon,
  RobotIcon,
  TrashIcon,
  VideoIcon,
  SourceIcon
} from '@vben/icons';

// 定义组件属性
const props = defineProps({
  videoList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// 定义组件事件
const emit = defineEmits([
  'view-details',
  'analyze',
  'delete',
  'filter-tag'
]);

// 资源服务器前缀
const FILE_SERVER_PREFIX = 'https://z.fee.red/video';

// 播放视频
const playVideo = (event: Event) => {
  const videoWrapper = (event.target as HTMLElement).closest('.video-wrapper');
  if (!videoWrapper) return;

  const videoElement = videoWrapper.querySelector('video');
  if (!videoElement) return;

  if (videoElement.paused) {
    // 暂停所有其他视频
    document.querySelectorAll('.video-player').forEach(video => {
      if (video !== videoElement) {
        (video as HTMLVideoElement).pause();
      }
    });

    videoElement.play();
    videoWrapper.classList.add('playing');
  } else {
    videoElement.pause();
    videoWrapper.classList.remove('playing');
  }
};

// 获取路由器
const router = useRouter();

// 查看视频详情
const viewVideoDetails = (video: any) => {
  // 暂停所有视频
  pauseAllVideos();

  // 使用路由导航到视频详情页
  router.push(`/video/${video.id}`);
};

// 确认分析视频
const confirmAnalyzeVideo = (video: any) => {
  pauseAllVideos();
  emit('analyze', video);
};

// 确认删除视频
const confirmDeleteVideo = (video: any) => {
  pauseAllVideos();
  emit('delete', video);
};

// 显示视频来源
const showVideoSource = (video: any) => {
  pauseAllVideos();

  // 使用Modal显示视频来源信息
  Modal.info({
    title: '视频来源',
    content: h('div', {}, [
      h('p', {}, [
        h('span', {}, '来源链接: '),
        h('a', {
          href: 'javascript:void(0)',
          onClick: () => openExternalLink(video.source_url),
          style: 'word-break: break-all;'
        }, video.source_url || '无')
      ])
    ]),
    okText: '关闭'
  });
};

// 通过标签过滤
const filterByTag = (tag: string) => {
  emit('filter-tag', tag);
};

// 获取标签颜色
const getTagColor = (tag: string) => {
  // 使用标签内容的哈希值来确定颜色
  const colors = ['blue', 'green', 'orange', 'purple', 'cyan', 'magenta', 'red', 'volcano', 'geekblue', 'lime'];
  const hashCode = tag.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return colors[hashCode % colors.length];
};

// 分页变化直接在模板中通过内联函数处理

// 暂停所有视频
const pauseAllVideos = () => {
  document.querySelectorAll('.video-player').forEach(video => {
    (video as HTMLVideoElement).pause();
  });
};

// 格式化日期
const formatDate = (dateString: string | number | Date) => {
  if (!dateString) return '未知时间';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '未知时间';

  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 格式化文件大小
const formatSize = (bytes: number) => {
  if (!bytes || isNaN(bytes)) return '未知大小';

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`;
};

// 获取平台名称
const getPlatformName = (video: any) => {
  // 平台名称映射
  const platformMap: Record<string, string> = {
    'douyin': '抖音',
    'youtube': 'YouTube',
    'twitter': 'Twitter/X',
    'bilibili': 'B站',
    'nebula': 'nebula'
  };

  // 只使用platform字段
  if (video.platform) {
    return platformMap[video.platform.toLowerCase()] || video.platform || 'nebula';
  }

  return 'nebula';
};

// 获取平台颜色
const getPlatformColor = (video: any) => {
  // 平台颜色映射
  const colorMap: Record<string, string> = {
    'douyin': 'var(--douyin-color)',    // 抖音红色
    'youtube': 'var(--youtube-color)',   // YouTube紫色
    'twitter': 'var(--twitter-color)',   // Twitter蓝色
    'bilibili': 'var(--bilibili-color)',   // B站绿色
    'nebula': 'var(--nebula-color)'   // nebula蓝色
  };

  return colorMap[video.platform.toLowerCase() || 'nebula'];
};
</script>

<style lang="less" scoped>
.video-waterfall {
  /* 平台颜色变量 */
  --douyin-color: rgb(9, 49, 55);
  --youtube-color: rgb(255, 102, 102);
  --twitter-color: rgb(46, 104, 158);
  --bilibili-color: rgb(57, 176, 150);
  --nebula-color: #b8d0e6;

  /* 平台背景色变量 */
  --douyin-bg-color: rgba(9, 49, 55, 0.05);
  --youtube-bg-color: rgba(255, 102, 102, 0.05);
  --twitter-bg-color: rgba(46, 104, 158, 0.05);
  --bilibili-bg-color: rgba(57, 176, 150, 0.05);
  --nebula-bg-color: rgba(57, 176, 150, 0.05);

  min-height: 200px;
  position: relative;
  width: 100%;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  width: 100%;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  width: 100%;
}

.waterfall-wrapper {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.waterfall-item {
  break-inside: avoid;
  background: var(--card);
  color: var(--card-foreground);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.waterfall-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.video-card {
  display: flex;
  flex-direction: column;
}

.video-wrapper {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 比例 */
  background-color: #000;
  overflow: hidden;
}

.video-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.8);
  font-size: 48px;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 2;
}

.video-play-button:hover {
  color: #fff;
  transform: translate(-50%, -50%) scale(1.1);
}

.video-wrapper.playing .video-play-button {
  display: none;
}

.video-info {
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.video-title {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.video-desc {
  margin: 8px 0 12px;
}

.desc-divider {
  height: 1px;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.05));
  margin: 4px 0 6px;
}

.desc-text {
  font-size: 13px;
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.65);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

.waterfall-item .video-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px dashed rgba(96, 125, 139, 0.3);
  margin: -1px -1px 0 -1px;
  border-top: 3px solid #1890ff; /* 默认颜色，会被平台特定样式覆盖 */
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  background-color: rgba(24, 144, 255, 0.05); /* 默认颜色，会被平台特定样式覆盖 */
}

.video-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);

  .platform-tag {
    margin-right: 0;
    font-size: 12px;
    line-height: 18px;
    height: 20px;
    padding: 0 6px;
  }
}

.video-tags {
  margin: 8px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.clickable-tag:hover {
  opacity: 0.8;
}

.video-actions {
  display: flex;
  gap: 4px;
  justify-content: flex-end;
}

.action-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  margin-left: 2px;

  &:hover {
    transform: scale(1.1);
  }

  &.view-btn {
    color: var(--twitter-color);
    :hover{
      background-color: var(--twitter-bg-color);
    }
  }

  &.source-btn {
    color: var(--youtube-color);
    :hover{
      background-color: var(--youtube-bg-color);
    }
  }

  &.analyze-btn {
    color: var(--bilibili-color);
    :hover{
      background-color: var(--bilibili-bg-color);
    }
  }

  &.delete-btn {
    color: var(--douyin-color);
    :hover{
      background-color: var(--douyin-bg-color);
    }
  }
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

/* 平台特定样式 */
.platform-douyin .video-card-header {
  border-top-color: var(--douyin-color);
  background-color: var(--douyin-bg-color);
}

.platform-youtube .video-card-header {
  border-top-color: var(--youtube-color);
  background-color: var(--youtube-bg-color);
}

.platform-twitter .video-card-header {
  border-top-color: var(--twitter-color);
  background-color: var(--twitter-bg-color);
}

.platform-bilibili .video-card-header {
  border-top-color: var(--bilibili-color);
  background-color: var(--bilibili-bg-color);
}

.platform-nebula .video-card-header {
  border-top-color: var(--nebula-color);
  background-color: var(--nebula-bg-color);
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .waterfall-wrapper {
    grid-template-columns: repeat(1, 1fr);
  }
}

@media screen and (min-width: 769px) and (max-width: 1200px) {
  .waterfall-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (min-width: 1201px) and (max-width: 1600px) {
  .waterfall-wrapper {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media screen and (min-width: 1601px) {
  .waterfall-wrapper {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>
