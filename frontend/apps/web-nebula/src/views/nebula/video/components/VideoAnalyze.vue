<template>
  <Modal
    :open="open"
    title="AI分析视频"
    :confirm-loading="loading"
    @ok="handleAnalyze"
    @cancel="handleCancel"
    @update:open="(val) => emit('update:open', val)"
    okText="开始分析"
    cancelText="取消"
  >
    <template v-if="video">
      <Flex vertical class="analyze-container">
        <Typography.Paragraph>确定要对视频 "{{ video.title || '未命名视频' }}" 进行AI分析吗？</Typography.Paragraph>
        <Typography.Paragraph class="analyze-desc">AI分析将提取视频的关键信息，生成概述和亮点，并自动添加标签。</Typography.Paragraph>

        <Alert
          type="info"
          show-icon
          message="分析提示"
          description="分析过程可能需要几分钟时间，请耐心等待。分析完成后，视频详情将自动更新。"
          class="analyze-alert"
        />
      </Flex>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits } from 'vue';
import { Modal, Alert, Typography, Flex } from 'ant-design-vue';
import { useVideoService } from '../../../../services/video';

// 定义组件属性
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  video: {
    type: Object,
    default: null
  }
});

// 定义组件事件
const emit = defineEmits(['update:open', 'success']);

// 加载状态
const loading = ref(false);

// 获取视频服务
const { actionService } = useVideoService();

// 处理分析
const handleAnalyze = async () => {
  if (!props.video) {
    return;
  }

  loading.value = true;

  try {
    await actionService.submit(
      useVideoService().analyze,
      {
        id: props.video.id,
        response_mode: "websocket"
      },
      {
        loading: ref(loading),
        showSubmitNotify: true,
        submitNotifyTitle: '视频分析',
        submitNotifyMsg: '视频分析任务已提交，正在后台处理中',
        onSuccess: () => {
          emit('update:open', false);
          emit('success');
        }
      }
    );
  } catch (error) {
    loading.value = false;
  }
};

// 处理取消
const handleCancel = () => {
  emit('update:open', false);
};
</script>

<style lang="less" scoped>
.analyze-container {
  padding: 8px 0;
}

.analyze-desc {
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.65);
}

.analyze-alert {
  margin-top: 16px;
}

/* 深色模式适配 */
:global(.dark) {
  .analyze-desc {
    color: rgba(255, 255, 255, 0.65);
  }
}
</style>
