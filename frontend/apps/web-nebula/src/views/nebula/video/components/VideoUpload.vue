<template>
  <Drawer
    :open="open"
    :title="'上传视频'"
    @update:open="$emit('update:open', $event)"
    :width="isMobile ? '100%' : '520px'"
    :height="isMobile ? '80%' : undefined"
    :placement="isMobile ? 'top' : 'right'"
    :footer-style="{ textAlign: 'right' }"
    @close="$emit('update:open', false)"
  >
    <Form ref="formRef" :model="formState" :rules="rules" layout="vertical">
      <Form.Item name="file" label="上传视频文件">
        <Upload
          v-model:file-list="fileList"
          name="file"
          accept="video/*"
          :before-upload="beforeUpload"
          :customRequest="customUpload"
          :multiple="false"
          :maxCount="1"
          :drag="true"
          class="full-width-upload"
        >
          <Flex vertical align="center" class="upload-drag-area">
            <Typography.Text class="upload-drag-icon">
              <UploadIcon />
            </Typography.Text>
            <Typography.Text class="upload-drag-text">点击或拖拽视频文件到此区域上传</Typography.Text>
            <Typography.Text type="secondary" class="upload-tip">支持MP4、WebM等常见视频格式</Typography.Text>
          </Flex>
        </Upload>
      </Form.Item>
      <Form.Item name="video_player_url" label="视频URL" help="输入视频的直接播放URL或上传视频文件">
        <Input
          v-model:value="formState.video_player_url"
          placeholder="请输入视频URL，例如：https://example.com/video.mp4"
          :maxlength="500"
        >
          <template #prefix>
            <LinkIcon />
          </template>
        </Input>
      </Form.Item>
      <Form.Item name="title" label="视频标题 (可选)">
        <Input
          v-model:value="formState.title"
          placeholder="请输入视频标题"
          :maxlength="200"
        />
      </Form.Item>
      <Form.Item name="desc" label="视频描述 (可选)">
        <Input.TextArea
          v-model:value="formState.desc"
          placeholder="添加视频描述..."
          :auto-size="{ minRows: 4, maxRows: 8 }"
          :maxlength="500"
          show-count
        />
      </Form.Item>
      <Alert
        type="info"
        show-icon
        message="上传提示"
        description="系统将使用AI分析视频内容，生成标签、概述和亮点。分析过程可能需要几分钟时间，请耐心等待。"
        class="upload-alert"
      />
    </Form>
    <template #footer>
      <Flex justify="end" gap="small">
        <Button @click="$emit('update:open', false)">取消</Button>
        <Button type="primary" :loading="loading" @click="handleConfirm">确定</Button>
      </Flex>
    </template>
  </Drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import { Drawer, Form, Input, Button, Alert, Flex, Typography, message, Upload } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { LinkIcon, UploadIcon } from '@vben/icons';
import { uploadUtils } from '#/utils/upload';

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:open', 'confirm']);

// 表单引用
const formRef = ref();

// 表单状态
const formState = reactive({
  video_player_url: '',
  title: '',
  desc: ''
});

// 文件列表
const fileList = ref<any[]>([]);

// 检测是否为移动设备
const isMobile = ref(window.innerWidth < 768);

// 监听窗口大小变化
window.addEventListener('resize', () => {
  isMobile.value = window.innerWidth < 768;
});

// 监听props.open变化，重置表单
watch(() => props.open, (newVal) => {
  if (!newVal) {
    // 抽屉关闭时重置表单
    formState.video_player_url = '';
    formState.title = '';
    formState.desc = '';
    fileList.value = [];
  }
});

// 监听loading状态变化
watch(() => props.loading, (newVal, oldVal) => {
  // 当loading从true变为false时，表示请求已完成
  if (oldVal === true && newVal === false) {
    // 延迟一点关闭抽屉，让用户能看到成功消息
    setTimeout(() => {
      emit('update:open', false);
    }, 500);
  }
});

// 表单验证规则
const rules = {
  video_player_url: [
    { required: false, message: '请输入视频URL或上传视频文件', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ]
};

// 上传前检查
const beforeUpload = (file: File) => {
  // 检查文件类型
  const isVideo = file.type.startsWith('video/');
  if (!isVideo) {
    message.error('请上传视频文件');
    return false;
  }

  // 检查文件大小（限制为1GB）
  const isLt1G = file.size / 1024 / 1024 < 1000;
  if (!isLt1G) {
    message.error('视频文件大小不能超过1GB');
    return false;
  }

  // 返回true允许自动上传，会调用customUpload方法
  return true;
};

// 自定义上传
const customUpload = async (options: any) => {
  const { file, onSuccess, onError } = options;

  try {
    // 使用uploadUtils处理上传
    const result = await uploadUtils.process(file, {
      bucketName: 'nebula',
      loading: ref(props.loading)
    });

    if (result && result.object_name) {
      // 更新表单状态
      formState.video_player_url = result.url;

      onSuccess(result, file);
    } else {
      onError(new Error('上传失败'));
    }
  } catch (error) {
    onError(error);
  }
};

// 处理确认
const handleConfirm = async () => {
  try {
    await formRef.value.validate();

    // 检查是否有视频URL
    if (!formState.video_player_url) {
      message.error('请输入视频URL或上传视频文件');
      return;
    }

    emit('confirm', {
      video_player_url: formState.video_player_url,
      title: formState.title,
      desc: formState.desc
    });
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};
</script>

<style lang="less" scoped>
.upload-alert {
  margin-top: 16px;
}

.full-width-upload {
  width: 100%;
}

.upload-drag-area {
  padding: 24px;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  transition: border-color 0.3s;

  &:hover {
    border-color: #1890ff;
  }
}

.upload-drag-icon {
  font-size: 48px;
  color: #40a9ff;
  margin-bottom: 8px;
}

.upload-drag-text {
  font-size: 16px;
  margin-bottom: 4px;
}

.upload-tip {
  font-size: 14px;
}


</style>
