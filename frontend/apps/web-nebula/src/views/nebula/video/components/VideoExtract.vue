<template>
  <Flex vertical>
    <Spin :spinning="isExtracting" tip="正在提取视频信息...">
      <Form
        ref="formRef"
        :model="formState"
        :rules="rules"
        layout="vertical"
        @finish="onFinish"
      >
        <Form.Item label="视频链接" name="extractUrl">
          <Input
            v-model:value="formState.extractUrl"
            placeholder="请输入抖音/YouTube/Twitter视频分享链接"
            :disabled="isExtracting"
            @press-enter="submitForm"
            allow-clear
          >
            <template #prefix>
              <LinkIcon />
            </template>
          </Input>
          <Typography.Text type="secondary" class="extract-tips">
            支持抖音、YouTube、Twitter等平台的视频分享链接
          </Typography.Text>
        </Form.Item>
        <Form.Item>
          <Button
            type="primary"
            @click="submitForm"
            :loading="isExtracting"
            :disabled="isExtracting"
          >
            <template #icon><DownloadIcon /></template>
            提取视频
          </Button>
        </Form.Item>
      </Form>
    </Spin>
  </Flex>
</template>

<script lang="ts" setup>
import { ref, reactive, defineEmits } from 'vue';
import type { FormInstance } from 'ant-design-vue/es/form';
import { Form, Input, Button, Spin, Typography, Flex } from 'ant-design-vue';
import { apiService } from '#/services/api';
import { useActionService } from '#/services/action';
import { notifyUtils } from '#/utils/notify';

// 导入 Iconify 图标
import {
  LinkIcon,
  DownloadIcon
} from '@vben/icons';

// 定义组件事件
const emit = defineEmits(['extract-success']);

// 表单引用
const formRef = ref<FormInstance>();

// 表单状态
const formState = reactive({
  extractUrl: ''
});

// 加载状态
const isExtracting = ref(false);

// 表单验证规则
const rules = {
  extractUrl: [
    { required: true, message: '请输入视频分享text', trigger: 'blur' },
  ]
};

// 使用 Action 服务单例
const actionService = useActionService();

// 提交表单
const submitForm = () => {
  formRef.value?.validate()
    .then(() => {
      extractVideo();
    })
    .catch(() => {
      // 表单验证失败，错误已由表单组件处理
    });
};

// 表单提交成功回调
const onFinish = (values: any) => {
  extractVideo();
};

// 提取视频信息
const extractVideo = async () => {
  // 防止重复提交
  if (isExtracting.value) return;

  try {
    // 手动设置加载状态
    isExtracting.value = true;

    // 使用 action 服务提交请求
    await actionService.submit(
      apiService.video.extract,
      {
        body: { share_text: formState.extractUrl }
      },
      {
        // 不使用loading参数，手动管理loading状态
        showNotify: false,
        loading: isExtracting,
        onSuccess: (response: any) => {
          // 将提取结果发送给父组件
          emit('extract-success', response);
        }
      }
    );
  } catch (error) {
    // 错误已在action中处理
    isExtracting.value = false;
  }
};
</script>

<style lang="less" scoped>
.extract-tips {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 4px;
}

/* 深色模式适配 */
:global(.dark) {
  .extract-tips {
    color: rgba(255, 255, 255, 0.45);
  }
}
</style>
