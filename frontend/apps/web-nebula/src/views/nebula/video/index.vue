<template>
  <View>
    <ViewContainer ref="dataCardRef" variant="page">
      <!-- 头部区域 -->
      <template #header>
        <ViewHeader ref="dataHeaderRef">
          <template #title>
            <span>视频管理</span>
          </template>

          <template #toolbar>
            <Space>
              <!-- 标签选择器 -->
              <Select
                v-model:value="selectedTags"
                placeholder="选择标签过滤"
                mode="multiple"
                style="min-width: 280px"
                allow-clear
                @change="handleTagChange"
              >
                <Select.Option
                  v-for="tag in allTags"
                  :key="tag"
                  :value="tag"
                >
                  {{ tag }}
                </Select.Option>
              </Select>

              <!-- 搜索栏 -->
              <Flex align="center" class="search-with-refresh">
                <Input
                  v-model:value="searchText"
                  placeholder="搜索视频..."
                  style="width: 220px"
                  allow-clear
                  @press-enter="handleSearch"
                  @clear="handleSearch"
                >
                  <template #suffix>
                    <SearchIcon @click="handleSearch" />
                  </template>
                </Input>
                <Button
                  type="text"
                  class="refresh-btn"
                  @click="fetchVideoList"
                  :disabled="loading.list.value"
                  title="刷新"
                >
                  <template #icon><RefreshIcon /></template>
                </Button>
              </Flex>
            </Space>
          </template>

          <template #actions>
            <Space>
              <!-- 提取视频按钮 -->
              <Button type="primary" @click="showExtractDrawer">
                <template #icon><DownloadIcon /></template>
                提取视频
              </Button>
              <!-- 上传视频按钮 -->
              <Button type="primary" @click="showUploadDrawer">
                <template #icon><UploadIcon /></template>
                上传视频
              </Button>
            </Space>
          </template>
        </ViewHeader>
      </template>

      <!-- 内容区域 -->
      <template #body>
        <ViewBody
          :extraPadding="16"
          :minHeight="300"
        >
          <!-- 视频列表 -->
          <VideoList
            :videoList="videoList"
            :loading="loading.list.value"
            @analyze="confirmAnalyzeVideo"
            @delete="confirmDeleteVideo"
            @filter-tag="filterByTag"
          />
        </ViewBody>
      </template>

      <!-- 底部区域 -->
      <template #footer>
        <ViewFooter ref="dataFooterRef">
          <template #left>
            <Typography.Text class="record-count">共 {{ total }} 条记录</Typography.Text>
          </template>
          <Pagination
            :total="total"
            :current="current"
            :pageSize="pageSize"
            :showSizeChanger="true"
            :pageSizeOptions="['12', '24', '36', '48']"
            :responsive="true"
            @change="handlePageChange"
            @showSizeChange="(_current, size) => { handlePageChange(1, size); }"
          />
        </ViewFooter>
      </template>
    </ViewContainer>

    <!-- 视频提取抽屉 -->
    <Drawer
      title="视频提取"
      :width="drawerWidth"
      :open="extractDrawerVisible"
      @close="closeExtractDrawer"
      :bodyStyle="{ paddingBottom: '80px' }"
      :headerStyle="{ borderBottom: '1px solid #f0f0f0' }"
      destroyOnClose
    >
      <VideoExtract @extract-success="handleExtractSuccess" />

      <!-- 提取结果预览 -->
      <Flex vertical v-if="extractedVideo" class="mt-4">
        <Spin :spinning="loading.importingVideo.value" tip="正在保存视频信息...">
          <Card :bordered="true" class="extract-result-card">
            <template #title>
              <Flex align="center" class="card-header-content">
                <Flex align="center" gap="8px" class="card-header-left">
                  <Typography.Text v-if="extractedVideo.video_id" class="video-id-header">
                    ID: {{ extractedVideo.video_id }}
                  </Typography.Text>
                </Flex>
              </Flex>
            </template>
            <template #extra>
              <Button
                type="primary"
                danger
                @click="saveExtractedVideo"
                :loading="loading.importingVideo.value"
                :disabled="loading.importingVideo.value"
                class="save-video-btn"
              >
                <template #icon><SaveIcon /></template>
                保存视频
              </Button>
            </template>
            <!-- 视频预览区域 -->
            <Flex vertical class="video-preview-section">
              <Flex justify="center" align="center" class="video-preview-container">
                <video
                  v-if="extractedVideo.player_url"
                  controls
                  class="video-preview"
                  :poster="extractedVideo.cover_url"
                >
                  <source :src="extractedVideo.player_url" type="video/mp4">
                  您的浏览器不支持视频播放
                </video>
                <Image
                  v-else-if="extractedVideo.cover_url"
                  :src="extractedVideo.cover_url"
                  class="image-preview"
                  alt="视频封面"
                  fallback="data:image/png;base64,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"
                  :preview="false"
                />
              </Flex>
            </Flex>

            <!-- 视频信息区域 -->
            <Flex vertical class="video-info-section">
              <!-- 标题区域 -->
              <Flex vertical class="video-title-section">
                <Typography.Title :level="2" class="video-title">{{ extractedVideo.title || '未知标题' }}</Typography.Title>
                <Flex wrap="wrap" gap="6px" class="video-meta">
                  <Flex class="video-platform">
                    <Tag :color="getPlatformColor(extractedVideo.from)">
                      {{ getPlatformName(extractedVideo.from) }}
                    </Tag>
                  </Flex>
                  <Flex class="video-duration">
                    <Tag color="blue">
                      <ClockIcon /> {{ formatDuration(extractedVideo.duration) }}
                    </Tag>
                  </Flex>
                  <Flex v-if="extractedVideo.watch_url" class="video-link">
                    <Tooltip :title="extractedVideo.watch_url">
                      <Tag color="purple" style="cursor: pointer;" @click="openUrl(extractedVideo.watch_url)">
                        <LinkIcon /> 原始链接
                      </Tag>
                    </Tooltip>
                  </Flex>
                </Flex>
              </Flex>

              <!-- 描述区域 -->
              <Flex vertical class="video-description-section">
                <Typography.Text strong class="section-title">视频描述</Typography.Text>
                <Flex vertical class="video-description">
                  <Typography.Paragraph>{{ extractedVideo.desc || '无描述' }}</Typography.Paragraph>
                </Flex>
              </Flex>

              <!-- 其他信息区域已移至卡片头部 -->
            </Flex>

            <!-- 操作按钮区域已移至卡片头部 -->
          </Card>
        </Spin>
      </Flex>

      <template #footer>
        <Flex justify="end">
          <Button @click="closeExtractDrawer">关闭</Button>
        </Flex>
      </template>
    </Drawer>

    <!-- 视频上传抽屉 -->
    <VideoUpload
      :open="uploadDrawerVisible"
      :loading="loading.uploadVideo.value"
      @update:open="uploadDrawerVisible = $event"
      @confirm="handleUploadConfirm"
    />

    <!-- 视频详情对话框已移至独立页面 -->

    <!-- 删除确认对话框使用函数式调用 -->
  </View>
</template>

<script lang="ts" setup>
// 视频管理页面
import { ref, onMounted, onUnmounted, computed, h } from 'vue';
import { Modal, Pagination, Space, Image, Select, Input, Button, Flex, Drawer, Spin, Card, Tag, Tooltip, Typography } from 'ant-design-vue';

import ViewContainer from '../../../components/ViewContainer.vue';
import ViewHeader from '../../../components/ViewHeader.vue';
import ViewBody from '../../../components/ViewBody.vue';
import ViewFooter from '../../../components/ViewFooter.vue';

// 导入 Iconify 图标
import {
  SearchIcon,
  RefreshIcon,
  DownloadIcon,
  ClockIcon,
  SaveIcon,
  LinkIcon,
  UploadIcon
} from '@vben/icons';
import { useWindowSize } from '@vueuse/core';
import { useActionService } from '#/services/action';
import { apiService } from '#/services/api';
import { notifyUtils } from '#/utils/notify';
import QueryParamStorage from '#/utils/queryParamStorage';

import VideoList from './components/VideoCard.vue';
import VideoExtract from './components/VideoExtract.vue';
import VideoUpload from './components/VideoUpload.vue';

const tabStorage = new QueryParamStorage(location.pathname);

// 使用窗口大小计算抽屉宽度
const { width } = useWindowSize();
const drawerWidth = computed(() => {
  return width.value < 768 ? '100%' : '500px';
});

// 获取action服务
const actionService = useActionService();


// 组件引用
const dataCardRef = ref(null);
const dataHeaderRef = ref(null);
const dataFooterRef = ref(null);


// 列表数据
const videoList = ref<any[]>([]);
const total = ref(0);
const current = ref(1);
const pageSize = ref(12);
const searchText = ref('');

// 标签数据
const allTags = ref<string[]>([]);
const selectedTags = ref<string[]>([]);

// 抽屉显示状态
const extractDrawerVisible = ref(false);
const uploadDrawerVisible = ref(false);
const extractedVideo = ref<any>(null);

// 对话框显示状态已移除，视频详情页使用独立路由

// 当前选中的视频（用于删除和分析操作）
const currentVideo = ref<any>(null);

// 加载状态 - 使用ref而不是reactive，与原始JavaScript项目保持一致
const loading = {
  list: ref(false),
  delete: ref(false),
  analyze: ref(false),
  importingVideo: ref(false),
  uploadVideo: ref(false)
};

// 资源服务器前缀（用于VideoDetailsDialog组件）

// 从URL和QueryParamStorage中读取参数的简化函数
const initParams = () => {
  current.value = (tabStorage.get('skip') || 0) / (tabStorage.get('limit') || pageSize.value) + 1;
  pageSize.value = tabStorage.get('limit') || pageSize.value;
  searchText.value = tabStorage.get('keyword') || '';
  // 使用forceArray参数确保tags参数始终是数组
  selectedTags.value = tabStorage.get('tags', true) || [];
};

// 更新URL参数
const updateUrlParams = (params: Record<string, any>) => {
  tabStorage.saveData(params);
  window.history.pushState({}, '', tabStorage.getPathQueryString()); // 更新URL，不刷新页面
};



// 生命周期钩子
onMounted(() => {
  // 初始化参数
  initParams();
  fetchVideoList();
});

// 组件卸载时移除事件监听
onUnmounted(() => {
});

// 获取视频列表
const fetchVideoList = async () => {
    const params: any = {
      skip: (current.value - 1) * pageSize.value,
      limit: pageSize.value
    };

    // 添加搜索关键字
    if (searchText.value) {
      params.keyword = searchText.value;
    }

    // 添加标签过滤
    if (selectedTags.value && selectedTags.value.length > 0) {
      params.tags = selectedTags.value;
    }

    updateUrlParams(params);

    await actionService.submit(
      apiService.video.list,
      params,
      {
        loading: loading.list,
        showNotify: false,
        onSuccess: (response: any) => {
          videoList.value = response.data || [];
          total.value = response.total || 0;
          // 提取所有标签
          extractTags();
        },
        onError: () => {
          videoList.value = [];
          total.value = 0;
        }
      }
    );
};

// 从视频列表中提取所有标签
const extractTags = () => {
  const tagSet = new Set<string>();

  // 遍历所有视频，收集标签
  videoList.value.forEach(video => {
    if (video.tags && Array.isArray(video.tags)) {
      video.tags.forEach((tag: string) => {
        tagSet.add(tag);
      });
    }
  });

  // 转换为数组并排序
  allTags.value = Array.from(tagSet).sort();
};

// 处理分页变化
const handlePageChange = (page: number, size: number) => {
  current.value = page;
  pageSize.value = size;
  fetchVideoList();
};

// 处理标签变化
const handleTagChange = () => {
  current.value = 1;
  fetchVideoList();
};

// 通过标签过滤
const filterByTag = (tag: string) => {
  // 如果标签已经选中，则不重复添加
  if (!selectedTags.value.includes(tag)) {
    selectedTags.value.push(tag);
    handleTagChange();
  }
};

// 处理搜索
const handleSearch = () => {
  current.value = 1;
  fetchVideoList();
};

// 显示视频提取抽屉
const showExtractDrawer = () => {
  extractDrawerVisible.value = true;
};

// 关闭视频提取抽屉
const closeExtractDrawer = () => {
  extractDrawerVisible.value = false;
  extractedVideo.value = null;
};

// 显示视频上传抽屉
const showUploadDrawer = () => {
  uploadDrawerVisible.value = true;
};



// 处理视频提取成功
const handleExtractSuccess = (videoInfo: any) => {
  extractedVideo.value = videoInfo;
};

// 保存提取的视频
const saveExtractedVideo = async () => {
  if (!extractedVideo.value) {
    notifyUtils.warning('无效的视频信息', '请先提取视频信息');
    return;
  }

  const videoInfo = extractedVideo.value;
  closeExtractDrawer();
  await actionService.submit(
    apiService.video.importFromShare,
    {
      body: {
        video_id: videoInfo.video_id || '',
        title: videoInfo.title || '',
        desc: videoInfo.desc || '',
        duration: videoInfo.duration || 0,
        cover_url: videoInfo.cover_url || '',
        player_url: videoInfo.player_url || '',
        watch_url: videoInfo.watch_url || '',
        from_platform: videoInfo.from || ''
      }
    },
    {
      // 不使用loading参数，手动管理loading状态
      loading: loading.importingVideo,
      showSubmitNotify: true,
      submitNotifyTitle: '视频导入',
      submitNotifyMsg: '视频导入任务已提交，正在后台处理中',
      onSuccess: () => {

      }
    }
  );
};

// 处理视频上传表单提交
const handleUploadConfirm = async (formData: any) => {
  await actionService.submit(
    apiService.video.create_from_url,
    {
      body: {
        video_player_url: formData.video_player_url,
        title: formData.title || '',
        desc: formData.desc || ''
      }
    },
    {
      loading: loading.uploadVideo,
      showSubmitNotify: true,
      submitNotifyTitle: '视频上传',
      submitNotifyMsg: '视频上传任务已提交，正在后台处理中',
      onAsyncTaskSubmitted: (response) => {
        console.log('视频上传任务已提交，任务ID:', response._task_id);
        uploadDrawerVisible.value = false;
      },
      onSuccess: () => {
        // 刷新视频列表
        fetchVideoList();
      },
      // 添加onError回调，确保即使出错也能关闭抽屉
      onError: () => {
        // 延迟一点关闭抽屉，让用户能看到错误消息
        setTimeout(() => {
          uploadDrawerVisible.value = false;
        }, 1500);
      }
    }
  );
};

// 查看视频详情函数已移至VideoCard组件中，使用路由导航

// 确认删除视频
const confirmDeleteVideo = (video: any) => {
  currentVideo.value = video;

  // 使用函数式调用确认对话框
  Modal.confirm({
    title: '确认删除',
    content: h('div', {}, [
      h('p', {}, `确定要删除视频 "${video.title || '未命名视频'}" 吗？此操作不可恢复。`)
    ]),
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    okButtonProps: { loading: loading.delete.value },
    onOk: () => deleteVideo()
  });
};

// 删除视频
const deleteVideo = async () => {
  if (!currentVideo.value) return;
  await actionService.submit(
    apiService.video.delete,
    { id: currentVideo.value.id },
    {
      loading: loading.delete,
      onSuccess: () => {
        fetchVideoList();
      }
    }
  );
};

// 确认分析视频
const confirmAnalyzeVideo = (video: any) => {
  currentVideo.value = video;

  // 使用函数式调用确认对话框
  Modal.confirm({
    title: '确认AI分析',
    content: h('div', {}, [
      h('p', {}, `确定要对视频 "${video.title || '未命名视频'}" 进行AI分析吗？`),
      h('p', {}, '注意：分析过程可能需要一定时间，请耐心等待。')
    ]),
    okText: '开始分析',
    cancelText: '取消',
    // 不使用loading状态，以便立即关闭对话框
    onOk: () => {
      // 点击确认后立即开始分析，不等待对话框关闭
      actionService.submit(
        apiService.video.analyze,
        {
          id: currentVideo.value.id
        },
        {
          // 不使用loading参数，手动管理loading状态
          showSubmitNotify: true,
          submitNotifyTitle: '开始分析',
          submitNotifyMsg: 'AI分析已启动，分析完成后将自动更新视频信息',
          onSuccess: () => {
          }
        }
      );
      // 返回true表示对话框可以关闭
      return Promise.resolve(true);
    }
  });
};

// 处理标签更新函数已移除，视频详情页使用独立路由

// 格式化时长
const formatDuration = (seconds: number) => {
  if (!seconds || isNaN(seconds)) return '未知时长';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
};

// 获取平台名称
const getPlatformName = (platform: string) => {
  const platformMap: Record<string, string> = {
    'douyin': '抖音',
    'youtube': 'YouTube',
    'twitter': 'Twitter/X',
    'bilibili': 'B站'
  };

  return platformMap[platform] || platform || '未知平台';
};

// 获取平台颜色
const getPlatformColor = (platform: string) => {
  const colorMap: Record<string, string> = {
    'douyin': 'volcano',
    'youtube': 'red',
    'twitter': 'blue',
    'bilibili': 'cyan'
  };

  return colorMap[platform] || 'default';
};

// 打开URL
const openUrl = (url: string) => {
  if (!url) return;
  window.open(url, '_blank');
};
</script>

<style lang="less" scoped>
// .video-container {
//   padding: 16px 16px 0px 16px;
//   display: flex;
//   flex-direction: column;

//   box-sizing: border-box;
// }

.search-with-refresh {
  display: flex;
  align-items: center;
  position: relative;
}

.refresh-btn {
  margin-left: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.3s;
}

.mt-4 {
  margin-top: 1rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.text-right {
  text-align: right;
}

/* 提取结果卡片样式 */
.extract-result-card {
  margin-bottom: 12px;
  overflow: hidden;

  :deep(.ant-card-body) {
    padding: 12px 12px 8px;
  }

  :deep(.ant-card-head) {
    min-height: 40px;
    padding: 0 12px;
    font-size: 13px;
    background-color: #f5f5f5;

    .ant-card-head-title {
      padding: 10px 0;
    }
  }

  .save-video-btn {
    background-color: #f50;
    border-color: #f50;

    &:hover, &:focus {
      background-color: #ff7a45;
      border-color: #ff7a45;
    }

    &:active {
      background-color: #d84a1b;
      border-color: #d84a1b;
    }
  }
}

/* 卡片头部内容样式 */
.card-header-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.card-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.video-id-header {
  font-family: monospace;
  font-size: 12px;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
  color: #666;
  margin-left: 8px;
}

/* 视频预览区域 */
.video-preview-section {
  margin-bottom: 12px;
}

.video-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.video-preview {
  max-width: 100%;
  max-height: 320px;
}

.image-preview {
  max-width: 100%;
  max-height: 320px;
  object-fit: contain;
}

/* 视频信息区域 */
.video-info-section {
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 6px;
  margin-bottom: 12px;
  font-size: 13px;
}

/* 标题区域 */
.video-title-section {
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.video-title {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 6px;
  line-height: 1.3;
  color: #333;
}

.video-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 4px;

  :deep(.ant-tag) {
    margin-right: 0;
    font-size: 12px;
    line-height: 18px;
    height: 20px;
    padding: 0 6px;
  }
}

/* 描述区域 */
.video-description-section {
  margin-bottom: 10px;
}

.section-title {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 6px;
  color: #666;
}

.video-description {
  background-color: #fff;
  padding: 8px 10px;
  border-radius: 4px;
  border: 1px solid #eee;
  max-height: 80px;
  overflow-y: auto;

  p {
    margin: 0;
    font-size: 12px;
    line-height: 1.5;
    color: #555;
    white-space: pre-wrap;
    word-break: break-word;
  }
}

/* 其他信息区域 */
.video-extra-info {
  margin-bottom: 10px;
}

.video-id {
  font-family: monospace;
  font-size: 12px;
  background-color: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
  color: #666;
  word-break: break-all;
}

/* 操作按钮区域 */
.action-buttons-section {
  display: flex;
  justify-content: flex-end;
  gap: 6px;
  margin-top: 8px;

  :deep(.ant-btn) {
    font-size: 13px;
    height: 28px;
    padding: 0 10px;

    .anticon {
      font-size: 13px;
    }
  }
}

</style>
