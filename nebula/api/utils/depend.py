from fastapi import Header
from nebula.core.protocol.base import ResponseMode


async def get_response_mode(
    x_response_mode: str = Header("sync", alias="X-Response-Mode")
) -> ResponseMode:
    """从请求头获取响应模式"""
    return ResponseMode(x_response_mode.lower())

async def get_callback_url(
    x_callback_url: str = Header(None, alias="X-Callback-Url")
) -> str:
    """从请求头获取回调URL"""
    return x_callback_url or ""
