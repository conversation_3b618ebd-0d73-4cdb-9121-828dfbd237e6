import fastapi
import fastapi.responses
import fastapi.exceptions
import starlette.exceptions

import traceback
import logging

# 创建日志记录器
logger = logging.getLogger("api.exceptions")

class APIException(Exception):
    """API自定义异常基类"""
    def __init__(self, errcode: int = 1, msg: str = "错误", status_code: int = 400):
        self.errcode = errcode
        self.msg = msg
        self.status_code = status_code
        super().__init__(self.msg)

async def api_exception_handler(request: fastapi.Request, exc: APIException):
    """处理自定义API异常"""
    # 记录异常但不包含堆栈
    logger.error(f"API异常: {exc.msg} (errcode={exc.errcode}, status={exc.status_code})", exc_info=True)
    import nebula.api.utils.response as response
    return fastapi.responses.JSONResponse(
        status_code=exc.status_code,
        content=response.ResponseBase(errcode=exc.errcode, msg=exc.msg).dict()
    )

async def exception_handler(request: fastapi.Request, exc: Exception):
    """处理所有异常"""
    # 严重异常，记录到异常日志文件，包含堆栈
    logger.error(f"服务器内部错误: {str(exc)}", exc_info=True)
    logger.error(f"{request.url} {str(exc)}")
    import nebula.api.utils.response as response
    return fastapi.responses.JSONResponse(
        status_code=fastapi.status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=response.ResponseBase(errcode=500, msg=str(exc)).dict(),
        # 直接在创建响应时设置CORS头 (Exception 有可能不是HTTPException,中间件还未加载就被中断,这是可能会有跨域问题)
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "*",
            "Access-Control-Allow-Headers": "*",
            "Access-Control-Allow-Credentials": "true"
        }
    )

async def http_exception_handler(request: fastapi.Request, exc: starlette.exceptions.HTTPException):
    """处理HTTP异常"""
    # 记录异常但不包含堆栈
    logger.error(f"HTTP异常: {exc.detail} (status={exc.status_code})", exc_info=True)
    import nebula.api.utils.response as response
    return fastapi.responses.JSONResponse(
        status_code=exc.status_code,
        content=response.ResponseBase(errcode=exc.status_code, msg=exc.detail).dict()
    )

async def validation_exception_handler(request: fastapi.Request, exc: fastapi.exceptions.RequestValidationError):
    """处理请求验证异常"""
    # 记录异常但不包含堆栈
    logger.error(f"请求验证异常: {str(exc.errors())}", exc_info=True)
    import nebula.api.utils.response as response
    return fastapi.responses.JSONResponse(
        status_code=fastapi.status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=response.ResponseBase(
            errcode=422,
            msg="请求数据验证失败",
            data={"errors": exc.errors()}
        ).dict()
    ) 