#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API层认证工具
将core层框架无关的认证服务适配到FastAPI
"""

import logging
from typing import Dict, Optional, List
from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from nebula.core.service.auth import AuthService
from pydantic import BaseModel
from nebula.core.service import provider
from nebula.core.infrastructure import get_logger

# 获取日志记录器
logger = get_logger(__name__)

# 创建HTTP Bearer鉴权处理器
# 

# Token响应模型
class Token(BaseModel):
    """令牌响应模型"""
    access_token: str
    token_type: str = "bearer"

class UserProfile(BaseModel):
    """用户信息模型"""
    username: str
    is_admin: bool
    scopes: List[str]

async def authenticate_api_token(
    credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())
) -> UserProfile:
    """
    验证API请求中的JWT令牌
    
    Args:
        credentials: HTTP Bearer凭证
        
    Returns:
        用户信息对象
        
    Raises:
        HTTPException: 令牌验证失败
    """
    try:
        # import asyncio
        # await asyncio.sleep(1)
        # 获取认证服务
        auth_service = provider.get_auth_service()
        
        # 验证令牌
        user_info = await auth_service.authenticate_token(credentials.credentials)
        
        # 构造用户信息响应
        return UserProfile(
            username=user_info["username"],
            is_admin='admin' in user_info["scopes"],
            scopes=user_info["scopes"]
        )
        
    except Exception as e:
        logger.warning(f"API令牌验证失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"}
        ) 