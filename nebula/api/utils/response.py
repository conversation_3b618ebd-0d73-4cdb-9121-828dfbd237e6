from fastapi import HTTPException
from typing import Any, Generic, TypeVar, Optional, Dict, Callable, Union
from pydantic import BaseModel, create_model, Field
from nebula.api.utils.exceptions import APIException
from nebula.core.infrastructure import NebulaException, NotFoundError, ValidationError, ExternalServiceError, exceptions
from functools import wraps
from nebula.core.protocol import ResponseMode
from nebula.core.tasks.interface import AbstractTaskScheduler, Task
import importlib
import sys
import logging
from threading import Lock
import uuid


logger = logging.getLogger(__name__)

T = TypeVar('T')


class ResponseBase(BaseModel, Generic[T]):
    """标准API响应格式

    所有API响应都将包含以下固定字段:
    - errcode: 错误码，0表示成功，非0表示错误
    - msg: 响应消息

    此外，可以包含任意其他顶级字段，例如:
    {
        "errcode": 0,
        "msg": "登录成功",
        "token": "xxx",
        "token_type": "bearer",
        "expires_in": 3600
    }

    或者:
    {
        "errcode": 0,
        "msg": "ok",
        "items": [...],
        "total": 100,
        "page": 1,
        "limit": 20
    }
    """
    errcode: int = 0
    msg: str = "ok"

    class Config:
        extra = "allow"  # 允许额外字段

def success_response(msg: str = "ok", **kwargs) -> ResponseBase:
    """创建成功响应

    Args:
        msg: 响应消息
        **kwargs: 其他任意顶级字段，这些字段将直接添加到响应JSON的顶级

    Returns:
        标准响应对象

    Example:
        >>> success_response(token="xxx", user_id=123)
        将生成:
        {
            "errcode": 0,
            "msg": "ok",
            "token": "xxx",
            "user_id": 123
        }
    """
    response = ResponseBase(errcode=0, msg=msg)
    for k, v in kwargs.items():
        setattr(response, k, v)
    return response

def asynctask_response(task: Union[str, Task], status: str = "started") -> ResponseBase:
    """创建一个异步任务提交的响应

    Args:
        task: 任务对象或任务ID
        status: 任务状态

    Returns:
        标准响应对象

    Example:
        >>> asynctask_response(task_id="123", status="started")
        将生成:
        {
            "errcode": 0,
            "msg": "任务已提交处理队列",
            "_task_id": "123",
            "_status": "started"
        }
    """
    # 获取任务ID
    task_id = task.task_id if isinstance(task, Task) else task

    # 创建基础响应对象
    return ResponseBase(**dict(errcode=0, msg="任务已提交处理队列", _task_id=task_id, _status=status))


def api_response(func: Callable):
    """API响应装饰器，统一处理异常并格式化返回结果"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            result = await func(*args, **kwargs)
            return result
        except HTTPException as e:
            # 明确处理HTTPException，保留其status_code和detail信息
            raise APIException(errcode=e.status_code, msg=e.detail, status_code=501)
        except (NotFoundError, ValidationError) as e:
            raise APIException(errcode=400, msg=str(e), status_code=400)
        except exceptions.BusinessError as e:
            raise APIException(errcode=422, msg=str(e), status_code=422)
        except ExternalServiceError as e:
            raise APIException(errcode=502, msg=f"外部错误:{str(e)}", status_code=502)
        except Exception as e:
            raise APIException(errcode=500, msg=f"服务器内部错误:{str(e)}", status_code=500)
    return wrapper

def delay_response(func: Callable):
    """延迟响应装饰器，延迟响应时间"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        import asyncio
        await asyncio.sleep(2)
        return await func(*args, **kwargs)
    return wrapper


async def dispatch_task(
    task_identifier: Callable,
    metadata: Dict[str, Any],
    response_mode: ResponseMode = ResponseMode.SYNC,
    callback: str = "",
    task_id: Optional[str] = None
) -> ResponseBase:
    """
    直接调度任务并返回标准响应

    Args:
        task_identifier: 任务标识符或任务函数，如果是函数，将自动提取任务名称
        metadata: 任务参数
        response_mode: 响应模式，同步或异步
        callback: 回调URL
        task_id: 可选的任务ID

    Returns:
        ResponseBase: 标准响应对象

    Example:
        >>> await dispatch_task("memo.create_text", {"content": "测试"})
        >>> await dispatch_task(memo.create_text_memo, {"content": "测试"})
        将生成:
        {
            "errcode": 0,
            "msg": "创建文本备忘录完成",
            ...其他业务字段
        }
    """

    from nebula.core.tasks.scheduler import TaskiqScheduler
    task_scheduler = TaskiqScheduler()

    # 如果task_identifier是函数，提取任务名称
    # if callable(task_identifier):
    #     # 尝试从函数的task_name属性获取任务名称
    #     if hasattr(task_identifier, "task_name"):
    #         task_identifier = task_identifier.task_name
    #     # 如果没有task_name属性，使用函数名
    #     else:
    #         task_identifier = task_identifier.__name__

    # 调度任务
    task = await task_scheduler.schedule_task(
        task_identifier=task_identifier,
        metadata=metadata,
        task_id=task_id or str(uuid.uuid4()),
        labels={
            "response_mode": response_mode,
            "callback": callback
        }
    )

    if response_mode != ResponseMode.SYNC:
        return asynctask_response(task.task_id)

    try:
        # 等待任务结果
        result = await task_scheduler.wait_task_result(task)

        # 处理错误
        if result["status"] == "error":
            raise exceptions.BusinessError(result["error"])

        # 处理成功结果
        task_result = result["result"]
        return success_response(
            msg=task_result.pop("message", "操作成功"),
            **task_result
        )
    except exceptions.TimeoutError as e:
        raise exceptions.TimeoutError(f"任务处理超时: {str(e)}")

# 保留旧函数以便兼容现有代码，但标记为已弃用
async def dispatch_message_with_response(task_scheduler: AbstractTaskScheduler, message: Any) -> ResponseBase:
    """
    处理同步/异步任务并返回标准响应（已弃用，请使用dispatch_task）

    Args:
        task_scheduler: 任务调度器实例
        message: 任务消息(必须包含 task_id 和 response_mode 属性)

    Returns:
        ResponseBase: 标准响应对象
    """
    # 根据消息类型和动作确定任务名称
    task_name = f"{message.__class__.__name__.replace('TaskMessage', '').lower()}.{message.action.lower()}"

    # 调用新函数
    return await dispatch_task(
        task_identifier=task_name,
        metadata=message.metadata,
        response_mode=message.response_mode,
        callback=message.callback,
        task_id=message.task_id
    )