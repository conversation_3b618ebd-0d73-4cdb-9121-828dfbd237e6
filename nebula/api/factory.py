import fastapi
import logging
from contextlib import asynccontextmanager
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response
import fastapi.middleware.cors

from nebula.core import context, infrastructure
from nebula.api.utils import exceptions as api_exceptions
import nebula.api.routers as routers

logger = logging.getLogger("api")

COMPONENTS = [
    "database", "cache", "auth", "cloudflare", "message",
    "storage", "memo", "video", "gemini", "browser",
    "notification", "telegram", "misc", "taskman"
]

# 中间件：异常处理
class ExceptionHandlingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        try:
            return await call_next(request)
        except Exception as e:
            return await api_exceptions.exception_handler(request, e)

# 生命周期管理器
@asynccontextmanager
async def lifespan(app: fastapi.FastAPI):
    logger.info("API服务启动")
    await load_context()
    configure_framework_logging()
    yield
    try:
        await context.graceful_shutdown()
        logger.info("API服务优雅关闭完成")
    except Exception as e:
        logger.error(f"应用关闭时发生错误: {str(e)}", exc_info=True)

# 初始化上下文
async def load_context():
    try:
        await context.init_application(
            app_name="Nebula API",
            debug=True,
            components=COMPONENTS
        )
        logger.info("应用初始化完成")
    except Exception as e:
        logger.error(f"应用初始化失败: {str(e)}", exc_info=True)
        exit(1)

# 框架日志配置
def configure_framework_logging():
    settings = infrastructure.get_settings()
    if not settings.logging.console_show_exc:
        log = logging.getLogger("uvicorn.error")
        for h in log.handlers[:]:
            log.removeHandler(h)
        log.propagate = False

# 构造 app 实例
def create_app() -> fastapi.FastAPI:
    app = fastapi.FastAPI(
        title="Nebula API",
        description="Nebula项目API服务",
        version="1.0.0",
        docs_url="/api/docs",
        redoc_url="/api/redoc",
        openapi_url="/api/openapi.json",
        redirect_slashes=False,
        lifespan=lifespan
    )

    app.add_middleware(ExceptionHandlingMiddleware)

    app.add_middleware(
        fastapi.middleware.cors.CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    app.add_exception_handler(api_exceptions.APIException, api_exceptions.api_exception_handler)
    app.add_exception_handler(fastapi.exceptions.RequestValidationError, api_exceptions.validation_exception_handler)
    app.add_exception_handler(fastapi.exceptions.HTTPException, api_exceptions.http_exception_handler)
    app.add_exception_handler(Exception, api_exceptions.exception_handler)

    app.include_router(routers.api_router, prefix="/api")
    return app


# 顶层变量，供 uvicorn 使用
app = create_app()