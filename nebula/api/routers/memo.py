from typing import List, Optional, Union, Dict, Any
from fastapi import APIRouter, Depends, UploadFile, File, Query, Path, Body
from bson import ObjectId
from nebula.api.utils.depend import get_response_mode,get_callback_url
from nebula.core.repository.memo import Entity
from nebula.core.service.memo import MemoService
from nebula.core.infrastructure import log_context
from nebula.api.utils import auth
from nebula.core.service import provider
from nebula.core.protocol import ResponseMode
from nebula.api.utils.response import delay_response, success_response, ResponseBase, api_response, asynctask_response, dispatch_task
from nebula.core.tasks.interface import AbstractTaskScheduler
from nebula.core.infrastructure.storage import StorageService

import uuid
import logging

logger = logging.getLogger(__name__)


router = APIRouter()

@router.post("/text", response_model=ResponseBase)
@api_response
async def create_text_memo(
    memo: Entity.TextMemo = Body(...,description="文本备忘录"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    profile = Depends(auth.authenticate_api_token)
):
    """创建文本备忘录"""
    from nebula.core.tasks.definitions import memo as memo_tasks

    return await dispatch_task(
        task_identifier=memo_tasks.create_text_memo,
        metadata=memo.to_dict(),
        response_mode=response_mode,
        callback=callback
    )

@router.post("/file", response_model=ResponseBase)
@api_response
async def create_file_memo(
    memo: Entity.FileMemo = Body(...,description="文件备忘录"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    profile = Depends(auth.authenticate_api_token)
):
    """创建文件类型的备忘录"""
    from nebula.core.tasks.definitions import memo as memo_tasks

    return await dispatch_task(
        task_identifier=memo_tasks.create_file_memo,
        metadata=memo.to_dict(),
        response_mode=response_mode,
        callback=callback
    )

@router.post("/image", response_model=ResponseBase)
@api_response
async def create_image_memo(
    memo: Entity.ImageMemo = Body(...,description="图片备忘录"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    profile = Depends(auth.authenticate_api_token)
):
    """创建图片类型的备忘录"""
    from nebula.core.tasks.definitions import memo as memo_tasks

    return await dispatch_task(
        task_identifier=memo_tasks.create_image_memo,
        metadata=memo.to_dict(),
        response_mode=response_mode,
        callback=callback
    )

@router.post("/share", response_model=ResponseBase)
@api_response
async def create_share_memo(
    memo: Entity.ShareMemo = Body(...,description="分享备忘录"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    profile = Depends(auth.authenticate_api_token)
):
    """创建分享类型的备忘录"""
    from nebula.core.tasks.definitions import memo as memo_tasks

    return await dispatch_task(
        task_identifier=memo_tasks.create_share_memo,
        metadata=memo.to_dict(),
        response_mode=response_mode,
        callback=callback
    )

@router.get("", response_model=ResponseBase)
@api_response
@log_context
async def list_memos(
    keyword: Optional[str] = Query(None, description="搜索关键字"),
    skip: Optional[int] = Query(0, description="跳过数量"),
    limit: Optional[int] = Query(20, description="限制数量"),
    type: Optional[str] = Query(None, description="类型"),
    service: MemoService = Depends(provider.get_memo_service),
    storage_service: StorageService = Depends(provider.get_storage_service),
    profile = Depends(auth.authenticate_api_token)
):
    """获取备忘录列表"""
    memos = await service.list_memos(keyword, type, skip, limit)
    total = await service.count_memos(keyword, type)

    # 处理文件类型备忘录的签名URL
    memo_list = []
    for memo in memos:
        memo_dict = memo.to_dict()
        # 如果是文件或图片类型，并且有file_key字段，添加签名URL
        if memo.type in ['file', 'image'] and memo.file_key:
            try:
                # 获取签名URL，有效期1小时
                signed_url = await storage_service.get_presigned_url(
                    object_name=memo.file_key,
                    expires=3600,  # 1小时
                    bucket_name=service.bucket  # 使用memo服务配置的bucket
                )
                memo_dict['signed_url'] = signed_url
            except Exception as e:
                # 如果获取签名URL失败，记录错误但不中断处理
                logger.error(f"获取备忘录[{memo.id}]的签名URL失败: {str(e)}")

        memo_list.append(memo_dict)

    return success_response(
        data=memo_list,
        total=total,
        skip=skip,
        limit=limit
    )

@router.get("/{memo_id}", response_model=ResponseBase)
@api_response
async def get_memo(
    memo_id: str = Path(...,description="备忘录ID"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    storage_service: StorageService = Depends(provider.get_storage_service),
    service: MemoService = Depends(provider.get_memo_service),
    profile = Depends(auth.authenticate_api_token)
):
    """获取单个备忘录"""
    # 创建任务调度器实例

    # 发送任务并获取结果
    result = await dispatch_task(
        task_identifier="memo.get_memo",
        metadata={"id": memo_id},
        response_mode=response_mode,
        callback=callback
    )

    # 如果请求成功且返回的是文件或图片类型的备忘录，添加签名URL
    if result.get('success') and isinstance(result.get('memo'), dict):
        memo = result.get('memo')
        if memo.get('type') in ['file', 'image'] and memo.get('file_key'):
            try:
                # 获取签名URL，有效期1小时
                signed_url = await storage_service.get_presigned_url(
                    object_name=memo.get('file_key'),
                    expires=3600,  # 1小时
                    bucket_name=service.bucket  # 使用memo服务配置的bucket
                )
                memo['signed_url'] = signed_url
            except Exception as e:
                # 如果获取签名URL失败，记录错误但不中断处理
                logger.error(f"获取备忘录[{memo_id}]的签名URL失败: {str(e)}")

    return result

@router.put("/{memo_id}", response_model=ResponseBase)
@api_response
@log_context
async def update_memo(
    memo_id: str = Path(...,description="备忘录ID"),
    memo: Union[Entity.TextMemo, Entity.FileMemo, Entity.ImageMemo, Entity.ShareMemo] = Body(...,description="备忘录"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    profile = Depends(auth.authenticate_api_token)
):
    """更新备忘录"""
    from nebula.core.tasks.definitions import memo as memo_tasks

    # 添加ID到配置中
    config = memo.to_dict()
    config["id"] = memo_id

    return await dispatch_task(
        task_identifier=memo_tasks.update_memo,
        metadata=config,
        response_mode=response_mode,
        callback=callback
    )

@router.delete("/{memo_id}", response_model=ResponseBase)
@api_response
async def delete_memo(
    memo_id: str = Path(...,description="备忘录ID"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    profile = Depends(auth.authenticate_api_token)
):
    """删除备忘录"""
    from nebula.core.tasks.definitions import memo as memo_tasks

    return await dispatch_task(
        task_identifier=memo_tasks.delete_memo,
        metadata={"id": memo_id},
        response_mode=response_mode,
        callback=callback
    )

@router.post("/{memo_id}/analyze", response_model=ResponseBase)
@api_response
async def analyze_memo(
    memo_id: str = Path(..., description="备忘录ID"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    profile = Depends(auth.authenticate_api_token)
):
    """分析备忘录内容"""
    from nebula.core.tasks.definitions import memo as memo_tasks

    return await dispatch_task(
        task_identifier=memo_tasks.analyze_content,
        metadata={"id": memo_id},
        response_mode=response_mode,
        callback=callback
    )