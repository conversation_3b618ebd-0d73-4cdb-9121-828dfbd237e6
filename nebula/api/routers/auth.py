#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Form, Body
from pydantic import BaseModel
from nebula.core.service import provider
from nebula.api.utils.auth import Token, UserProfile, authenticate_api_token
from nebula.api.utils.response import ResponseBase, success_response
from nebula.api.utils.exceptions import APIException
from typing import Dict, Any

router = APIRouter()
logger = logging.getLogger(__name__)

class LoginRequest(BaseModel):
    username: str
    password: str

@router.post("/auth", response_model=ResponseBase, summary="获取访问令牌")
async def login_for_access_token(
    username: str = Body(...,description="用户名"),
    password: str = Body(...,description="密码")
):
    """
    获取访问令牌，认证信息已写死为admin/admin
    
    此接口仅用于测试，实际生产环境应当调用core层进行用户验证
    返回格式：
    {
        "errcode": 0,
        "msg": "ok",
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "token_type": "bearer"
    }
    """
    # 写死的用户名和密码
    if username != "xiatian" or password != "helloworld!":
        raise APIException(
            errcode=401, 
            msg="用户名或密码错误", 
            status_code=status.HTTP_401_UNAUTHORIZED
        )
        
    # 获取认证服务
    auth_service = provider.get_auth_service()
        
    # 生成JWT令牌
    access_token_expires = timedelta(minutes=30) # settings.TOKEN_EXPIRE_MINUTES)
    access_token = await auth_service.create_access_token(
        data={"username": "admin", "scopes": ["admin"]}, 
        expires_delta=access_token_expires
    )
    
    # 直接返回token作为顶级字段
    return success_response(
        token=access_token,
        token_type="bearer"
    )

@router.get("/profile", response_model=ResponseBase, summary="获取当前用户信息")
async def get_user_profile(user_info: Dict[str, Any] = Depends(authenticate_api_token)):
    """
    获取当前登录用户的信息，用于验证token有效性
    
    实际生产环境应当调用core层获取完整的用户信息
    """
    # 这里可以调用core层获取用户详细信息
    # 暂时返回简单的用户信息
    logger.info(f"获取当前登录用户信息: {user_info}")
    
    # 直接将用户信息作为顶级字段返回
    return success_response(
        username="xiatian",
        email="<EMAIL>", 
        is_admin=True
    ) 