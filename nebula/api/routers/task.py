#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
任务API路由模块

提供任务管理的API接口，包括：
1. 获取任务列表
2. 获取任务详情
3. 获取任务日志
4. 获取任务状态
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, Path, Query
from nebula.api.utils.depend import get_response_mode
from nebula.core.repository.taskinfo import Entity as TaskEntity
from nebula.core.service.taskman import TaskService
from nebula.api.utils import auth
from nebula.core.service import provider
from nebula.core.protocol import ResponseMode
from nebula.api.utils.response import ResponseBase, api_response, success_response
from nebula.core.infrastructure import log_context

import logging

# 创建日志记录器
logger = logging.getLogger(__name__)

# 创建路由实例
router = APIRouter()


@router.get("", response_model=ResponseBase)
@api_response
async def list_tasks(
    status: Optional[str] = Query(None, description="任务状态"),
    keywords: Optional[str] = Query(None, description="搜索关键词"),
    skip: int = Query(0, description="跳过数量"),
    limit: int = Query(20, description="限制数量"),
    service: TaskService = Depends(provider.get_task_service),
    profile = Depends(auth.authenticate_api_token)
):
    """
    获取任务列表

    Args:
        status: 过滤的任务状态
        keywords: 过滤的关键词
        skip: 跳过数量
        limit: 限制数量
        service: 任务服务
        profile: 当前用户

    Returns:
        任务列表和总数
    """
    # 转换状态字符串为枚举值
    task_status = None
    if status:
        try:
            task_status = TaskEntity.TaskStatus(status)
        except ValueError:
            raise ValueError(f"无效的任务状态: {status}")

    # 获取任务列表
    tasks = await service.list_tasks(
        status=task_status,
        keywords=keywords,
        skip=skip,
        limit=limit
    )

    # 获取任务总数
    total = await service.count_tasks(
        status=task_status,
        keywords=keywords
    )

    return success_response(
        data=[x.to_dict() for x in tasks],
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/{task_id}", response_model=ResponseBase)
@api_response
async def get_task_info(
    task_id: str = Path(..., description="任务ID"),
    include_logs: bool = Query(False, description="是否包含日志"),
    service: TaskService = Depends(provider.get_task_service),
    profile = Depends(auth.authenticate_api_token)
):
    """
    获取任务详情

    Args:
        task_id: 任务ID
        include_logs: 是否包含日志
        service: 任务服务
        profile: 当前用户

    Returns:
        任务详情
    """
    # 获取任务信息
    task_info = await service.get_task_info(task_id, include_logs)

    # 返回结果
    return success_response(**task_info.to_dict())


@router.get("/{task_id}/logs", response_model=ResponseBase)
@api_response
async def get_task_logs(
    task_id: str = Path(..., description="任务ID"),
    skip: int = Query(0, description="跳过数量"),
    limit: int = Query(100, description="限制数量"),
    service: TaskService = Depends(provider.get_task_service),
    profile = Depends(auth.authenticate_api_token)
):
    """
    获取任务日志

    Args:
        task_id: 任务ID
        skip: 跳过数量
        limit: 限制数量
        service: 任务服务
        profile: 当前用户

    Returns:
        任务日志列表和总数
    """
    # 获取任务日志
    logs = await service.get_task_logs(task_id, skip, limit)

    # 返回结果
    return success_response(data=[x.to_dict() for x in logs])


@router.get("/{task_id}/status", response_model=ResponseBase)
@api_response
async def get_task_status(
    task_id: str = Path(..., description="任务ID"),
    service: TaskService = Depends(provider.get_task_service),
    profile = Depends(auth.authenticate_api_token)
):
    """
    获取任务状态

    Args:
        task_id: 任务ID
        service: 任务服务
        profile: 当前用户

    Returns:
        任务状态
    """
    # 获取任务信息
    task_info = await service.get_task_info(task_id)

    # 如果任务不存在，返回空结果
    if not task_info:
        return success_response({"status": None})

    # 返回状态
    return success_response(status=task_info.status.value)
