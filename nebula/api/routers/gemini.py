"""
Gemini API路由模块 - 提供Google Gemini大模型生成内容的API接口
"""

from typing import List, Optional, Union, Dict, Any, AsyncIterable
from fastapi import APIRouter, Depends, Path, Query, Body, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
import json
import asyncio
from bson import ObjectId

from nebula.api.utils.depend import get_response_mode, get_callback_url
from nebula.api.utils import auth
from nebula.core.service import provider
from nebula.core.protocol import ResponseMode
from nebula.api.utils.response import (
    delay_response, success_response, ResponseBase,
    api_response, asynctask_response, dispatch_message_with_response
)
from nebula.core.infrastructure import get_logger
from nebula.core.infrastructure.gemini import GeminiService

# 创建路由实例
router = APIRouter()
logger = get_logger(__name__)


@router.post("/generate/text", response_model=ResponseBase)
@api_response
async def generate_text(
    text: str = Body(..., description="输入文本"),
    prompt: str = Body(..., description="提示词"),
    history: Optional[List[Dict[str, Any]]] = Body(None, description="历史消息，格式为 [{'role': 'user', 'content': '用户消息'}, {'role': 'model', 'content': 'AI回复'}]"),
    use_structured_response: bool = Body(False, description="是否使用结构化响应格式"),
    response_format: Optional[Dict[str, Any]] = Body(None, description="响应格式示例，例如 {\"title\": \"标题\", \"content\": \"内容\"}，仅当 use_structured_response=True 时有效"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user = Depends(auth.authenticate_api_token)
):
    """
    根据输入文本和提示词生成内容

    - **text**: 必填，输入文本
    - **prompt**: 必填，提示词
    - **history**: 可选，历史消息，格式为 [{'role': 'user', 'content': '用户消息'}, {'role': 'model', 'content': 'AI回复'}]
    - **use_structured_response**: 可选，是否使用结构化响应格式，默认为 False
    - **response_format**: 可选，响应格式示例，例如 {"title": "标题", "content": "内容"}，仅当 use_structured_response=True 时有效
    """
    gemini_service = provider.get_gemini_service()
    content = await gemini_service.generate_content_for_text(
        text=text,
        prompt=prompt,
        history=history,
        use_structured_response=use_structured_response,
        response_format=response_format
    )
    return success_response(content=content, response_mode=response_mode, callback=callback)

@router.post("/generate/text/stream")
async def stream_generate_text(
    text: str = Body(..., description="输入文本"),
    prompt: str = Body(..., description="提示词"),
    history: Optional[List[Dict[str, Any]]] = Body(None, description="历史消息，格式为 [{'role': 'user', 'content': '用户消息'}, {'role': 'model', 'content': 'AI回复'}]"),
    use_structured_response: bool = Body(False, description="是否使用结构化响应格式"),
    response_format: Optional[Dict[str, Any]] = Body(None, description="响应格式示例，例如 {\"title\": \"标题\", \"content\": \"内容\"}，仅当 use_structured_response=True 时有效"),
    current_user = Depends(auth.authenticate_api_token)
):
    """
    流式生成文本内容

    返回一个流式响应，客户端可以实时接收生成的内容。
    每个响应块都是一个JSON对象，包含生成的文本内容。

    - **text**: 必填，输入文本
    - **prompt**: 必填，提示词
    - **history**: 可选，历史消息，格式为 [{'role': 'user', 'content': '用户消息'}, {'role': 'model', 'content': 'AI回复'}]
    - **use_structured_response**: 可选，是否使用结构化响应格式，默认为 False
    - **response_format**: 可选，响应格式示例，例如 {"title": "标题", "content": "内容"}，仅当 use_structured_response=True 时有效
    """
    gemini_service = provider.get_gemini_service()

    # 使用真正的流式响应
    async def generate():
        try:
            # 使用流式生成方法
            async for chunk in gemini_service.stream_generate_content_for_text(
                text=text,
                prompt=prompt,
                history=history,
                use_structured_response=use_structured_response,
                response_format=response_format
            ):
                if chunk:
                    # 将每个块包装为JSON并添加换行符
                    yield json.dumps({"content": chunk}) + "\n"

        except Exception as e:
            logger.error(f"流式生成内容失败: {str(e)}")
            yield json.dumps({"errcode": 500, "msg": str(e)}) + "\n"

    return StreamingResponse(
        generate(),
        media_type="application/json"
    )

@router.post("/generate/file", response_model=ResponseBase)
@api_response
async def generate_from_file(
    file_url: str = Body(..., description="文件URL"),
    prompt: str = Body(..., description="提示词"),
    use_structured_response: bool = Body(False, description="是否使用结构化响应格式"),
    response_format: Optional[Dict[str, Any]] = Body(None, description="响应格式示例，例如 {\"title\": \"标题\", \"content\": \"内容\"}，仅当 use_structured_response=True 时有效"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user = Depends(auth.authenticate_api_token)
):
    """
    根据文件和提示词生成内容

    - **file_url**: 必填，文件URL
    - **prompt**: 必填，提示词
    - **use_structured_response**: 可选，是否使用结构化响应格式，默认为 False
    - **response_format**: 可选，响应格式示例，例如 {"title": "标题", "content": "内容"}，仅当 use_structured_response=True 时有效
    """
    gemini_service = provider.get_gemini_service()
    content = await gemini_service.generate_content_for_file(
        file_uri=file_url,
        prompt=prompt,
        use_structured_response=use_structured_response,
        response_format=response_format
    )
    return success_response(content=content, response_mode=response_mode, callback=callback)

@router.post("/generate/file/stream")
async def stream_generate_from_file(
    file_uri: str = Body(..., description="文件URI"),
    prompt: str = Body(..., description="提示词"),
    use_structured_response: bool = Body(False, description="是否使用结构化响应格式"),
    response_format: Optional[Dict[str, Any]] = Body(None, description="响应格式示例，例如 {\"title\": \"标题\", \"content\": \"内容\"}，仅当 use_structured_response=True 时有效"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user = Depends(auth.authenticate_api_token)
):
    """
    流式生成文件内容

    返回一个流式响应，客户端可以实时接收生成的内容。
    每个响应块都是一个JSON对象，包含生成的文本内容。

    - **file_url**: 必填，文件URL
    - **prompt**: 必填，提示词
    - **use_structured_response**: 可选，是否使用结构化响应格式，默认为 False
    - **response_format**: 可选，响应格式示例，例如 {"title": "标题", "content": "内容"}，仅当 use_structured_response=True 时有效
    """
    gemini_service = provider.get_gemini_service()

    # 使用模拟流式响应
    async def generate():
        try:
            async for chunk in gemini_service.stream_generate_content_for_file(
                file_uri=file_uri,
                prompt=prompt,
                use_structured_response=use_structured_response,
                response_format=response_format
            ):
                if chunk:
                    # 将每个块包装为JSON并添加换行符
                    yield json.dumps({"content": chunk}) + "\n"

        except Exception as e:
            logger.error(f"流式生成内容失败: {str(e)}")
            yield json.dumps({"errcode": 500, "msg": str(e)}) + "\n"

    return StreamingResponse(
        generate(),
        media_type="application/json"
    )

    # 注意：浏览器收到的流式响应是二进制的，因为我们使用了 .encode('utf-8') 将 JSON 字符串转换为 UTF-8 编码的字节。

# 注意：浏览器收到的流式响应是二进制的，因为我们使用了 .encode('utf-8') 将 JSON 字符串转换为 UTF-8 编码的字节。
# 这是因为 HTTP 协议传输的是字节流，而不是直接的字符串。将响应编码为字节确保了数据的正确传输和解析。
# 客户端（如浏览器）在接收到这些字节后，会根据指定的 media_type 进行解码和处理。
