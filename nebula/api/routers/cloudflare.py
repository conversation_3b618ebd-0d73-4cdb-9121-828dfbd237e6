from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Query, Header, Path, Body
from nebula.api.utils import auth
from nebula.core.protocol import ResponseMode
from nebula.core.repository.cloudflare import Entity
from nebula.api.utils.response import ResponseBase, api_response, success_response, asynctask_response, dispatch_task
from pydantic import BaseModel,ValidationError

from nebula.core.service.cloudflare import CloudflareService
from nebula.core.service import provider
import nebula.core.infrastructure.exceptions as exceptions
from nebula.api.utils.depend import get_response_mode, get_callback_url
router = APIRouter()



@router.post("/config", response_model=ResponseBase)
@api_response
async def save_cloudflare_config(
    config: Entity.Account,
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user=Depends(auth.authenticate_api_token)
):
    """保存Cloudflare API配置"""
    from nebula.core.tasks.definitions import cloudflare as cloudflare_tasks

    return await dispatch_task(
        task_identifier=cloudflare_tasks.save_config,
        metadata=config.to_dict(),
        response_mode=response_mode,
        callback=callback
    )

@router.post("/sync", response_model=ResponseBase)
@api_response
async def sync_domains(
    config: Entity.Account,
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user=Depends(auth.authenticate_api_token)
):
    """同步域名数据"""
    from nebula.core.tasks.definitions import cloudflare as cloudflare_tasks

    return await dispatch_task(
        task_identifier=cloudflare_tasks.sync_domains,
        metadata=config.to_dict(),
        response_mode=response_mode,
        callback=callback
    )

@router.get("/config", response_model=ResponseBase)
@api_response
async def query_cloudflare_config(
    current_user=Depends(auth.authenticate_api_token)
):
    """查询Cloudflare配置状态"""
    cf_service: CloudflareService = provider.get_cloudflare_service()
    account = await cf_service.get_account()
    if not account:
        return success_response(configured=False)

    return success_response(
        configured=True,
        valid=True,
        account_id=account.account_id,
        api_key=account.api_key,
        domain_list=account.domain_list
    )

@router.post("/domain/{domain_id}/dns", response_model=ResponseBase)
@api_response
async def create_dns_record(
    domain_id: str = Path(...,description="域名ID"),
    record: Entity.DnsRecord = Body(...,description="DNS记录"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user=Depends(auth.authenticate_api_token)
):
    """为指定域名创建DNS记录"""
    from nebula.core.tasks.definitions import cloudflare as cloudflare_tasks
    cf_service: CloudflareService = provider.get_cloudflare_service()

    account = await cf_service.get_account()
    if not account:
        raise ValidationError("Cloudflare账户未配置")

    return await dispatch_task(
        task_identifier=cloudflare_tasks.create_dns_record,
        metadata={
            "account_id": account.account_id,
            "domain_id": domain_id,
            "record": record.to_dict()
        },
        response_mode=response_mode,
        callback=callback
    )

@router.put("/domain/{domain_id}/dns_record/{record_id}", response_model=ResponseBase)
@api_response
async def update_dns_record(
    domain_id: str = Path(...,description="域名ID"),
    record_id: str = Path(...,description="记录ID"),
    record: Entity.DnsRecord = Body(...,description="DNS记录"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    profile=Depends(auth.authenticate_api_token)
):
    """更新DNS记录"""
    from nebula.core.tasks.definitions import cloudflare as cloudflare_tasks
    cf_service: CloudflareService = provider.get_cloudflare_service()

    account = await cf_service.get_account()
    if not account:
        raise ValidationError("Cloudflare账户未配置")

    return await dispatch_task(
        task_identifier=cloudflare_tasks.update_dns_record,
        metadata={
            "account_id": account.account_id,
            "domain_id": domain_id,
            "record_id": record_id,
            "record": record.to_dict()
        },
        response_mode=response_mode,
        callback=callback
    )

@router.delete("/domain/{domain_id}/dns_record/{record_id}", response_model=ResponseBase)
@api_response
async def delete_dns_record(
    domain_id: str = Path(...,description="域名ID"),
    record_id: str = Path(...,description="记录ID"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user=Depends(auth.authenticate_api_token)
):
    """删除DNS记录"""
    from nebula.core.tasks.definitions import cloudflare as cloudflare_tasks
    cf_service: CloudflareService = provider.get_cloudflare_service()

    account = await cf_service.get_account()
    if not account:
        raise ValidationError("Cloudflare账户未配置")

    return await dispatch_task(
        task_identifier=cloudflare_tasks.delete_dns_record,
        metadata={
            "account_id": account.account_id,
            "domain_id": domain_id,
            "record_id": record_id
        },
        response_mode=response_mode,
        callback=callback
    )

@router.post("/validate-api-key", response_model=ResponseBase)
@api_response
async def validate_api_key(
    config: Entity.Account = Body(...,description="Cloudflare账号配置"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user=Depends(auth.authenticate_api_token)
):
    """
    验证Cloudflare API密钥有效性

    Args:
        config: 包含Cloudflare账号ID和API密钥的模型
        current_user: the current user

    Returns:
        Dict: 验证结果
    """
    from nebula.core.tasks.definitions import cloudflare as cloudflare_tasks

    return await dispatch_task(
        task_identifier=cloudflare_tasks.validate_api_key,
        metadata={"account_id": config.account_id, "api_key": config.api_key},
        response_mode=response_mode,
        callback=callback
    )

