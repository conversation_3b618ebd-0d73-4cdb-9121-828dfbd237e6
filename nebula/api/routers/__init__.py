#!/usr/bin/env python
# -*- coding: utf-8 -*-

import fastapi
import nebula.api.routers.auth as auth_router
import nebula.api.routers.cloudflare as cloudflare_router
import nebula.api.routers.storage as storage_router
import nebula.api.routers.memo as memo_router
import nebula.api.routers.video as video_router
import nebula.api.routers.gemini as gemini_router
import nebula.api.routers.notification as notification_router
import nebula.api.routers.telegram as telegram_router
import nebula.api.routers.tide as tide_router
import nebula.api.routers.task as task_router


# 创建主路由
api_router = fastapi.APIRouter()

# # 注册子路由
api_router.include_router(auth_router.router, prefix="", tags=["认证管理"])
api_router.include_router(cloudflare_router.router, prefix="/cloudflare", tags=["Cloudflare管理"])
api_router.include_router(storage_router.router, prefix="/storage", tags=["存储管理"])
api_router.include_router(memo_router.router, prefix="/memo", tags=["备忘录管理"])
api_router.include_router(video_router.router, prefix="/video", tags=["视频管理"])
api_router.include_router(gemini_router.router, prefix="/gemini", tags=["Gemini生成内容"])
api_router.include_router(notification_router.router, prefix="/notifications", tags=["通知管理"])
api_router.include_router(telegram_router.router, prefix="/telegram", tags=["Telegram机器人"])
api_router.include_router(tide_router.router, prefix="/tide", tags=["潮汐数据"])
api_router.include_router(task_router.router, prefix="/tasks", tags=["任务管理"])