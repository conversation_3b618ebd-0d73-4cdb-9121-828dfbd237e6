from typing import List, Optional, Union, Dict, Any
from fastapi import APIRouter, Depends, Path, Query, Body
from bson import ObjectId
from nebula.api.utils.depend import get_response_mode, get_callback_url
from nebula.core.repository.video import Entity
from nebula.core.service.video import VideoService
from nebula.api.utils import auth
from nebula.core.service import provider
from nebula.core.protocol import ResponseMode
from nebula.api.utils.response import (
    delay_response, success_response, ResponseBase,
    api_response, asynctask_response, dispatch_task
)
from nebula.core.infrastructure import log_context

# 创建路由实例
router = APIRouter()

# 创建视频
@router.post("", response_model=ResponseBase)
@api_response
async def create_video(
    video: Entity.Video = Body(..., description="视频数据"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user = Depends(auth.authenticate_api_token)
):
    """创建新视频"""
    from nebula.core.tasks.definitions import video as video_tasks

    return await dispatch_task(
        task_identifier=video_tasks.create_video,
        metadata=video.to_dict(),
        response_mode=response_mode,
        callback=callback
    )

# 获取视频列表
@router.get("", response_model=ResponseBase)
@api_response
@log_context
async def list_videos(
    keyword: Optional[str] = Query(None, description="搜索关键字"),
    tags: Optional[List[str]] = Query(None, description="标签列表"),
    category: Optional[str] = Query(None, description="视频类别"),
    skip: Optional[int] = Query(0, description="跳过数量"),
    limit: Optional[int] = Query(20, description="限制数量"),
    sort_field: Optional[str] = Query("updated_at", description="排序字段"),
    sort_order: Optional[int] = Query(-1, description="排序方式，1为升序，-1为降序"),
    service: VideoService = Depends(provider.get_video_service),
    current_user = Depends(auth.authenticate_api_token)
):
    """获取视频列表

    支持多种查询方式：
    1. 不提供任何参数：获取所有视频
    2. 提供keyword参数：按关键词搜索视频（标题、描述、概述、字幕）
    3. 提供tags参数：按标签过滤视频
    4. 提供category参数：按类别过滤视频

    这些参数可以组合使用，实现更精确的查询。
    """
    videos = await service.list_videos(keyword, tags, category, skip, limit, sort_field, sort_order)
    total = await service.count_videos(keyword, tags, category)

    return success_response(
        data=[x.to_dict() for x in videos],
        total=total,
        skip=skip,
        limit=limit
    )

# 获取单个视频
@router.get("/{id}", response_model=ResponseBase)
@api_response
async def get_video(
    id: str = Path(..., description="视频ID"),
    service: VideoService = Depends(provider.get_video_service),
    current_user = Depends(auth.authenticate_api_token)
):
    """获取单个视频详情"""
    video = await service.get_video(id)
    return success_response(**video.to_dict())

# 更新视频
@router.put("/{id}", response_model=ResponseBase)
@api_response
async def update_video(
    id: str = Path(..., description="视频ID"),
    update_data: Dict[str, Any] = Body(..., description="更新数据"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user = Depends(auth.authenticate_api_token)
):
    """更新视频信息"""
    from nebula.core.tasks.definitions import video as video_tasks

    # 添加ID到配置中
    update_data["id"] = id

    return await dispatch_task(
        task_identifier=video_tasks.update_video,
        metadata=update_data,
        response_mode=response_mode,
        callback=callback
    )

# 删除视频
@router.delete("/{id}", response_model=ResponseBase)
@api_response
async def delete_video(
    id: str = Path(..., description="视频ID"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user = Depends(auth.authenticate_api_token)
):
    """删除视频"""
    from nebula.core.tasks.definitions import video as video_tasks

    return await dispatch_task(
        task_identifier=video_tasks.delete_video,
        metadata={"id": id},
        response_mode=response_mode,
        callback=callback
    )

# 添加标签
@router.post("/{id}/tags", response_model=ResponseBase)
@api_response
async def add_tags(
    id: str = Path(..., description="视频ID"),
    tags: List[str] = Body(..., description="标签列表"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user = Depends(auth.authenticate_api_token)
):
    """为视频添加标签"""
    from nebula.core.tasks.definitions import video as video_tasks

    config = {"id": id, "tags": tags}

    return await dispatch_task(
        task_identifier=video_tasks.add_tags,
        metadata=config,
        response_mode=response_mode,
        callback=callback
    )

# 移除标签
@router.delete("/{id}/tags", response_model=ResponseBase)
@api_response
async def remove_tags(
    id: str = Path(..., description="视频ID"),
    tags: List[str] = Body(..., description="要移除的标签列表"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user = Depends(auth.authenticate_api_token)
):
    """移除视频标签"""
    from nebula.core.tasks.definitions import video as video_tasks

    config = {"id": id, "tags": tags}

    return await dispatch_task(
        task_identifier=video_tasks.remove_tags,
        metadata=config,
        response_mode=response_mode,
        callback=callback
    )

# 更新字幕
@router.put("/{id}/subtitles", response_model=ResponseBase)
@api_response
async def update_subtitles(
    id: str = Path(..., description="视频ID"),
    subtitles: str = Body(..., description="字幕文本"),
    chunks: Optional[List[str]] = Body(None, description="字幕片段列表"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user = Depends(auth.authenticate_api_token)
):
    """更新视频字幕"""
    from nebula.core.tasks.definitions import video as video_tasks

    config = {"id": id, "subtitles": subtitles}
    if chunks:
        config["chunks"] = chunks

    return await dispatch_task(
        task_identifier=video_tasks.update_subtitles,
        metadata=config,
        response_mode=response_mode,
        callback=callback
    )

# 分析视频内容
@router.post("/{id}/analyze", response_model=ResponseBase)
@api_response
async def analyze_content(
    id: str = Path(..., description="视频ID"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user = Depends(auth.authenticate_api_token)
):
    """分析视频内容"""
    from nebula.core.tasks.definitions import video as video_tasks

    return await dispatch_task(
        task_identifier=video_tasks.analyze_content,
        metadata={"video_id": id},
        response_mode=response_mode,
        callback=callback
    )



# 从分享链接提取视频信息
@router.post("/extract", response_model=ResponseBase)
@api_response
async def extract_video_info(
    share_text: str = Body(..., embed=True, description="包含视频链接的分享文本"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user = Depends(auth.authenticate_api_token)
):
    """从分享文本中提取视频信息"""
    from nebula.core.tasks.definitions import video as video_tasks

    # 创建配置
    config = {"share_text": share_text}

    return await dispatch_task(
        task_identifier=video_tasks.extract_share_info,
        metadata=config,
        response_mode=response_mode,
        callback=callback
    )

# 从分享链接导入视频
@router.post("/import-from-share", response_model=ResponseBase)
@api_response
async def import_video_from_share(
    video_id: str = Body("", description="视频ID"),
    title: str = Body(..., description="视频标题"),
    desc: str = Body("", description="视频描述"),
    duration: int = Body(0, description="视频时长"),
    cover_url: str = Body(..., description="视频封面URL"),
    player_url: str = Body(..., description="视频播放URL"),
    watch_url: str = Body("", description="视频观看URL"),
    from_platform: str = Body("", description="视频来源平台"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    service: VideoService = Depends(provider.get_video_service),
    current_user = Depends(auth.authenticate_api_token)
):
    """从分享链接导入视频

    下载视频封面和视频文件，然后创建视频记录
    """
    from nebula.core.tasks.definitions import video as video_tasks

    # 创建配置
    config = {
        "title": title,
        "desc": desc,
        "cover_url": cover_url,
        "player_url": player_url,
        "video_id": video_id,
        "watch_url": watch_url,
        "duration": duration,
        "from_platform": from_platform
    }

    return await dispatch_task(
        task_identifier=video_tasks.import_from_share,
        metadata=config,
        response_mode=response_mode,
        callback=callback
    )

# 从URL创建视频
@router.post("/create-from-url", response_model=ResponseBase)
@api_response
async def create_from_url(
    video_player_url: str = Body(..., description="视频播放URL"),
    title: str = Body("", description="视频标题"),
    desc: str = Body("", description="视频描述"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user = Depends(auth.authenticate_api_token)
):
    """从URL创建视频

    使用提供的视频URL创建视频记录，并使用AI分析视频内容
    """
    from nebula.core.tasks.definitions import video as video_tasks

    # 创建配置
    config = {
        "title": title,
        "desc": desc,
        "play_url": video_player_url,
        "source_url": video_player_url,
        "platform": ""  # 空平台表示用户上传的视频
    }

    return await dispatch_task(
        task_identifier=video_tasks.create_video,
        metadata=config,
        response_mode=response_mode,
        callback=callback
    )


# 从分享链接创建视频（链式任务）
@router.post("/create-from-share-chain", response_model=ResponseBase)
@api_response
async def create_video_from_share_chain(
    share_text: str = Body(..., embed=True, description="包含视频链接的分享文本"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
    current_user = Depends(auth.authenticate_api_token)
):
    """从分享链接创建视频（完整流程）

    执行完整的视频创建流程：
    1. 提取分享信息
    2. 导入视频
    3. 分析内容

    所有步骤共享重试策略，任何步骤失败都会重试整个流程。
    """
    from nebula.core.tasks.definitions import video as video_tasks

    # 创建配置
    config = {"share_text": share_text}

    return await dispatch_task(
        task_identifier=video_tasks.create_video_from,
        metadata=config,
        response_mode=response_mode,
        callback=callback
    )