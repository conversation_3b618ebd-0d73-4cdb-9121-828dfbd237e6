#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通知API路由
"""

from typing import Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body
from nebula.core.service.notification import NotificationService
from nebula.api.utils import auth
from nebula.core.service import provider
from nebula.api.utils.response import (
    success_response, ResponseBase,
    api_response
)
from nebula.core.infrastructure import log_context

# 创建路由实例
router = APIRouter()

@router.get("", response_model=ResponseBase)
@api_response
@log_context
async def list_notifications(
    include_read: Optional[bool] = Query(True, description="是否包含已读通知"),
    skip: Optional[int] = Query(0, description="跳过数量"),
    limit: Optional[int] = Query(50, description="限制数量"),
    service: NotificationService = Depends(provider.get_notification_service),
    profile = Depends(auth.authenticate_api_token)
):
    """获取当前用户的通知列表"""
    # 获取通知列表
    notifications = await service.get_user_notifications(
        profile.username, skip, limit, include_read
    )

    # 统计未读数量
    unread_count = await service.count_unread(profile.username)

    return success_response(
        data=notifications,
        total=len(notifications),
        unread=unread_count,
        skip=skip,
        limit=limit
    )

@router.get("/{id}", response_model=ResponseBase)
@api_response
async def get_notification(
    id: str = Path(..., description="通知ID"),
    service: NotificationService = Depends(provider.get_notification_service),
    profile = Depends(auth.authenticate_api_token)
):
    """获取单个通知详情"""
    # 验证用户权限（当前简单实现，未检查通知是否属于当前用户）
    notification = await service.get_notification(id)

    # 转换为响应格式
    notification_dict = {
        "id": str(notification.id),
        "message": notification.message,
        "from_source": notification.from_source,
        "event": notification.event,
        "type": notification.type,
        "is_read": notification.is_read,
        "created_at": notification.created_at.isoformat(),
        "updated_at": notification.updated_at.isoformat() if notification.updated_at else None,
        "metadata": notification.metadata,
        "task_id": notification.task_id
    }

    return success_response(data=notification_dict)

@router.post("/{id}/read", response_model=ResponseBase)
@api_response
async def mark_as_read(
    id: str = Path(..., description="通知ID"),
    service: NotificationService = Depends(provider.get_notification_service),
    profile = Depends(auth.authenticate_api_token)
):
    """标记通知为已读"""
    # 验证用户权限（当前简单实现，未检查通知是否属于当前用户）
    await service.mark_as_read(id)
    return success_response(msg="通知已标记为已读")

@router.post("/read-all", response_model=ResponseBase)
@api_response
async def mark_all_as_read(
    service: NotificationService = Depends(provider.get_notification_service),
    profile = Depends(auth.authenticate_api_token)
):
    """标记所有通知为已读"""
    count = await service.mark_all_as_read(profile.username)
    return success_response(msg="所有通知已标记为已读", count=count)

@router.delete("/{id}", response_model=ResponseBase)
@api_response
async def delete_notification(
    id: str = Path(..., description="通知ID"),
    service: NotificationService = Depends(provider.get_notification_service),
    profile = Depends(auth.authenticate_api_token)
):
    """删除通知"""
    # 验证用户权限（当前简单实现，未检查通知是否属于当前用户）
    await service.delete_notification(id)
    return success_response(msg="通知已删除")

@router.delete("", response_model=ResponseBase)
@api_response
async def clear_all_notifications(
    service: NotificationService = Depends(provider.get_notification_service),
    profile = Depends(auth.authenticate_api_token)
):
    """清空所有通知"""
    count = await service.clear_all_notifications(profile.username)
    return success_response(msg="所有通知已清空", count=count)

# @router.post("", response_model=ResponseBase)
# @api_response
# async def create_notification(
#     message: str = Body(..., description="通知内容"),
#     event: str = Body(..., description="事件类型"),
#     from_source: str = Body("系统", description="通知来源"),
#     type: str = Body("info", description="通知类型"),
#     metadata: Optional[Dict[str, Any]] = Body(None, description="附加元数据"),
#     service: NotificationService = Depends(provider.get_notification_service),
#     current_user = Depends(auth.authenticate_api_token)
# ):
#     """创建通知"""
#     notification = await service.create_notification(
#         user_id=current_user["username"],
#         message=message,
#         event=event,
#         from_source=from_source,
#         type=type,
#         metadata=metadata
#     )

#     return success_response(
#         msg="通知已创建",
#         id=str(notification.id)
#     )
