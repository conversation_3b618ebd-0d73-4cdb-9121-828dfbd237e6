#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import uuid
import base64
import tempfile
from typing import List, Optional
from datetime import datetime, timedelta

import fastapi
from fastapi import File, UploadFile, Depends, HTTPException, Form, Path, Query, Body
from nebula.api.utils.response import api_response, ResponseBase, success_response
from nebula.core.infrastructure.exceptions import NebulaException
from starlette.status import HTTP_400_BAD_REQUEST, HTTP_404_NOT_FOUND, HTTP_500_INTERNAL_SERVER_ERROR
from nebula.api.utils import auth
from nebula.core.service import provider

import nebula.core.context as context
from nebula.core.infrastructure.storage import StorageService

router = fastapi.APIRouter()



@router.post("/upload/{bucket_name}", response_model=ResponseBase)
@api_response
async def upload_file(
    file: UploadFile = File(...),
    path: Optional[str] = Form(None),
    bucket_name: str = Path(..., description="存储桶名称，不指定则使用默认桶"),
    current_user=Depends(auth.authenticate_api_token)
):
    """
    上传文件到存储服务

    - **file**: 要上传的文件
    - **path**: 存储路径前缀（可选）
    - **bucket_name**: 存储桶名称，不指定则使用默认桶
    """

    storage_service = provider.get_storage_service()

    # 生成文件名（使用UUID避免重名）
    filename = file.filename
    file_ext = os.path.splitext(filename)[1] if filename else ""
    object_name = f"{uuid.uuid4().hex}{file_ext}"

    # 如果指定了路径，添加到对象名前
    if path:
        # 确保路径格式正确（以/结尾但不以/开头）
        if not path.endswith("/"):
            path = path + "/"
        if path.startswith("/"):
            path = path[1:]
        object_name = f"{path}{object_name}"

    # 直接读取文件内容为字节
    content = await file.read()

    # 使用upload_bytes方法直接上传字节数据
    url = await storage_service.upload_bytes(
        data=content,
        bucket_name=bucket_name,  # 使用指定的桶或默认桶
        object_name=object_name
    )

    # 返回文件URL和其他信息
    return success_response(
        url=url,
        filename=filename,
        object_name=object_name,
        size=len(content),
        bucket=bucket_name or storage_service._bucket
    )


@router.get("/presigned-url/{bucket_name}/{object_name:path}", response_model=ResponseBase)
@api_response
async def get_presigned_url(
    object_name: str = Path(..., description="对象名称"),
    expires: int = Query(3600, description="过期时间（秒）"),
    bucket_name: str = Path(..., description="存储桶名称"),
    storage_service: StorageService = Depends(provider.get_storage_service)
):
    """
    获取对象的预签名URL

    - **object_name**: 对象名称
    - **expires**: 过期时间（秒），默认3600秒(1小时)
    - **bucket_name**: 存储桶名称，不指定则使用默认桶
    """
    try:
        url = await storage_service.get_presigned_url(
            object_name=object_name,
            expires=expires,
            bucket_name=bucket_name
        )

        return {
            "success": True,
            "url": url,
            "object_name": object_name,
            "expires_in": expires
        }
    except Exception as e:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取预签名URL失败: {str(e)}"
        )

@router.delete("/{bucket_name}/{object_name:path}", response_model=ResponseBase)
@api_response
async def delete_file(
    object_name: str = Path(..., description="要删除的对象名称"),
    bucket_name: str = Path(..., description="存储桶名称"),
    storage_service: StorageService = Depends(provider.get_storage_service)
):
    """
    从存储服务删除文件

    - **object_name**: 要删除的对象名称
    - **bucket_name**: 存储桶名称，不指定则使用默认桶
    """
    try:
        success = await storage_service.delete_object(object_name, bucket_name=bucket_name)
        if not success:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail=f"文件不存在或删除失败: {object_name}"
            )

        return {
            "success": True,
            "message": f"文件删除成功: {object_name}"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件删除失败: {str(e)}"
        )

@router.get("/{bucket_name}", response_model=ResponseBase)
@api_response
async def get_file_list(
    search: Optional[str] = Query(None, description="搜索关键字"),
    bucket_name: str = Path(..., description="存储桶名称"),
    temp_only: bool = Query(False, description="是否只返回临时文件"),
    continuation_token: Optional[str] = Query(None, description="分页令牌"),
    max_keys: int = Query(100, description="每页最大文件数"),
    current_user=Depends(auth.authenticate_api_token)
):
    """
    获取文件列表

    - **search**: 可选的搜索关键字，用于筛选文件
    - **bucket_name**: 存储桶名称
    - **temp_only**: 是否只返回临时文件（位于temp/目录下的文件）
    - **continuation_token**: 分页令牌，从上一次调用返回的next_token获取
    - **max_keys**: 每页最大文件数，默认100
    """
    storage_service: StorageService = provider.get_storage_service()
    # 准备参数
    params = {
        "bucket_name": bucket_name,
        "max_keys": max_keys,
        "continuation_token": continuation_token
    }

    # 根据temp_only参数决定是否只显示临时文件或排除临时文件
    if temp_only:
        params["prefix"] = "temp/"
    else:
        params["exclude_prefix"] = "temp/"

    # 使用新的分页API获取对象列表
    result = await storage_service.list_objects_with_exclusion(**params)
    objects = result["objects"]
    next_token = result["next_token"]
    is_truncated = result["is_truncated"]

    # 构建文件列表
    files = []
    for obj in objects:
        obj_key = obj.get("Key", "")

        # 如果有搜索关键字，进行过滤
        if search and search.lower() not in obj_key.lower():
            continue

        # 构建文件对象
        file_obj = {
            "object_name": obj_key,
            "size": obj.get("Size", 0),
            "upload_time": obj.get("LastModified", ""),
            "url": storage_service._build_url(obj_key, bucket_name)
        }

        # 从对象名中提取文件名
        file_obj["filename"] = os.path.basename(file_obj["object_name"])

        # 判断是否为临时文件
        file_obj["is_temp"] = obj_key.startswith("temp/")

        # 尝试获取文件元数据
        try:
            metadata = await storage_service.get_object_metadata(obj_key, bucket_name=bucket_name)
            # 添加过期时间信息（如果有）
            if "expires_at" in metadata:
                file_obj["expires_at"] = metadata["expires_at"]
        except:
            # 获取元数据失败不影响整体列表
            pass

        files.append(file_obj)

    # 返回结果，包含分页信息
    return success_response(
        files=files,
        bucket=bucket_name or storage_service._bucket,
        is_temp_filter=temp_only,
        next_token=next_token,
        has_more=is_truncated
    )

@router.post("/upload-temp/{bucket_name}", response_model=ResponseBase)
@api_response
async def upload_temp_file(
    file: UploadFile = File(...),
    expires_in: Optional[int] = Form(86400, description="过期时间(秒)，默认24小时"),
    bucket_name: str = Path(..., description="存储桶名称"),
    current_user=Depends(auth.authenticate_api_token)
):
    """
    上传文件到指定桶的临时存储区域，带有过期时间

    - **file**: 要上传的文件
    - **expires_in**: 过期时间(秒)，默认24小时
    - **bucket_name**: 存储桶名称，不指定则使用默认桶
    """
    storage_service = provider.get_storage_service()

    # 生成文件名
    filename = file.filename
    file_ext = os.path.splitext(filename)[1] if filename else ""

    # 计算过期时间
    expiry_date = datetime.now() + timedelta(seconds=expires_in)

    # 直接读取文件内容为字节
    content = await file.read()

    # 使用upload_byte_to_temp方法直接上传字节数据，设置元数据
    url, temp_path = await storage_service.upload_byte_to_temp(
        data=content,
        ext_name=file_ext,
        expires_in=expires_in,
        bucket_name=bucket_name,  # 使用指定的桶或默认桶
    )

    # 返回文件URL和其他信息
    return success_response(
        url=url,
        filename=filename,
        object_name=temp_path,
        size=len(content),
        expires_at=expiry_date.isoformat(),
        temp=True,
        bucket=bucket_name or storage_service._bucket
    )

@router.post("/cleanup-temp-files/{bucket_name}", response_model=ResponseBase)
@api_response
async def cleanup_temp_files(
    older_than_hours: int = Body(24, description="清理超过多少小时的临时文件"),
    bucket_name: str = Path(..., description="存储桶名称"),
    storage_service: StorageService = Depends(provider.get_storage_service)
):
    """
    清理指定桶中temp目录下的过期临时文件

    - **older_than_hours**: 清理超过多少小时的临时文件，默认24小时
    - **bucket_name**: 存储桶名称，不指定则使用默认桶
    """
    try:
        # 计算截止时间
        cutoff_time = datetime.now() - timedelta(hours=older_than_hours)

        # 获取指定桶中的所有临时对象
        result = await storage_service.list_objects_with_exclusion(prefix="temp/", bucket_name=bucket_name, max_keys=1000)
        objects = result["objects"]

        # 如果有多页，继续获取
        while result["is_truncated"]:
            result = await storage_service.list_objects_with_exclusion(
                prefix="temp/",
                bucket_name=bucket_name,
                continuation_token=result["next_token"],
                max_keys=1000
            )
            objects.extend(result["objects"])

        deleted_count = 0
        for obj in objects:
            obj_key = obj.get("Key", "")

            try:
                # 获取对象元数据
                metadata = await storage_service.get_object_metadata(
                    obj_key,
                    bucket_name=bucket_name
                )

                # 检查是否为临时文件
                if metadata.get("is_temp") != "true":
                    continue

                # 检查是否过期
                if "expires_at" in metadata:
                    try:
                        expires_at = datetime.fromisoformat(metadata["expires_at"])
                        if expires_at < datetime.now():
                            # 删除过期文件
                            storage_service.delete_object(
                                obj_key,
                                bucket_name=bucket_name
                            )
                            deleted_count += 1
                    except (ValueError, TypeError) as e:
                        continue
                else:
                    # 如果没有过期时间但创建时间超过指定时间
                    if obj.get("LastModified") and obj.get("LastModified") < cutoff_time:
                        storage_service.delete_object(
                            obj_key,
                            bucket_name=bucket_name
                        )
                        deleted_count += 1
            except Exception as file_error:
                # 单个文件处理错误不影响整体操作
                continue

        return success_response(
            deleted_count=deleted_count,
            message=f"已清理 {deleted_count} 个过期临时文件",
            bucket=bucket_name or storage_service._bucket
        )
    except Exception as e:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理临时文件失败: {str(e)}"
        )