"""
Telegram API路由模块
提供Telegram Webhook接口
"""

import logging
import json
import time
from typing import Dict, Any
from fastapi import APIRouter, Depends, Body, Request
from nebula.api.utils.depend import get_response_mode, get_callback_url
from nebula.api.utils import auth
from nebula.core.service import provider
from nebula.core.protocol import ResponseMode
from nebula.api.utils.response import (
    success_response, ResponseBase, api_response, dispatch_task
)
from nebula.core.infrastructure import log_context

# 创建路由实例
router = APIRouter()

@router.post("/webhook", response_model=ResponseBase)
@api_response
async def telegram_webhook(
    update: Dict[str, Any] = Body(..., description="Telegram更新数据"),
    request: Request = None
):
    """
    处理Telegram Webhook请求

    Args:
        update: Telegram更新数据
        request: 请求对象
        response_mode: 响应模式
        callback: 回调URL

    Returns:
        处理结果
    """
    logger = logging.getLogger(__name__)

    # 记录请求信息
    logger.info(f"收到Telegram webhook请求")
    # logger.info(f"请求头: {request.headers if request else 'No request object'}")
    # logger.info(f"更新数据: {json.dumps(update, ensure_ascii=False)}")

    # 获取Telegram服务
    telegram_service = provider.get_telegram_service()

    # 解析更新数据
    event, _ = telegram_service.parse_update(update)

    # 导入任务定义
    from nebula.core.tasks.definitions import telegram as telegram_tasks

    # 发送到消息队列并处理响应
    return await dispatch_task(
        task_identifier=telegram_tasks.process_event,
        metadata={
            "event_type": event.event_type,
            "update": update
        },
        response_mode=ResponseMode.WEBSOCKET
    )

@router.get("/webhook/info", response_model=ResponseBase)
@api_response
async def get_webhook_info(

):
    """
    获取Telegram Webhook信息

    Args:
        current_user: 当前用户

    Returns:
        Webhook信息
    """
    # 获取Telegram服务
    telegram_service = provider.get_telegram_service()

    # 获取Webhook信息
    info = await telegram_service.get_webhook_info()

    # 返回信息
    return success_response(
        msg="获取Webhook信息成功",
        info=info
    )

@router.get("/webhook/setup", response_model=ResponseBase)
@api_response
async def setup_webhook(
    drop_pending_updates: bool = Body(True, description="是否清除积压的更新")
):
    """
    设置Telegram Webhook

    Args:
        drop_pending_updates: 是否清除积压的更新

    Returns:
        设置结果
    """
    # 获取Telegram服务
    telegram_service = provider.get_telegram_service()

    # 设置Webhook
    webhook_info = await telegram_service.setup_webhook(drop_pending_updates=drop_pending_updates)

    # 返回成功响应
    return success_response(msg="Webhook设置成功", info=webhook_info)

@router.post("/webhook/verify", response_model=ResponseBase)
@api_response
async def verify_webhook(
    chat_id: int = Body(..., description="聊天ID"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url)
):
    """
    验证Webhook是否正常工作

    Args:
        chat_id: 聊天ID
        response_mode: 响应模式
        callback: 回调URL

    Returns:
        验证结果
    """
    # 导入任务定义
    from nebula.core.tasks.definitions import telegram as telegram_tasks

    return await dispatch_task(
        task_identifier=telegram_tasks.send_message,
        metadata={
            "chat_id": chat_id,
            "text": "Webhook验证消息，如果您收到此消息，则表示Webhook正常工作。"
        },
        response_mode=response_mode,
        callback=callback
    )

@router.post("/webhook/test", response_model=ResponseBase)
@api_response
async def test_webhook(
    chat_id: int = Body(..., description="聊天ID"),
    request: Request = None,
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url)
):
    """
    测试Webhook

    Args:
        chat_id: 聊天ID
        request: 请求对象
        response_mode: 响应模式
        callback: 回调URL

    Returns:
        测试结果
    """
    logger = logging.getLogger(__name__)

    # 记录请求信息
    logger.info(f"收到Webhook测试请求")
    # logger.info(f"请求头: {request.headers if request else 'No request object'}")

    # 获取Telegram服务
    telegram_service = provider.get_telegram_service()

    # 获取Webhook信息
    webhook_info = await telegram_service.get_webhook_info()
    logger.info(f"Webhook信息: {webhook_info}")

    # 导入任务定义
    from nebula.core.tasks.definitions import telegram as telegram_tasks

    # 发送到消息队列并处理响应
    result = await dispatch_task(
        task_identifier=telegram_tasks.send_message,
        metadata={
            "chat_id": chat_id,
            "text": f"Webhook测试消息，当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
        },
        response_mode=response_mode,
        callback=callback
    )

    # 添加webhook信息到响应
    setattr(result, "webhook_info", webhook_info)

    return result

@router.post("/send/message", response_model=ResponseBase)
@api_response
async def send_message(
    chat_id: int = Body(..., description="聊天ID"),
    text: str = Body(..., description="消息文本"),
    response_mode: ResponseMode = Depends(get_response_mode),
    callback: str = Depends(get_callback_url),
):
    """
    发送Telegram消息

    Args:
        chat_id: 聊天ID
        text: 消息文本
        response_mode: 响应模式
        callback: 回调URL

    Returns:
        发送结果
    """
    # 导入任务定义
    from nebula.core.tasks.definitions import telegram as telegram_tasks

    return await dispatch_task(
        task_identifier=telegram_tasks.send_message,
        metadata={"chat_id": chat_id, "text": text},
        response_mode=response_mode,
        callback=callback
    )

@router.post("/send/direct", response_model=ResponseBase)
@api_response
async def send_direct_message(
    chat_id: int = Body(..., description="聊天ID"),
    text: str = Body(..., description="消息文本"),
):
    """
    直接发送Telegram消息（不通过消息队列）

    Args:
        chat_id: 聊天ID
        text: 消息文本

    Returns:
        发送结果
    """
    # 获取Telegram服务
    telegram_service = provider.get_telegram_service()

    # 直接发送消息
    await telegram_service.bot.send_message(
        chat_id=chat_id,
        text=text
    )

    # 返回成功响应
    return success_response(msg="消息发送成功")

@router.get("/test", response_model=ResponseBase)
@api_response
async def test_telegram():
    """
    测试Telegram服务

    Returns:
        测试结果
    """
    # 获取Telegram服务
    telegram_service = provider.get_telegram_service()

    # 获取机器人信息
    bot_info = await telegram_service.bot.get_me()

    # 获取Webhook信息
    webhook_info = await telegram_service.get_webhook_info()

    # 返回成功响应
    return success_response(
        msg="Telegram服务测试成功",
        bot_info=bot_info.to_dict(),
        webhook_info=webhook_info
    )
