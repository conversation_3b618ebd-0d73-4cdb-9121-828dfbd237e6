#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
潮汐API路由模块
提供潮汐数据的API接口
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

import fastapi
from fastapi import APIRouter,Depends, Query, Path, HTTPException
from starlette.responses import JSONResponse

from nebula.core.infrastructure import NebulaException, NotFoundError
from nebula.core.service import provider
from nebula.api.utils.response import ResponseBase, success_response, api_response

# 创建路由实例
router = APIRouter()

# 获取日志记录器
logger = logging.getLogger(__name__)

@router.get("", response_model=ResponseBase)
@api_response
async def get_tide_data(
    date: Optional[str] = Query(None, description="日期，格式为YYYY-MM-DD，如果不指定则获取当前日期及未来7天的数据")
):
    """
    获取潮汐数据

    Args:
        date: 日期，格式为YYYY-MM-DD，如果不指定则获取当前日期及未来7天的数据

    Returns:
        潮汐数据
    """
    # 获取杂项服务
    misc_service = provider.get_misc_service()

    # 获取潮汐数据
    data = await misc_service.get_tide_data(date)

    # 返回成功响应
    return success_response(data=data)