# Nebula API 服务

本服务是 Nebula 项目的 API 层，基于 FastAPI 框架开发，提供 RESTful API 和 WebSocket 接口，负责处理前端请求，调用核心业务逻辑，并处理异步任务。

## 功能特性

- RESTful API 接口
- WebSocket 实时通信
- 与 RabbitMQ 集成的异步任务处理
- 基于 JWT 的身份验证
- 与核心服务模块集成

## 架构设计

API 服务主要作为业务入口，本身不与数据库直接交互，只负责调用 core 模块的业务逻辑。对于高 IO 业务，API 会生成消息放入队列创建一个异步任务，直接返回任务创建是否成功的信息给客户端。

```
+----------+      +--------+      +---------+
|          |      |        |      |         |
| Frontend | <--> |  API   | <--> |  Core   |
|          |      |        |      |         |
+----------+      +--------+      +---------+
                      |
                      v
                 +---------+      +----------+
                 |         |      |          |
                 | RabbitMQ| <--> | Consumer |
                 |         |      |          |
                 +---------+      +----------+
```

## 目录结构

```
api/
├── config.py            # 配置文件
├── main.py              # 应用入口
├── Dockerfile           # Docker 构建文件
├── requirements.txt     # 依赖列表
├── routers/             # API 路由
│   ├── health.py        # 健康检查路由
│   ├── users.py         # 用户管理路由
│   └── tasks.py         # 任务管理路由
├── services/            # API 服务
├── utils/               # 工具函数
│   └── rabbitmq.py      # RabbitMQ 工具
└── websockets/          # WebSocket 服务
    └── notifications.py # 通知 WebSocket
```

## 部署方式

### Docker 部署

```bash
docker build -t nebula-api -f api/Dockerfile .
docker run -d --name nebula-api --network xbridge -p 8091:8091 nebula-api
```

### 开发环境

```bash
# 安装依赖
pip install -r api/requirements.txt

# 运行服务
python -m api.main
```

## API 文档

服务启动后，可以通过以下 URL 访问 API 文档：

- Swagger UI: http://localhost:8091/api/docs
- ReDoc: http://localhost:8091/api/redoc

## WebSocket 连接

WebSocket 连接需要提供有效的 JWT 令牌进行身份验证：

```
ws://localhost:8091/ws/notifications?token=your_jwt_token
``` 