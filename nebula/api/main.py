#!/usr/bin/env python
# -*- coding: utf-8 -*-
import uvicorn
from nebula.core.infrastructure import get_settings

if __name__ == "__main__":
    settings = get_settings()

    uvicorn.run(
        "nebula.api.factory:app",  # 仅作为路径入口，避免副作用
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.api.reload,
        workers=settings.api.workers,
        proxy_headers=True,
        forwarded_allow_ips="*"
    )