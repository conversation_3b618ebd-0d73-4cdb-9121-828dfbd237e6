#!/usr/bin/env python
# -*- coding: utf-8 -*-
import time
import subprocess
import sys
import importlib.util
import os
import logging
import uvicorn
import fastapi
import fastapi.middleware.cors
import fastapi.exceptions
import starlette.exceptions
import nebula.core.infrastructure as infrastructure
import nebula.api.utils.exceptions as api_exceptions
import nebula.core.context as context
from contextlib import asynccontextmanager
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response


# 创建日志记录器
logger = logging.getLogger("api")

# 禁用uvicorn的默认异常处理器
logging.getLogger("uvicorn.error").propagate = False


COMPONENTS = ["database","cache","auth","cloudflare","message","storage", "memo","video","gemini","browser","notification","telegram","misc"]

# 创建一个更严格的异常日志过滤器
class StrictExceptionFilter(logging.Filter):
    """清除日志记录的exc_info，防止堆栈在控制台输出，但保留错误消息"""
    def filter(self, record):
        # 如果有exc_info且不是我们的异常处理器记录的，则清除exc_info
        if record.exc_info and not record.name.startswith('api.exceptions'):
            # 保留异常信息但清除堆栈
            record.exc_info = None
            # 清除exc_text（已经格式化的异常文本）
            record.exc_text = None
        return True

# 应用过滤器到所有相关日志器
for logger_name in ["uvicorn", "uvicorn.error", "uvicorn.access", "fastapi", "api"]:
    logging.getLogger(logger_name).addFilter(StrictExceptionFilter())

# 创建自定义中间件拦截异常
class ExceptionHandlingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        try:
            return await call_next(request)
        except Exception as e:
            # 异常已经被捕获，不需要让FastAPI再次记录
            # 我们的异常处理器会在后续处理中正确记录它
            return await api_exceptions.exception_handler(request, e)

# def check_and_install(package_name, install_dir):
#     # 检查包是否已安装
#     package_spec = importlib.util.find_spec(package_name)
#     if package_spec is None:
#         print(f"Package '{package_name}' is not installed. Installing...")

#         # 使用 subprocess 安装包，指定目录
#         subprocess.check_call([sys.executable, "-m", "pip", "install", "-e", install_dir])
#     else:
#         print(f"Package '{package_name}' is already installed.")


# def check_and_setup_lib():
#     package_name = "nebula"  # 要检查的包名

#     install_dir = "/nebula"  # 安装目录（源目录）

#     # 确保安装目录存在
#     if not os.path.exists(install_dir):
#         print(f"Error: The directory {install_dir} does not exist.")
#         return

#     check_and_install(package_name, install_dir)

# check_and_setup_lib()

@asynccontextmanager
async def lifespan(app: fastapi.FastAPI):
    """生命周期事件管理器"""
    # 启动时的初始化
    logger.info("API服务启动")
    app_ctx = await load_context()

    # 配置FastAPI和uvicorn的日志，以符合我们的堆栈跟踪配置
    configure_framework_logging()

    yield

    # 关闭时使用应用上下文进行优雅关闭
    try:
        await context.graceful_shutdown()
        logger.info("API服务优雅关闭完成")
    except Exception as e:
        logger.error(f"应用关闭时发生错误: {str(e)}", exc_info=True)

def configure_framework_logging():
    """配置框架日志处理器，使其不自动输出异常堆栈"""
    # 获取日志配置
    settings = infrastructure.get_settings()
    show_exc = settings.logging.console_show_exc

    # 如果配置为不在控制台显示异常堆栈，则禁用框架的异常处理器
    if not show_exc:
        # 禁用uvicorn的默认异常处理
        for logger_name in ["uvicorn.error"]:
            log = logging.getLogger(logger_name)
            # 移除所有处理器
            for handler in log.handlers[:]:
                log.removeHandler(handler)
            # 确保不传播到root logger
            log.propagate = False

def create_app() -> fastapi.FastAPI:
    """创建并配置FastAPI应用"""
    app = fastapi.FastAPI(
        title="Nebula API",
        description="Nebula项目API服务",
        version="1.0.0",
        docs_url="/api/docs",
        redoc_url="/api/redoc",
        openapi_url="/api/openapi.json",
        redirect_slashes=False,  # 禁用斜杠重定向
        lifespan=lifespan
    )

    # 添加自定义中间件来拦截异常
    app.add_middleware(ExceptionHandlingMiddleware)

    # 配置CORS
    app.add_middleware(
        fastapi.middleware.cors.CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 注册异常处理程序
    app.add_exception_handler(api_exceptions.APIException, api_exceptions.api_exception_handler)
    app.add_exception_handler(starlette.exceptions.HTTPException, api_exceptions.http_exception_handler)
    app.add_exception_handler(fastapi.exceptions.RequestValidationError, api_exceptions.validation_exception_handler)
    # 确保异常处理器被正确注册
    app.add_exception_handler(Exception, api_exceptions.exception_handler)

    # 注册路由
    import nebula.api.routers as routers
    app.include_router(routers.api_router, prefix="/api")

    return app

async def load_context():
    # 使用应用上下文进行初始化，会配置日志
    try:
        app_ctx = await context.init_application(
            app_name="Nebula API",
            debug=True,
            components=COMPONENTS
        )
        # 日志配置已由上下文完成
        logger.info("应用初始化完成")
        return app_ctx
    except Exception as e:
        logger.error(f"应用初始化失败: {str(e)}", exc_info=True)
        exit(1)


app = create_app()

if __name__ == "__main__":
    settings = infrastructure.get_settings()

    uvicorn.run(
        app,
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.api.reload,
        workers=settings.api.workers,
        proxy_headers=True,
        forwarded_allow_ips="*"
    )