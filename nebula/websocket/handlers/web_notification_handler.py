#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Web通知处理器
这是一个RabbitMQ队列的消费者，用于处理Web通知消息并通过WebSocket广播给客户端
"""

import logging
from typing import Dict, Any
from nebula.core.protocol import WebNotification
from nebula.core.handlers.message_handler import MessageHandler

class WebNotificationHandler(MessageHandler[WebNotification]):
    """Web通知处理器，负责接收通知消息并通过WebSocket广播给订阅的客户端"""

    def __init__(self, connection_manager):
        """
        初始化处理器

        Args:
            connection_manager: WebSocket连接管理器实例
        """
        super().__init__(WebNotification)
        self.connection_manager = connection_manager
        self.logger = logging.getLogger(self.__class__.__name__)

    async def process(self, message: WebNotification) -> None:
        """
        处理Web通知消息

        Args:
            message: Web通知消息
        """
        self.logger.info(f"处理Web通知: {message.event} \n {message.from_id}")

        try:
            websocket_message = None
            if message.event == "message.NOTIFICATION":
                websocket_message = {
                    "event": message.event,
                    "text": message.text
                }
                websocket_message.update(message.metadata)
            else:
                # 构造要发送的消息
                websocket_message = {
                    "event": message.event,
                    "task_id": message.from_id,
                    "text": message.text,
                    "failed": message.failed,
                    "data": message.metadata
                }


            self.logger.info(f"准备广播消息到主题: {message.to_web_topic}, 内容: {websocket_message}")

            import asyncio
            await asyncio.sleep(2)
            # 广播消息到对应主题
            await self.connection_manager.broadcast_to_topic(
                message.to_web_topic,
                websocket_message
            )

        except Exception as e:
            self.logger.error(f"处理Web通知时发生错误: {str(e)}")
            # 由父类的handle_error和异常重抛机制处理错误