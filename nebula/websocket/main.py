#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WebSocket服务主模块
"""

import logging
import os
import sys
import uuid
from pathlib import Path
from typing import Dict, Any, Optional, Set, List
from contextlib import asynccontextmanager
import fastapi
import nebula.core.context as context
import nebula.websocket.handlers.web_notification_handler as web_notification

# 创建日志记录器
logger = logging.getLogger(__name__)

# 开发模式配置
DEV_MODE = os.getenv("NEBULA_DEV_MODE", "false").lower() == "true"
if DEV_MODE:
    logger.warning("⚠️ 开发模式已启用：WebSocket连接将跳过鉴权！")


components=["message","auth","cache"]

class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        """初始化连接管理器"""
        # 使用新的数据结构: {"ip:port": {"connection": conn, "topic_list": [], "client_id": user_id}}
        self.connections: Dict[str, Dict[str, Any]] = {}
        self.logger = logging.getLogger(self.__class__.__name__)
        
    def _get_connection_key(self, websocket: fastapi.WebSocket) -> str:
        """获取连接的唯一键 (IP:port)"""
        client_host = websocket.client.host
        client_port = websocket.client.port
        return f"{client_host}:{client_port}"
    
    async def connect(self, client_id: str, websocket: fastapi.WebSocket):
        """建立WebSocket连接"""
        await websocket.accept()
        connection_key = self._get_connection_key(websocket)
        
        # 创建连接记录
        self.connections[connection_key] = {
            "connection": websocket,
            "topic_list": set(),
            "client_id": client_id
        }
        self.logger.info(f"客户端连接成功: {client_id} ({connection_key})")
        return connection_key
    
    async def disconnect(self, connection_key: str):
        """断开WebSocket连接"""
        if connection_key in self.connections:
            client_id = self.connections[connection_key]["client_id"]
            topic_list = self.connections[connection_key]["topic_list"]
            self.logger.info(f"客户端 {client_id} ({connection_key}) 断开连接，取消订阅主题: {topic_list}")
            del self.connections[connection_key]
            self.logger.info(f"客户端断开连接: {client_id} ({connection_key})")
    
    async def subscribe(self, connection_key: str, topic: str):
        """订阅主题"""
        if connection_key in self.connections:
            self.connections[connection_key]["topic_list"].add(topic)
            client_id = self.connections[connection_key]["client_id"]
            self.logger.info(f"客户端 {client_id} ({connection_key}) 订阅主题: {topic}, 当前订阅: {self.connections[connection_key]['topic_list']}")
            return True
        else:
            self.logger.warning(f"连接 {connection_key} 未存在，无法订阅主题: {topic}")
            return False
    
    async def unsubscribe(self, connection_key: str, topic: str):
        """取消订阅主题"""
        if connection_key in self.connections:
            if topic in self.connections[connection_key]["topic_list"]:
                self.connections[connection_key]["topic_list"].discard(topic)
                client_id = self.connections[connection_key]["client_id"]
                self.logger.info(f"客户端 {client_id} ({connection_key}) 取消订阅主题: {topic}, 当前订阅: {self.connections[connection_key]['topic_list']}")
                return True
            else:
                client_id = self.connections[connection_key]["client_id"]
                self.logger.warning(f"客户端 {client_id} ({connection_key}) 尝试取消未订阅的主题: {topic}")
                return False
        else:
            self.logger.warning(f"连接 {connection_key} 未存在，无法取消订阅主题: {topic}")
            return False
    
    async def broadcast_to_topic(self, topic: str, message: dict):
        """向订阅了指定主题的客户端广播消息"""
        recipients_count = 0
        
        for connection_key, conn_data in self.connections.items():
            if topic in conn_data["topic_list"]:
                try:
                    await conn_data["connection"].send_json(message)
                    recipients_count += 1
                    client_id = conn_data["client_id"]
                    self.logger.debug(f"向客户端 {client_id} ({connection_key}) 发送消息: {message}")
                except Exception as e:
                    client_id = conn_data["client_id"]
                    self.logger.error(f"向客户端 {client_id} ({connection_key}) 发送消息失败: {str(e)}", exc_info=True)
                    await self.disconnect(connection_key)
        
        if recipients_count > 0:
            self.logger.info(f"已向 {recipients_count} 个客户端广播主题 {topic} 的消息")
        else:
            self.logger.warning(f"主题 {topic} 没有订阅者，消息未广播")
    
    def get_client_connections(self, client_id: str) -> List[str]:
        """获取指定客户端ID的所有连接键"""
        return [key for key, data in self.connections.items() if data["client_id"] == client_id]
    
    def get_topic_subscribers(self, topic: str) -> int:
        """获取订阅了指定主题的客户端数量"""
        return sum(1 for conn_data in self.connections.values() if topic in conn_data["topic_list"])

# 创建连接管理器实例
manager = ConnectionManager()

@asynccontextmanager
async def lifespan(app: fastapi.FastAPI):
    """
    FastAPI生命周期事件管理器
    """
    try:
        app_ctx = await context.init_application(
            app_name="Nebula API", 
            debug=DEV_MODE,
            components=components
        )
        app.state.app_context = app_ctx
        
        # 日志配置已由上下文完成
        logger.info("应用初始化完成")
        
         # 获取服务实例
        message_service = app_ctx.get_component("message")
        
        # 初始化处理器
        handlers = [web_notification.WebNotificationHandler(manager)]
        for handler in handlers:
            await handler.setup(message_service)
        
        logger.info("开始消费消息...")
        # 开始消费消息
        await message_service.start_consuming_all()
        
        logger.info("WebSocket服务启动成功")
        yield
    finally:
        # 关闭时的清理
        try:
            # 断开所有客户端连接
            for connection_key in list(manager.connections.keys()):
                await manager.disconnect(connection_key)
            # 通过应用上下文关闭服务
            await context.graceful_shutdown()
            logger.info("WebSocket服务关闭成功")
        except Exception as e:
            logger.error(f"WebSocket服务关闭时发生错误: {str(e)}", exc_info=True)

# 创建FastAPI应用
app = fastapi.FastAPI(
    title="Nebula WebSocket Service",
    description="Nebula项目WebSocket服务",
    version="1.0.0",
    docs_url="/socket.io/docs",
    redoc_url="/socket.io/redoc",
    openapi_url="/socket.io/openapi.json",
    lifespan=lifespan
)

@app.websocket("/socket.io/{token}")
async def websocket_endpoint(websocket: fastapi.WebSocket, token: str):
    """WebSocket连接端点"""
    logger.info(f"收到WebSocket连接请求")
    
    # 使用共享鉴权模块进行验证
    client_id = ""
    try:
        if not token:
            logger.warning(f"未提供认证令牌")
            await websocket.close(code=1008, reason="认证失败：未提供令牌")
            return
        
        app_ctx = context.get_app_context()
        auth_service = app_ctx.get_component("auth")
        userinfo = await auth_service.authenticate_token(token)
        client_id = userinfo["username"]

    except Exception as e:
        logger.warning(f"Token({token})的认证失败: {str(e)}")
        await websocket.close(code=1008, reason="认证失败")
        return
        
    try:
        # 建立连接
        connection_key = await manager.connect(client_id, websocket)
        logger.info(f"成功接受 {client_id} 的WebSocket连接，连接键: {connection_key}")
        
        # 告知客户端其连接信息
        await websocket.send_json({
            "event": "connected",
            "client_id": client_id,
            "connection_key": connection_key
        })
        
        while True:
            try:
                # 接收消息
                data = await websocket.receive_json()
                logger.info(f"接收到客户端 {client_id} ({connection_key}) 的消息: {data}")
                
                # 处理订阅/取消订阅请求
                action = data.get("action")
                topic = data.get("topic")
                if action == "ping":
                    await websocket.send_json({"event": "pong"})
                elif action and topic:
                    if action == "subscribe":
                        success = await manager.subscribe(connection_key, topic)
                        response = {
                            "event": "subscribed" if success else "error",
                            "topic": topic,
                            "success": success
                        }
                        if not success:
                            response["message"] = "订阅失败"
                        await websocket.send_json(response)
                    elif action == "unsubscribe":
                        success = await manager.unsubscribe(connection_key, topic)
                        response = {
                            "event": "unsubscribed" if success else "error",
                            "topic": topic,
                            "success": success
                        }
                        if not success:
                            response["message"] = "取消订阅失败"
                        await websocket.send_json(response)
                    else:
                        logger.warning(f"客户端 {client_id} ({connection_key}) 发送了未知操作: {action}")
                        await websocket.send_json({
                            "event": "error",
                            "message": f"未知操作: {action}"
                        })
                else:
                    logger.warning(f"客户端 {client_id} ({connection_key}) 发送了无效消息: {data}")
                    # await websocket.send_json({
                    #     "event": "error",
                    #     "message": "无效请求，缺少action或topic"
                    # })
                
            except fastapi.WebSocketDisconnect:
                logger.info(f"客户端 {client_id} ({connection_key}) WebSocket断开连接")
                await manager.disconnect(connection_key)
                break
            except Exception as e:
                logger.error(f"处理WebSocket消息失败: {str(e)}", exc_info=True)
                await websocket.send_json({
                    "event": "error",
                    "message": str(e)
                })
                break
    except Exception as e:
        logger.error(f"WebSocket连接失败: {str(e)}", exc_info=True)
        
        connection_key = manager._get_connection_key(websocket)
        if connection_key in manager.connections:
            await manager.disconnect(connection_key)

if __name__ == "__main__":
    import uvicorn
    
    # 从环境变量获取配置，如果没有则使用默认值
    host = os.getenv("WEBSOCKET_HOST", "0.0.0.0")
    port = int(os.getenv("WEBSOCKET_PORT", 8092))
    
    # 启动WebSocket服务
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=DEV_MODE,  # 开发模式下启用热重载
        log_level="info" if DEV_MODE else "info"
    ) 