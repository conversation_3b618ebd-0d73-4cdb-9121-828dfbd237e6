rabbitmq:
  host: "*************"
  port: 5672
  username: "admin"
  password: "admin"
  vhost: "/"

  # 延迟消息配置 - 使用rabbitmq_delayed_message_exchange插件
  delayed_exchange: nebula.delayed  # 延迟交换机名称
  delayed_exchange_type: x-delayed-message  # 延迟交换机类型
  max_retry_count: 3  # 最大重试次数
  retry_intervals: [60, 300, 1800]  # 重试间隔：1分钟、5分钟、30分钟

# Redis配置
redis:
  host: nas.fee.red
  port: 6379
  password: yi3K_6UyCZDh-2*Bu7.v
  db: 0
  pool_size: 10
  socket_timeout: 5
  socket_connect_timeout: 5

logging:
  level: INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  file: logs/nebula.log
  max_size_mb: 10
  backup_count: 5
  format: text  # json 或 text
  text_format: "%(asctime)s [%(levelname)s] %(name)s - %(message)s"
  date_format: "%Y-%m-%d %H:%M:%S"
  stacktrace_file: logs/nebula_error.log
  console_show_exc: true