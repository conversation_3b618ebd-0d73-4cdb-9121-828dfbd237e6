# Nebula WebSocket 服务

## 概述

Nebula WebSocket 服务提供实时消息推送功能，支持：
- 客户端订阅/取消订阅特定主题
- 基于主题的消息广播
- 与RabbitMQ集成的消息通知系统
- 基于JWT的用户认证和权限控制（可在开发模式下跳过）

## 开发模式

在开发阶段，可以通过设置环境变量 `NEBULA_DEV_MODE=true` 来启用开发模式。在开发模式下：
- WebSocket连接不需要提供token
- 所有用户都具有管理员权限
- 可以订阅任何主题
- 跳过所有权限检查

### 开发模式下的连接示例

```javascript
// 开发模式下的WebSocket连接
const ws = new WebSocket('ws://localhost:8092/ws/dev_client_123');

// 连接成功处理
ws.onopen = () => {
    console.log('WebSocket连接成功');
    // 订阅主题
    ws.send(JSON.stringify({
        action: 'subscribe',
        topic: 'video.status'
    }));
};

// 接收消息
ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    console.log('收到消息:', message);
};

// 错误处理
ws.onerror = (error) => {
    console.error('WebSocket错误:', error);
};

// 连接关闭处理
ws.onclose = (event) => {
    console.log('WebSocket连接关闭:', event.code, event.reason);
};
```

## 生产模式

在生产环境中，需要进行正确的身份验证和权限控制。

## 功能特性

1. WebSocket连接管理
   - 支持多客户端连接
   - JWT令牌认证
   - 自动断开处理
   - 连接状态监控

2. 主题订阅系统
   - 动态订阅/取消订阅
   - 基于主题的消息过滤
   - 支持多主题订阅
   - 基于用户权限的订阅控制

3. 消息广播
   - 定向推送
   - 主题广播
   - 错误处理和重试机制
   - 权限动态检查

## 快速开始

### 获取访问令牌

在建立WebSocket连接前，需要先通过API获取JWT令牌：

```bash
curl -X POST "http://localhost:8091/api/auth" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=admin"
```

响应示例：
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIs...",
  "token_type": "bearer"
}
```

### 建立连接

使用获取到的token建立WebSocket连接：

```javascript
// 获取到的JWT令牌
const token = "eyJhbGciOiJIUzI1NiIs...";

// 建立WebSocket连接
const ws = new WebSocket(`ws://localhost:8092/ws/client123?token=${token}`);

// 连接错误处理
ws.onerror = (error) => {
  console.error('WebSocket连接错误:', error);
};

// 连接关闭处理
ws.onclose = (event) => {
  console.log('WebSocket连接关闭:', event.code, event.reason);
  // 如果是token相关错误，需要重新获取token
  if (event.code === 1008) {
    console.log('需要重新获取token');
  }
};
```

### 订阅主题

```javascript
ws.send(JSON.stringify({
  action: 'subscribe',
  topic: 'video.status'
}));
```

### 取消订阅

```javascript
ws.send(JSON.stringify({
  action: 'unsubscribe',
  topic: 'video.status'
}));
```

### 接收消息

```javascript
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  console.log('收到消息:', message);
};
```

## 主题权限说明

系统预定义的主题及其权限：

- `video.status`: 所有用户可订阅
  - 视频处理状态更新
  - 转码进度
  - 错误通知

- `shortlink.stats`: 所有用户可订阅
  - 点击统计
  - 访问来源
  - 实时数据

- `cloudflare.sync`: 仅管理员可订阅
  - DNS记录同步状态
  - 操作结果
  - 错误通知

- `system.status`: 仅管理员可订阅
  - 系统运行状态
  - 资源使用情况
  - 告警信息

## 配置说明

配置文件 `config.yaml` 包含以下主要配置：

1. 服务器配置
   - host: 监听地址
   - port: 监听端口
   - workers: 工作进程数
   - log_level: 日志级别

2. RabbitMQ配置
   - host: RabbitMQ服务器地址
   - port: RabbitMQ端口
   - username: 用户名
   - password: 密码
   - notification_queue: 通知队列名

3. 日志配置
   - 控制台输出
   - 文件日志
   - 日志轮转

## 开发指南

### 添加新的消息处理

1. 在 `handle_notification` 函数中添加新的消息类型处理：

```python
async def handle_notification(message: WebSocketNotification):
    try:
        message_dict = message.dict()
        topic = message_dict.get("topic")
        if topic:
            await manager.broadcast_to_topic(topic, message_dict)
    except Exception as e:
        logger.error(f"处理通知消息时发生错误: {str(e)}")
```

### 自定义消息过滤

在 `ConnectionManager` 类中添加自定义的消息过滤逻辑：

```python
async def should_send_message(self, client_id: str, message: dict) -> bool:
    # 实现自定义的消息过滤逻辑
    return True
```

## 错误处理

1. 认证错误
   - 1008: 策略违规（无效token、token过期等）
   - 1011: 内部错误

2. 权限错误
   - 订阅被拒绝
   - 权限变更导致的自动取消订阅

3. 连接错误
   - 自动重连机制
   - 错误日志记录

## 监控指标

服务提供以下监控指标：

1. 连接统计
   - 活动连接数
   - 连接失败率
   - 平均连接时长

2. 消息统计
   - 消息处理速率
   - 错误率
   - 消息延迟

3. 系统资源
   - CPU使用率
   - 内存使用
   - 网络IO 