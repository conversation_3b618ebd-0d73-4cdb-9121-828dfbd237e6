FROM python:3.10-slim

WORKDIR /app

ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 安装依赖
COPY requirements.txt .
# 复制应用代码
# COPY . .

RUN mkdir -p /root/.pip && \
    echo "[global]" > /root/.pip/pip.conf && \
    echo "index-url = https://mirrors.aliyun.com/pypi/simple/" >> /root/.pip/pip.conf && \
    sed -i "<EMAIL>@mirrors.aliyun.com@g" /etc/apt/sources.list.d/debian.sources && apt update && \
    apt install -y supervisor && touch /var/run/supervisor.sock

RUN pip install -r requirements.txt

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV CONSUMER_TYPE=all

# 启动消费者
CMD ["tail", "-f", "/dev/null"] 