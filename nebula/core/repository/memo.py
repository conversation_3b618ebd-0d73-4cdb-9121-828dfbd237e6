from datetime import datetime
from typing import Dict, List, Optional, Any, Annotated, Union

from bson import ObjectId
from pydantic import BaseModel, HttpUrl, Field, BeforeValidator, model_validator
from nebula.core.infrastructure.exceptions import NotFoundError,ValidationError
from nebula.core.infrastructure import provider
from nebula.core.repository.base import BaseEntity, BaseRepository

# 自定义 ObjectId 类型
class Entity:
    class BaseMemo(BaseEntity):
        """基础备忘录实体"""
        type: str  # 文档类型：text, image, file, share
        text: Optional[str] = None  # 文本内容，基类中允许为空
        created_at: datetime = Field(default_factory=datetime.now)  # 添加时间，使用default_factory确保每次实例化时生成新的时间
        generated: bool = False  # 是否已通过AI生成内容
        gen_text: Optional[str] = None  # AI生成的文本内容

        def __init__(self, **data):
            """初始化基础备忘录，处理id字段"""
            super().__init__(**data)

        @staticmethod
        def from_dict(data: Dict[str, Any]) -> "Entity.BaseMemo":
            """工厂方法：根据类型创建对应的备忘录实体

            Args:
                data: 包含备忘录数据的字典

            Returns:
                对应类型的备忘录实体

            Raises:
                ValidationError: 当类型无效或找不到对应的类时抛出
            """
            memo_type = data.get("type")
            if not memo_type:
                raise ValidationError("备忘录类型不能为空")

            # 将类型转换为 PascalCase 格式
            class_name = "".join(word.capitalize() for word in memo_type.split("_")) + "Memo"

            try:
                # 动态获取对应的类
                memo_cls = getattr(Entity, class_name)
                # 验证获取的类是否是 BaseMemo 的子类
                if not issubclass(memo_cls, Entity.BaseMemo):
                    raise ValidationError(f"无效的备忘录类型: {memo_type}")
                return memo_cls(**data)
            except AttributeError:
                raise ValidationError(f"未找到对应的备忘录类型: {memo_type}")

    class TextMemo(BaseMemo):
        """文本类型备忘录"""
        type: str = "text"
        text: str  # 文本类型备忘录中text不允许为空

        def __init__(self, **data):
            """验证文本备忘录的必要字段"""
            super().__init__(**data)
            if not self.text or not self.text.strip():
                raise ValidationError("文本内容不能为空")

    class FileMemo(BaseMemo):
        """文件类型备忘录"""
        type: str = "file"
        file_type: str  # 文件类型，例如".pdf"
        file_size: int  # 文件大小，单位为字节
        file_key: str  # 文件存储路径
        stock_code: Optional[str] = None  # 股票代码，格式如"SH000001"

        def __init__(self, **data):
            """验证文件备忘录的必要字段"""
            super().__init__(**data)
            if not self.file_key or not self.file_key.strip():
                raise ValidationError("文件路径不能为空")
            if not self.file_type or not self.file_type.strip():
                raise ValidationError("文件类型不能为空")
            if not self.file_size or self.file_size <= 0:
                raise ValidationError("文件大小必须大于0")
            if self.stock_code:
                if not self.stock_code.strip():
                    raise ValidationError("股票代码不能为空字符串")
                if not self.stock_code.startswith(('SH', 'SZ', 'BJ')):
                    raise ValidationError("股票代码格式错误，必须以SH、SZ或BJ开头")
                if not self.stock_code[2:].isdigit() or len(self.stock_code) != 8:
                    raise ValidationError("股票代码格式错误，必须为6位数字")

    class ImageMemo(BaseMemo):
        """图片类型备忘录"""
        type: str = "image"
        file_key: str  # 文件存储路径

        def __init__(self, **data):
            """验证图片备忘录的必要字段"""
            super().__init__(**data)
            if not self.file_key or not self.file_key.strip():
                raise ValidationError("图片路径不能为空")

    class ShareMemo(BaseMemo):
        """分享类型备忘录"""
        type: str = "share"
        link: str = Field(default_factory=str)  # 分享链接，必须是有效URL
        desc: Optional[str] = None  # 描述信息
        icon: Optional[str] = ""  # 图标URL
        stock_code: Optional[str] = None  # 股票代码

        def __init__(self, **data):
            """验证分享备忘录的必要字段"""
            super().__init__(**data)
            if not self.text or not self.text.strip():
                raise ValidationError("分享标题不能为空")
            if not str(self.link):
                raise ValidationError("分享链接不能为空")
            if self.desc and not self.desc.strip():
                raise ValidationError("描述不能为空字符串")
            if self.icon and not str(self.icon):
                raise ValidationError("图标URL不能为空字符串")
            if self.stock_code:
                if not self.stock_code.strip():
                    raise ValidationError("股票代码不能为空字符串")
                if not self.stock_code.startswith(('SH', 'SZ', 'BJ')):
                    raise ValidationError("股票代码格式错误，必须以SH、SZ或BJ开头")
                if not self.stock_code[2:].isdigit() or len(self.stock_code) != 8:
                    raise ValidationError("股票代码格式错误，必须为6位数字")



class MemoRepository(BaseRepository[Entity.BaseMemo]):
    """备忘录仓储类"""

    def __init__(self):
        """初始化仓储"""
        super().__init__("memo", Entity.BaseMemo)

    async def find_many(self, query: Dict = None, skip: int = 0, limit: int = 20,
                       sort_field: str = "created_at", sort_order: int = -1) -> List[Entity.BaseMemo]:
        """重写find_many方法，默认按created_at排序"""
        return await super().find_many(query, skip, limit, sort_field, sort_order)