from datetime import datetime
from typing import Dict, List, Optional, Any
from bson import ObjectId
from pydantic import BaseModel, Field, validator
from nebula.core.infrastructure.exceptions import ValidationError
from nebula.core.infrastructure import provider
from nebula.core.repository.base import BaseEntity, BaseRepository

class Entity:
    class Video(BaseEntity):
        """视频实体

        表示系统中的视频资源，包含视频元数据、内容分析和存储位置。
        """
        updated_at: datetime = Field(default_factory=datetime.now, description="视频更新时间")
        cover_path: str = Field(..., description="封面图片在存储服务中的路径")
        video_path: str = Field(..., description="视频文件在存储服务中的路径")
        cover_url: str = Field(..., description="封面图片的访问URL")
        video_size: int = Field(..., description="视频文件大小(字节)")
        video_id: Optional[str] = Field(None, description="视频源平台ID")
        video_desc: str = Field(..., description="视频描述文本")
        play_url: str = Field(..., description="视频播放URL")
        subtitles: Optional[List[str]] = Field(None, description="字幕文本(完整)")
        tags: List[str] = Field(default_factory=list, description="视频标签列表")
        title: str = Field("", description="视频标题")
        generated: bool = Field(False, description="内容是否由AI生成")
        overview: str = Field("", description="视频内容概述")
        category: str = Field("", description="视频主类别")
        highlights: List[str] = Field(default_factory=list, description="视频内容亮点/关键点列表")
        location: str = Field("", description="视频相关地理位置")
        sub_category: str = Field("", description="视频子类别")
        subtitles_chunks: List[str] = Field(default_factory=list, description="字幕文本片段列表")
        source_url: str = Field(..., description="视频源URL")
        platform: str = Field("", description="视频来源平台(douyin, youtube, twitter等)")

        def __init__(self, **data):
            """初始化视频实体"""
            super().__init__(**data)

        @validator('video_size')
        def video_size_must_be_positive(cls, v):
            """验证视频大小必须为正数"""
            if v <= 0:
                raise ValueError("视频文件大小必须大于0")
            return v

        @staticmethod
        def from_dict(data: Dict[str, Any]) -> "Entity.Video":
            """从字典创建视频实体"""
            return Entity.Video(**data)


class VideoRepository(BaseRepository[Entity.Video]):
    """视频仓储类"""

    def __init__(self):
        """初始化仓储"""
        super().__init__("video", Entity.Video)

    async def find_by_tags(self, tags: List[str], skip: int = 0, limit: int = 20, sort_field: str = "updated_at", sort_order: int = -1) -> List[Entity.Video]:
        """按标签查找视频

        Args:
            tags: 标签列表
            skip: 跳过数量
            limit: 限制数量
            sort_field: 排序字段，默认为"updated_at"
            sort_order: 排序方式，1为升序，-1为降序，默认为-1（降序）

        Returns:
            视频实体列表
        """
        query = {"tags": {"$in": tags}}
        return await self.find_many(query, skip, limit, sort_field, sort_order)

    async def find_by_category(self, category: str, skip: int = 0, limit: int = 20, sort_field: str = "updated_at", sort_order: int = -1) -> List[Entity.Video]:
        """按类别查找视频

        Args:
            category: 类别
            skip: 跳过数量
            limit: 限制数量
            sort_field: 排序字段，默认为"updated_at"
            sort_order: 排序方式，1为升序，-1为降序，默认为-1（降序）

        Returns:
            视频实体列表
        """
        query = {"category": category}
        return await self.find_many(query, skip, limit, sort_field, sort_order)

    async def search_by_keyword(self, keyword: str, skip: int = 0, limit: int = 20, sort_field: str = "updated_at", sort_order: int = -1) -> List[Entity.Video]:
        """按关键词搜索视频

        Args:
            keyword: 搜索关键词
            skip: 跳过数量
            limit: 限制数量
            sort_field: 排序字段，默认为"updated_at"
            sort_order: 排序方式，1为升序，-1为降序，默认为-1（降序）

        Returns:
            视频实体列表
        """
        query = {
            "$or": [
                {"title": {"$regex": keyword, "$options": "i"}},
                {"video_desc": {"$regex": keyword, "$options": "i"}},
                {"overview": {"$regex": keyword, "$options": "i"}},
                {"subtitles": {"$regex": keyword, "$options": "i"}}
            ]
        }
        return await self.find_many(query, skip, limit, sort_field, sort_order)