"""
Cloudflare仓储模块

包含:
1. 实体定义 - Entity.DnsRecord/Domain/Account
2. 仓储实现 - CloudflareRepository
"""
from datetime import datetime
from typing import List, Optional, Dict, Any, ForwardRef
from bson import ObjectId
from nebula.core.repository.base import BaseEntity, BaseRepository
from pydantic import BaseModel, Field
from nebula.core.infrastructure import provider
from nebula.core.infrastructure.fields import ObjectIdField
from nebula.core.infrastructure.exceptions import RuntimeError

# =============== 实体定义 ===============

class Entity:
    """Cloudflare实体命名空间"""

    class DnsRecord(BaseEntity):
        """DNS记录"""
        sub: str
        type: str
        content: str
        ttl: int = 1
        proxied: bool = False
        priority: Optional[int] = None
        record_id: Optional[str] = None
        created_at: Optional[datetime] = Field(default_factory=datetime.now)
        updated_at: Optional[datetime] = Field(default_factory=datetime.now)

    class Domain(BaseEntity):
        """域名"""
        zone_id: str
        status: str = "active"
        dns: List[ForwardRef("Entity.DnsRecord")] = []
        created_at: datetime = Field(default_factory=datetime.now)
        updated_at: datetime = Field(default_factory=datetime.now)

        # def to_dict(self) -> Dict[str, Any]:
        #     """将对象转换为字典"""
        #     return {
        #         "zone_id": self.zone_id,
        #         "status": self.status,
        #         "dns": [record.to_dict() for record in self.dns],
        #         "created_at": self.created_at,
        #         "updated_at": self.updated_at
        #     }

        # @classmethod
        # def from_dict(cls, data: Dict[str, Any]) -> "Entity.Domain":
        #     """从字典创建对象"""
        #     dns_records = []
        #     for record_data in data.get("dns", []):
        #         dns_records.append(Entity.DnsRecord.from_dict(record_data))

        #     return cls(
        #         zone_id=data.get("zone_id"),
        #         status=data.get("status", "active"),
        #         dns=dns_records,
        #         created_at=data.get("created_at"),
        #         updated_at=data.get("updated_at")
        #     )

    class Account(BaseEntity):
        """Cloudflare账户"""
        account_id: str
        api_key: str
        domain_list: Dict[str, ForwardRef("Entity.Domain")] = {}
        created_at: datetime = Field(default_factory=datetime.now)
        updated_at: datetime = Field(default_factory=datetime.now)

        # @classmethod
        # def from_dict(cls, data: Dict[str, Any]) -> "Entity.Account":
        #     """从字典创建对象"""
        #     domain_list = {}
        #     for domain_name, domain_data in data.get("domain_list", {}).items():
        #         domain_list[domain_name] = Entity.Domain.from_dict(domain_data)

        #     return cls(
        #         _id=data.get("_id"),
        #         account_id=data.get("account_id"),
        #         api_key=data.get("api_key"),
        #         domain_list=domain_list,
        #         created_at=data.get("created_at"),
        #         updated_at=data.get("updated_at")
        #     )

# 更新前向引用
Entity.Domain.model_rebuild()
Entity.Account.model_rebuild()

# =============== 仓储实现 ===============

class CloudflareRepository(BaseRepository[Entity.Account]):
    """Cloudflare仓储类"""

    def __init__(self):
        """
        初始化仓储
        """
        super().__init__("cloudflare", Entity.Account)

    async def save_account(self, account: Entity.Account):
        """保存账户"""
        account.updated_at = datetime.now()
        if account.id:
            account.id = None
        self.collection.update_one(
            {},
            {"$set": account.dict()},
            upsert=True
        )
        return True

    async def save_dns_record(self, account: Entity.Account, domain_name: str, record: Entity.DnsRecord):
        """保存DNS记录"""
        domain = account.domain_list[domain_name]
        if not domain:
            return
        up_record = False
        for i, r in enumerate(account.domain_list[domain_name].dns):
            if r.record_id == record.record_id:
                account.domain_list[domain_name].dns[i] = record
                up_record = True
                break
        if not up_record:
            account.domain_list[domain_name].dns.append(record)

        await self.save_account(account)

    async def delete_dns_record(self,account:Entity.Account, domain_name: str, record_id: str) -> bool:
        """删除DNS记录"""
        domain = account.domain_list[domain_name]
        if not domain:
            return False

        account.domain_list[domain_name].dns = [r for r in domain.dns if r.record_id != record_id]
        await self.save_account(account)
        return True

# 导出所需的类
__all__ = [
    'Entity',
    'CloudflareRepository'
]