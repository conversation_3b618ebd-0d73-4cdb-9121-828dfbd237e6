#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通知仓储模块

包含:
1. 实体定义 - Entity.Notification
2. 仓储实现 - NotificationRepository
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from bson import ObjectId
from nebula.core.repository.base import BaseEntity, BaseRepository
from pydantic import Field

class Entity:
    """通知实体命名空间"""

    class Notification(BaseEntity):
        """通知实体"""
        user_id: str  # 用户ID
        title: str  # 通知标题
        message: str  # 通知内容
        from_source: str = "系统"  # 通知来源
        event: str  # 事件类型
        type: str = "info"  # 通知类型：info, success, warning, error
        is_read: bool = False  # 是否已读
        created_at: datetime = Field(default_factory=datetime.now)  # 创建时间
        updated_at: Optional[datetime] = None  # 更新时间
        metadata: Optional[Dict[str, Any]] = None  # 附加元数据
        task_id: Optional[str] = None  # 关联的任务ID

        class Config:
            """模型配置"""
            arbitrary_types_allowed = True


class NotificationRepository(BaseRepository[Entity.Notification]):
    """通知仓储类"""

    def __init__(self):
        """初始化仓储"""
        super().__init__("notification", Entity.Notification)
