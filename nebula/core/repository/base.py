from datetime import datetime
from typing import Optional, Dict, Any, ClassVar, TypeVar, Generic, List, Type
from nebula.core.infrastructure.fields import ObjectIdField
from pydantic import BaseModel, Field
from bson import ObjectId
from nebula.core.infrastructure import provider
from nebula.core.infrastructure.exceptions import NotFoundError


class BaseEntity(BaseModel):
    """实体基类

    所有实体类应继承此类，提供基本的ID和时间戳字段以及MongoDB文档转换方法。
    """
    id: Optional[ObjectIdField] = Field(None, description="实体唯一标识符")

    class Config:
        """模型配置"""
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            ObjectId: lambda v: str(v)
        }

    def __init__(self, **data):
        """初始化实体，处理_id字段"""
        if "_id" in data:
            data["id"] = data.pop("_id")
        super().__init__(**data)

    def to_dict(self) -> Dict[str, Any]:
        """将实体转换为字典，不适用于MongoDB存储(请用dict()方法))

        Returns:
            Dict[str, Any]: 实体的字典表示
        """
        data = self.model_dump(exclude_none=False)
        # 将id转换为_id以符合MongoDB格式
        # if "id" in data and data["id"] is not None:
        #     data["_id"] = data.pop("id")
        # 转换datetime类型为ISO格式字符串
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "BaseEntity":
        """从字典创建实体

        Args:
            data (Dict[str, Any]): 字典数据，通常来自MongoDB

        Returns:
            BaseEntity: 创建的实体实例
        """
        # 将_id转换为id以符合实体字段名
        if "_id" in data:
            data["id"] = str(data.pop("_id"))
        return cls(**data)


# 定义泛型类型变量，用于表示实体类型
T = TypeVar('T', bound=BaseEntity)


class BaseRepository(Generic[T]):
    """仓储基类

    提供通用的CRUD操作，所有仓储类应继承此类。
    """

    def __init__(self, collection_name: str, entity_class: Type[T]):
        """初始化仓储

        Args:
            collection_name: MongoDB集合名称
            entity_class: 实体类，必须是BaseEntity的子类
        """
        from nebula.core.infrastructure import database
        self.db = provider.get(database.MongoDB)
        self.collection = self.db.get_collection(collection_name)
        self.entity_class = entity_class

    def _prepare_query(self, query: Dict) -> Dict:
        """准备查询条件，处理ObjectId转换

        Args:
            query: 原始查询条件

        Returns:
            处理后的查询条件
        """
        query = query or {}
        if "_id" in query and isinstance(query["_id"], str):
            query["_id"] = ObjectId(query["_id"])
        return query

    async def find_one(self, query: Dict = None) -> Optional[T]:
        """查找单个实体

        Args:
            query: 查询条件

        Returns:
            实体对象或None
        """
        query = self._prepare_query(query)
        doc = self.collection.find_one(query)
        if not doc:
            return None
        return self.entity_class.from_dict(doc)

    async def find_many(self, query: Dict = None, skip: int = 0, limit: int = 20,
                       sort_field: str = "updated_at", sort_order: int = -1) -> List[T]:
        """查找多个实体

        Args:
            query: 查询条件
            skip: 跳过数量
            limit: 限制数量
            sort_field: 排序字段
            sort_order: 排序顺序(1升序，-1降序)

        Returns:
            实体对象列表
        """
        query = self._prepare_query(query)
        cursor = self.collection.find(query).skip(skip).limit(limit).sort(sort_field, sort_order)
        data = []
        for doc in cursor:
            data.append(self.entity_class.from_dict(doc))
        return data

    async def save(self, entity: T) -> str:
        """保存实体

        Args:
            entity: 实体对象

        Returns:
            实体ID
        """
        # 更新时间戳
        if hasattr(entity, "updated_at"):
            entity.updated_at = datetime.now()

        # 排除id字段，让MongoDB自动生成或使用现有ID
        doc = entity.dict(exclude={'id'}) if hasattr(entity, 'dict') else entity.model_dump(exclude={'id'})

        if entity.id:
            # 更新现有实体
            self.collection.update_one(
                {"_id": ObjectId(entity.id)},
                {"$set": doc}
            )
            return str(entity.id)
        else:
            # 插入新实体
            result = self.collection.insert_one(doc)
            return str(result.inserted_id)

    async def delete(self, id: str) -> bool:
        """删除实体

        Args:
            id: 实体ID

        Returns:
            是否成功删除
        """
        result = self.collection.delete_one({"_id": ObjectId(id)})
        return result.deleted_count > 0

    async def count(self, query: Dict = None) -> int:
        """统计实体数量

        Args:
            query: 查询条件

        Returns:
            实体数量
        """
        query = self._prepare_query(query)
        return self.collection.count_documents(query)
