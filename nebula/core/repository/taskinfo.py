#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
任务信息仓储模块

包含:
1. 实体定义 - TaskStatus/TaskInfo/TaskLog/TaskAttempt
2. 仓储实现 - TaskInfoRepository
"""

from datetime import datetime
from enum import Enum
from typing import Any, ClassVar, Dict, List, Optional, Literal, Union
from pydantic import BaseModel, Field
from nebula.core.repository.base import BaseEntity, BaseRepository
from nebula.core.infrastructure.exceptions import NotFoundError
from bson import ObjectId


# 定义任务状态枚举
class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"    # 等待执行
    RUNNING = "running"    # 执行中
    SUCCESS = "success"    # 执行成功
    FAILED = "failed"      # 执行失败
    RETRYING = "retrying"  # 重试中
    WAITRETRY = "waitretry"  # 等待重试
    CANCELLED = "cancelled"  # 已取消

# 定义任务日志实体（用于独立集合）
class TaskLog(BaseEntity):
    """单条任务日志记录，适用于 MongoDB 独立集合"""
    task_id: str                                      # 所属任务 ID
    name: str                                         # logger名字
    retry_index: int = 0                              # 第几次重试（0 表示首次执行）
    order_no: int = 0                                 # 日志顺序号
    time: datetime = Field(default_factory=datetime.now)  # 日志时间
    level: Literal["DEBUG", "INFO", "WARNING", "ERROR"] = "INFO"  # 日志等级
    message: str                                      # 日志内容
    step: Optional[int] = None                        # 可选：业务步骤，如 "validate", "upload"
    extra: Optional[Dict[str, Any]] = None            # 结构化附加信息，如 {"user_id": 123, "file": "abc.txt"}

    class Config:
        arbitrary_types_allowed = True

# 定义任务尝试实体
class TaskAttempt(BaseModel):
    """任务尝试实体，记录每次任务执行的尝试"""
    retry_index: int                           # 第几次重试（0 表示首次执行）
    started_at: datetime                       # 开始时间
    finished_at: Optional[datetime] = None     # 结束时间
    status: TaskStatus                         # 状态
    elapsed_ms: Optional[int] = 0           # 执行耗时（毫秒）
    error_message: Optional[str] = None        # 错误信息

    class Config:
        """模型配置"""
        arbitrary_types_allowed = True

class TaskType(str, Enum):
    """任务类型枚举"""
    SCHEDULER = "scheduler"  # 调度任务
    WORKER = "worker"       # 工作任务

# 定义任务信息实体
class TaskInfo(BaseEntity):
    """任务信息实体"""
    # 任务描述模板缓存
    _description_template: ClassVar[Dict[str, str]] = {}

    task_id: str  # 任务ID
    task_name: str  # 任务名称
    description: Optional[str] = None  # 任务描述模板，可以包含{param_name}形式的占位符
    task_type: TaskType = TaskType.WORKER  # 任务类型 (scheduler, worker)
    status: TaskStatus = TaskStatus.PENDING  # 任务状态
    created_at: datetime = Field(default_factory=datetime.now)  # 创建时间
    updated_at: Optional[datetime] = None  # 更新时间
    started_at: Optional[datetime] = None  # 开始执行时间
    next_retry_at: Optional[datetime] = None  # 下次执行时间
    finished_at: Optional[datetime] = None  # 完成时间
    retry_count: int = 0  # 重试次数
    error_message: Optional[str] = None  # 错误信息
    result: Optional[Dict[str, Any]] = None  # 执行结果
    params: Optional[Dict[str, Any]] = None  # 任务参数，用于格式化描述
    labels: Optional[Dict[str, Any]] = None  # 任务标签
    attempts: List[TaskAttempt] = Field(default_factory=list)  # 任务执行尝试记录

    def __init__(self, **data):
        """初始化任务信息"""
        super().__init__(**data)
        self.description = self.formatted_description()



    def formatted_description(self) -> str:
        """获取格式化后的任务描述

        首先尝试从缓存中获取描述模板，如果找不到，则从任务定义中获取并缓存。
        如果模板或description包含{param_name}形式的占位符，会使用params中的值进行替换。

        Returns:
            格式化后的描述
        """
        # 尝试从缓存中获取模板
        template = None
        if self.task_name:
            if self.task_name in TaskInfo._description_template:
                template = TaskInfo._description_template[self.task_name]
            else:
                # 缓存中没有，尝试从任务定义中获取
                try:
                    # 解析任务名称，格式为"module.name:function_name"
                    module_name, func_name = self.task_name.split(":")
                    # 动态导入模块
                    import importlib
                    module = importlib.import_module(module_name)
                    func = getattr(module, func_name, None)

                    # 尝试获取任务描述模板
                    template = None
                    # 尝试从labels获取
                    if hasattr(func, "labels") and isinstance(func.labels, dict) and "description" in func.labels:
                        template = func.labels["description"]
                    
                    # 如果找到模板，缓存它
                    if template:
                        TaskInfo._description_template[self.task_name] = template

                except (ImportError, AttributeError, ValueError) as e:
                    # 如果导入失败或找不到任务函数，使用description字段
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.debug(f"获取任务描述模板失败: {e}")

        # 如果找不到模板，使用description字段
        if not template:
            template = self.description

        # 如果没有模板，返回空字符串
        if not template:
            return ""

        # 如果没有参数，返回模板
        if not self.params:
            return template

        # 格式化模板
        try:
            # print ("x"*100, template, self.params["kwargs"].get("metadata", {}))
            return template.format(**self.params["kwargs"].get("metadata", {}))
        except (KeyError, ValueError):
            # 如果格式化失败，返回原始模板
            return template

    class Config:
        """模型配置"""
        arbitrary_types_allowed = True

# 定义TaskInfoWithLogs扩展模型
class TaskInfoWithLogs(TaskInfo):
    """任务信息实体，包含日志信息"""
    logs: List[TaskLog] = Field(default_factory=list)  # 任务日志（从独立集合中查询）

    class Config:
        """模型配置"""
        arbitrary_types_allowed = True

# 为了保持向后兼容，创建Entity命名空间
class Entity:
    """任务实体命名空间"""
    TaskStatus = TaskStatus
    TaskInfo = TaskInfo
    TaskLog = TaskLog
    TaskAttempt = TaskAttempt
    TaskInfoWithLogs = TaskInfoWithLogs
    TaskType = TaskType



class TaskInfoRepository(BaseRepository[TaskInfo]):
    """任务信息仓储类"""

    def __init__(self):
        """初始化仓储"""
        super().__init__("task_info", TaskInfo)  # 修改集合名称

    async def find_one(self, query: Dict = None, include_logs: bool = True) -> Union[Optional[TaskInfo], Optional[TaskInfoWithLogs]]:
        """查找单个实体，支持动态联表查询日志

        Args:
            query: 查询条件
            include_logs: 是否包含日志信息

        Returns:
            实体对象或None，当include_logs=True时返回TaskInfoWithLogs
        """
        query = self._prepare_query(query)
        if not include_logs:
            # 不需要联表查询，直接使用父类方法
            doc = self.collection.find_one(query)
            if not doc:
                return None
            return self.entity_class.from_dict(doc)

        # 需要联表查询，使用聚合管道
        pipeline = [
            {"$match": query},
            {"$lookup": {
                "from": "task_log",
                "localField": "task_id",
                "foreignField": "task_id",
                "as": "logs"
            }},
            {"$limit": 1}
        ]

        cursor = self.collection.aggregate(pipeline)
        doc = next(cursor, None)

        if not doc:
            return None

        # 将日志转换为TaskLog对象
        logs_data = doc.pop("logs", [])
        # 按_id字段倒序排列日志
        logs_data.sort(key=lambda x: x.get("order_no"), reverse=True)
        logs = [TaskLog.from_dict(log_doc) for log_doc in logs_data]

        # 创建TaskInfo对象
        info_dict = doc

        # 创建TaskInfoWithLogs对象
        info_with_logs = TaskInfoWithLogs(**info_dict)
        info_with_logs.logs = logs

        return info_with_logs



    async def find_logs(self, task_id: str, skip: int = 0, limit: int = 100) -> List[TaskLog]:
        """根据任务ID查找日志

        Args:
            task_id: 任务ID
            skip: 跳过数量
            limit: 限制数量

        Returns:
            日志列表
        """
        query = {"task_id": task_id}
        cursor = self.db.get_collection("task_log").find(query).skip(skip).limit(limit).sort("order_no", -1)
        logs = []
        for doc in cursor:
            logs.append(TaskLog.from_dict(doc))
        return logs

    async def find_by_status(
        self,
        status: TaskStatus,
        skip: int = 0,
        limit: int = 20
    ) -> List[TaskInfo]:
        """根据状态查找任务信息

        Args:
            status: 任务状态
            skip: 跳过数量
            limit: 限制数量

        Returns:
            任务信息列表
        """
        return await self.find_many({"status": status}, skip, limit)

    async def save(self, entity: TaskInfo) -> str:
        """
        因为有联合字段，所以重写save方法，确保不保存logs字段

        Args:
            entity: 实体对象

        Returns:
            实体ID
        """
        # 更新时间戳
        if hasattr(entity, "updated_at"):
            entity.updated_at = datetime.now()

        # 排除id字段和logs字段
        try:
            doc = entity.model_dump(exclude={'id', 'logs'})
        except AttributeError:
            doc = entity.dict(exclude={'id', 'logs'})

        if entity.id:
            # 更新现有实体
            self.collection.update_one(
                {"_id": ObjectId(entity.id)},
                {"$set": doc}
            )
            return str(entity.id)
        else:
            # 插入新实体
            result = self.collection.insert_one(doc)
            return str(result.inserted_id)

    async def update_status(
        self,
        task_id: str,
        status: TaskStatus,
        error_message: Optional[str] = None,
        result: Optional[Dict[str, Any]] = None
    ) -> Optional[TaskInfo]:
        """更新任务状态

        Args:
            task_id: 任务ID
            status: 新状态
            error_message: 错误信息
            result: 执行结果

        Returns:
            更新后的任务信息或None
        """
        info = await self.find_one({"task_id": task_id})
        if not info:
            raise NotFoundError("TaskInfo", f"task_id={task_id}")

        # 更新状态
        info.status = status
        info.updated_at = datetime.now()

        # 根据状态设置其他字段
        if status == TaskStatus.RUNNING:
            info.started_at = datetime.now()
        elif status in (TaskStatus.SUCCESS, TaskStatus.FAILED):
            info.finished_at = datetime.now()

        # 设置错误信息和结果
        if error_message:
            info.error_message = error_message
        if result:
            info.result = result

        # 保存到数据库
        await self.save(info)

        return info
