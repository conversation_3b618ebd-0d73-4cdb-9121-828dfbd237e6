"""
Redis Streams结果后端实现

提供支持Redis Streams的结果后端实现，用于实时获取任务结果。
"""

import logging
import json
import time
import asyncio
from typing import Any, Dict, List, Optional, Set, TypeVar, Union
from redis.asyncio import BlockingConnectionPool, Redis
from taskiq import AsyncResultBackend
from taskiq.abc.serializer import TaskiqSerializer
from taskiq.result import TaskiqResult
from taskiq.serializers import PickleSerializer
from nebula.core.infrastructure.exceptions import TimeoutError
from nebula.core.infrastructure.logging import log_context,calculate_time

logger = logging.getLogger(__name__)

_ReturnType = TypeVar("_ReturnType")

class StreamsRedisResultBackend(AsyncResultBackend[_ReturnType]):
    """支持Redis Streams的结果后端

    直接实现AsyncResultBackend接口，使用Redis Streams功能，允许客户端订阅任务结果的变化。
    这样可以实时获取任务结果，而不是通过轮询方式。
    """

    def __init__(
        self,
        redis_url: str,
        keep_results: bool = True,
        result_ex_time: Optional[int] = None,
        result_px_time: Optional[int] = None,
        max_connection_pool_size: Optional[int] = None,
        serializer: Optional[TaskiqSerializer] = None,
        prefix_str: Optional[str] = None,
        stream_max_len: int = 1000,  # 流的最大长度
        **connection_kwargs: Any
    ):
        """初始化StreamsRedisResultBackend

        Args:
            redis_url: Redis连接URL
            keep_results: 是否在读取后保留结果
            result_ex_time: 结果过期时间（秒）
            result_px_time: 结果过期时间（毫秒）
            max_connection_pool_size: 连接池最大连接数
            serializer: 序列化器
            prefix_str: 键前缀
            stream_max_len: Redis Stream的最大长度
            **connection_kwargs: 传递给Redis连接池的其他参数
        """
        self.redis_pool = BlockingConnectionPool.from_url(
            url=redis_url,
            max_connections=max_connection_pool_size,
            **connection_kwargs,
        )
        # 创建Redis连接对象，避免重复创建
        self.redis = Redis(connection_pool=self.redis_pool)
        self.serializer = serializer or PickleSerializer()
        self.keep_results = keep_results
        self.result_ex_time = result_ex_time
        self.result_px_time = result_px_time
        self.prefix_str = prefix_str
        self.stream_max_len = stream_max_len
        self.stream_name = "taskiq:results"  # Redis Stream的名称

        # 验证过期时间设置
        if self.result_ex_time and self.result_px_time:
            raise ValueError("不能同时设置result_ex_time和result_px_time")

        if (self.result_ex_time is not None and self.result_ex_time <= 0) or \
           (self.result_px_time is not None and self.result_px_time <= 0):
            raise ValueError("过期时间必须大于0")

        logger.info("StreamsRedisResultBackend初始化成功")

    def _task_name(self, task_id: str) -> str:
        """获取任务键名

        Args:
            task_id: 任务ID

        Returns:
            带前缀的任务键名
        """
        if self.prefix_str is None:
            return task_id
        return f"{self.prefix_str}:{task_id}"

    async def set_result(self, task_id: str, result: TaskiqResult[_ReturnType]) -> None:
        """设置任务结果并发布通知

        Args:
            task_id: 任务ID
            result: 任务结果
        """
        # 设置Redis参数
        redis_set_params: Dict[str, Union[str, int, bytes]] = {
            "name": self._task_name(task_id),
            "value": self.serializer.dumpb(getattr(result, "model_dump", result.dict)()),
        }

        if self.result_ex_time:
            redis_set_params["ex"] = self.result_ex_time
        elif self.result_px_time:
            redis_set_params["px"] = self.result_px_time

        # 存储结果
        await self.redis.set(**redis_set_params)  # type: ignore

        # 使用Redis Streams发布通知
        # 序列化结果状态（成功/失败）和执行时间，不包含完整结果以减少消息大小
        message = {
            "task_id": task_id,
            "status": "error" if result.is_err else "success",
            "execution_time": str(result.execution_time),
            "timestamp": str(time.time())
        }

        # 使用xadd命令添加消息到流
        # MAXLEN ~ stream_max_len表示大约保留stream_max_len条消息
        await self.redis.xadd(
            self.stream_name,
            message,
            id='*',  # 自动生成ID
            maxlen=self.stream_max_len,
            approximate=True  # 使用~符号，表示大约保留
        )
        logger.info(f"已发布任务结果通知到Streams: {task_id}")

    @log_context
    async def is_result_ready(self, task_id: str) -> bool:
        """检查任务结果是否就绪

        Args:
            task_id: 任务ID

        Returns:
            结果是否就绪
        """
        return bool(await self.redis.exists(self._task_name(task_id)))

    async def get_result(
        self,
        task_id: str,
        with_logs: bool = False,
    ) -> TaskiqResult[_ReturnType]:
        """获取任务结果

        Args:
            task_id: 任务ID
            with_logs: 是否包含日志

        Returns:
            任务结果

        Raises:
            Exception: 结果不存在
        """
        task_name = self._task_name(task_id)
        if self.keep_results:
            result_value = await self.redis.get(name=task_name)
        else:
            result_value = await self.redis.getdel(name=task_name)

        if result_value is None:
            raise Exception(f"任务 {task_id} 的结果不存在")

        # 反序列化结果
        if hasattr(TaskiqResult, "model_validate"):
            taskiq_result = TaskiqResult[_ReturnType].model_validate(
                self.serializer.loadb(result_value),
            )
        else:
            taskiq_result = TaskiqResult[_ReturnType].parse_obj(
                self.serializer.loadb(result_value),
            )

        if not with_logs:
            taskiq_result.log = None

        return taskiq_result

    @log_context
    async def get_last_stream_id(self) -> str:
        """获取流的最后一个ID的下一个ID

        Returns:
            最后一个ID的下一个ID，如果流不存在或出现错误则返回'$'
        """
        try:
            # 检查流是否存在
            if not await self.redis.exists(self.stream_name):
                logger.info(f"流 {self.stream_name} 不存在，返回特殊ID '$'")
                return '$'  # 流不存在，返回特殊ID '$'，表示只读取新消息

            # 获取流的最后一个消息
            last_messages = await self.redis.xrevrange(
                self.stream_name,
                count=1  # 只获取最后一条
            )

            if not last_messages:
                logger.info(f"流 {self.stream_name} 存在但没有消息，返回特殊ID '$'")
                return '$'  # 流存在但没有消息，返回特殊ID '$'

            # 安全地获取消息ID
            if len(last_messages) > 0 and len(last_messages[0]) > 0:
                message_id = last_messages[0][0]
                if isinstance(message_id, bytes):
                    message_id = message_id.decode('utf-8')
                else:
                    message_id = str(message_id)

                # 解析消息ID，格式为"timestamp-sequence"
                parts = message_id.split('-')
                if len(parts) == 2:
                    timestamp, sequence = parts
                    # 返回下一个ID，即sequence+1
                    next_id = f"{timestamp}-{int(sequence) + 1}"
                    logger.info(f"获取到流 {self.stream_name} 的最后一个ID: {message_id}，返回下一个ID: {next_id}")
                    return next_id
                else:
                    logger.warning(f"消息ID格式不符合预期: {message_id}，返回特殊ID '$'")
                    return '$'
            else:
                logger.warning(f"流 {self.stream_name} 的消息格式不符合预期，返回特殊ID '$'")
                return '$'  # 消息格式不符合预期
        except Exception as e:
            logger.error(f"获取流 {self.stream_name} 的最后一个ID时发生错误: {str(e)}")
            return '$'  # 出现错误，返回特殊ID '$'，表示只读取新消息

    async def wait_for_result(self, task_id: str, timeout: float = None, check_interval: float = None) -> TaskiqResult:
        """使用Redis Streams等待任务结果

        Args:
            task_id: 任务ID
            timeout: 超时时间（秒）
            check_interval: 不再使用，保留参数是为了兼容性

        Returns:
            任务结果

        Raises:
            TimeoutError: 等待超时
            Exception: 获取结果时发生错误
        """
        # 首先检查结果是否已经存在
        if await self.is_result_ready(task_id):
            logger.info(f"任务结果已就绪，直接获取: {task_id}")
            return await self.get_result(task_id)

        # 获取当前流的最后一个ID
        last_id = await self.get_last_stream_id()

        try:
            async with calculate_time("wait_for_stream_message",logger=logger):
                # 使用xread阻塞式读取流
                async def wait_for_stream_message():
                    nonlocal last_id  # 声明使用外部的last_id变量
                    while True:
                        try:
                            # 使用XREAD BLOCK命令阻塞式地等待新消息
                            # 这里使用0表示无限期阻塞，直到有新消息
                            logger.info(f"等待流 {self.stream_name} 的新消息，从ID {last_id} 开始")
                            streams = await self.redis.xread(
                                streams={self.stream_name: last_id},
                                count=1,
                                block=0  # 无限期阻塞
                            )
                        except Exception as e:
                            logger.error(f"读取流时发生错误: {str(e)}")
                            # 如果读取出错，等待一小段时间后重试
                            await asyncio.sleep(0.05)
                            continue

                        # 如果收到消息
                        if streams:
                            try:
                                stream_info, messages = streams[0]
                                logger.info(f"收到流 {stream_info.decode('utf-8') if isinstance(stream_info, bytes) else stream_info} 的新消息")

                                for message_id, message in messages:
                                    try:
                                        # 检查消息是否与当前任务相关
                                        message_task_id = message.get(b'task_id')
                                        if message_task_id is not None and message_task_id.decode('utf-8') == task_id:
                                            logger.info(f"收到任务 {task_id} 的结果通知")
                                            return await self.get_result(task_id)
                                    except Exception as e:
                                        logger.error(f"处理消息时发生错误: {str(e)}")

                                    # 更新last_id为最新的消息ID
                                    try:
                                        if isinstance(message_id, bytes):
                                            last_id = message_id.decode('utf-8')
                                        else:
                                            last_id = str(message_id)
                                        logger.info(f"更新last_id为: {last_id}")
                                    except Exception as e:
                                        logger.error(f"更新last_id时发生错误: {str(e)}")
                                        # 如果无法更新last_id，使用特殊ID '$'
                                        last_id = '$'
                            except Exception as e:
                                logger.error(f"处理流消息时发生错误: {str(e)}")
                                # 如果处理出错，使用特殊ID '$'
                                last_id = '$'

                # 设置超时
                if timeout is not None:
                    logger.info(f"设置等待任务 {task_id} 结果的超时时间: {timeout}秒")
                    return await asyncio.wait_for(wait_for_stream_message(), timeout)
                else:
                    logger.info(f"无限期等待任务 {task_id} 的结果")
                    return await wait_for_stream_message()

        except asyncio.TimeoutError:
            logger.warning(f"等待任务 {task_id} 结果超时")
            raise TimeoutError(f"等待任务 {task_id} 结果超时")
        except Exception as e:
            logger.error(f"等待任务 {task_id} 结果时发生错误: {str(e)}")
            raise

    async def shutdown(self) -> None:
        """关闭所有连接

        清理资源并关闭连接。
        """
        # 关闭Redis连接
        await self.redis.close()
        # 关闭Redis连接池
        await self.redis_pool.disconnect()
        await super().shutdown()

    async def set_progress(self, task_id: str, progress: Any) -> None:
        """设置任务进度

        Args:
            task_id: 任务ID
            progress: 任务进度
        """
        # 实现进度跟踪功能
        logger.info(f"设置任务 {task_id} 的进度: {progress}")
        # 目前只是占位符，不实际执行任何操作
        pass

    async def get_progress(self, task_id: str) -> Optional[Any]:
        """获取任务进度

        Args:
            task_id: 任务ID

        Returns:
            任务进度
        """
        # 实现进度跟踪功能
        logger.info(f"获取任务 {task_id} 的进度")
        # 目前只是占位符，不实际执行任何操作
        return None