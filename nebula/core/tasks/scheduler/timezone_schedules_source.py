from taskiq.schedule_sources.label_based import LabelScheduleSource

class TimezoneAwareLabelScheduleSource(LabelScheduleSource):
    def __init__(self, broker, default_timezone="Asia/Shanghai"):
        super().__init__(broker)
        self.default_timezone = default_timezone

    async def startup(self) -> None:
        # 先调用父类的startup方法来解析所有任务的schedule标签
        await super().startup()
        # 然后为所有没有设置时区的cron任务添加默认时区
        for schedule_id, task in self.schedules.items():
            if task.cron and not task.cron_offset:
                task.cron_offset = self.default_timezone