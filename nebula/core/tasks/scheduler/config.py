"""
CLI端的调度器

配置Taskiq Broker实例，作为任务调度的核心组件。
"""

from nebula.core.tasks.broker.config import broker
from taskiq import TaskiqScheduler
from nebula.core.tasks.scheduler.timezone_schedules_source import TimezoneAwareLabelScheduleSource


# 创建TaskiqScheduler实例用于定时任务
scheduler = TaskiqScheduler(
    broker=broker,
    sources=[TimezoneAwareLabelScheduleSource(broker, default_timezone="Asia/Shanghai")],
)