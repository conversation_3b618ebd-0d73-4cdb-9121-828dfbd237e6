"""
Taskiq调度器实现

实现抽象任务调度器接口，使用Taskiq作为底层实现。
"""

import uuid
import logging
import asyncio
from enum import Enum
from typing import Any, Callable, Dict, Optional, List, Union
from taskiq.abc.broker import AsyncBroker
from taskiq.message import BrokerMessage
from taskiq.result import TaskiqResult
from taskiq.task import AsyncTaskiqTask
from nebula.core.infrastructure import singleton
from nebula.core.infrastructure.config import get_settings
from nebula.core.infrastructure.exceptions import NebulaException, TimeoutError
from nebula.core.infrastructure import log_context,calculate_time
from nebula.core.tasks.interface.scheduler import AbstractTaskScheduler
from nebula.core.tasks.interface.task import Task
from nebula.core.tasks.backend import StreamsRedisResultBackend
from nebula.core.repository.taskinfo import TaskType

logger = logging.getLogger(__name__)

@singleton
class TaskiqScheduler(AbstractTaskScheduler):
    """Taskiq调度器实现 (客户端的调度器)

    使用Taskiq作为底层实现，提供任务调度功能。
    """

    def __init__(self, settings: Optional[Any] = None):
        """初始化调度器"""
        self.settings = get_settings()
        self.broker: Optional[AsyncBroker] = None
        self._started = False

        from nebula.core.tasks.broker.config import broker
        self.broker = broker
        logger.info("TaskiqScheduler initialized")

    async def startup(self) -> None:
        """启动调度器

        初始化AsyncTaskiqClient实例。
        """
        if self._started:
            return

        # 初始化客户端
        self.broker = self.broker

        # 启动Broker
        await self.broker.startup()

        self._started = True
        logger.info("客户端Taskiq调度器已启动")

    async def shutdown(self) -> None:
        """关闭调度器

        关闭AsyncTaskiqClient实例。
        """
        if not self._started:
            return

        # 关闭Broker
        await self.broker.shutdown()

        self._started = False
        logger.info("Taskiq调度器已关闭")

    async def schedule_task(self,
                           task_identifier: Callable,
                           *args: Any,
                           **kwargs: Any) -> Task:
        """调度任务

        使用AsyncTaskiqClient发送任务。

        Args:
            task_name: 任务名称
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            任务对象

        Raises:
            NebulaException: 调度器未启动
        """
        if not self._started or not self.broker:
            await self.startup()

        task_name = task_identifier.__name__

        # 从kwargs中提取特殊参数
        task_id = kwargs.pop("task_id", str(uuid.uuid4()))
        labels = kwargs.pop("labels", {})

        # 发送任务
        logger.info(f"调度任务: {task_name}, task_id={task_id}")

        # 处理labels中的枚举类型，将其转换为字符串
        serializable_labels = {}
        for key, value in labels.items():
            # 如果值是枚举类型，转换为字符串
            if isinstance(value, Enum):
                serializable_labels[key] = value.value
            else:
                serializable_labels[key] = value

        # 创建任务消息字典
        # task_message = {
        #     "task_name": task_name,
        #     "task_id": task_id,
        #     "args": args,
        #     "kwargs": kwargs,
        #     "labels": {**serializable_labels}
        # }

        # 将任务消息序列化为bytes
        # import json
        # message_bytes = json.dumps(task_message).encode("utf-8")

        # # 创建BrokerMessage
        # message = BrokerMessage(
        #     task_id=task_id,
        #     task_name=task_name,
        #     message=message_bytes,
        #     labels=serializable_labels
        # )

        # 创建任务记录
        # try:
        #     from nebula.core.service.taskman import TaskService
        #     from nebula.core.infrastructure import provider

        #     task_service = provider.get(TaskService)
        #     if task_service:
        #         # 创建任务参数字典
        #         params = {
        #             "args": args,
        #             "kwargs": kwargs,
        #             "labels": serializable_labels,
        #         }

        #         # 创建任务信息
        #         task_obj = Task(
        #             task_id=task_id,
        #             task_name=task_name,
        #             task_type=TaskType.WORKER.value,
        #             labels=serializable_labels
        #         )
        #         await task_service.create_task_info(task_obj, params)
        #         logger.info(f"已创建任务信息: {task_id}")
        # except Exception as e:
        #     logger.error(f"创建任务信息失败: {str(e)}")

        # 发送任务
        # await self.broker.kick(message)
        message = await task_identifier.kiq(*args, **kwargs)

        # 创建并返回任务对象
        return Task(
            task_id=message.task_id,
            task_name=task_name,
            task_type=TaskType.WORKER.value,
            labels=serializable_labels
        )

    # 记录花费时间
    @log_context
    async def wait_task_result(self,
                              task: Union[Task, str],
                              timeout: int = 30) -> Dict[str, Any]:
        """等待任务结果

        等待任务完成并获取结果。

        Args:
            task: 任务对象或任务ID
            timeout: 超时时间（秒）

        Returns:
            任务结果

        Raises:
            TimeoutError: 等待超时
            NebulaException: 调度器未启动
        """
        if not self._started or not self.broker:
            await self.startup()

        # 获取任务ID
        task_id = task.task_id if isinstance(task, Task) else task

        # 创建AsyncTaskiqTask对象
        taskiq_task = AsyncTaskiqTask(
            task_id=task_id,
            result_backend=self.broker.result_backend
        )

        # 等待结果
        try:
            # 使用StreamsRedisResultBackend的wait_for_result方法
            # 这种方式使用Redis Streams而不是轮询，更高效
            if isinstance(self.broker.result_backend, StreamsRedisResultBackend):
                result = await self.broker.result_backend.wait_for_result(
                    task_id=task_id,
                    timeout=float(timeout)
                )
            else:
                # 兼容其他结果后端
                result = await taskiq_task.wait_result(
                    timeout=float(timeout),
                    check_interval=0.2
                )

            # 处理结果
            if result.is_err:
                return {
                    "status": "error",
                    "error": str(result.error),
                    "task_id": task_id,
                    "task": task if isinstance(task, Task) else Task(task_id=task_id, task_name="unknown")
                }

            return {
                "status": "success",
                "result": result.return_value,
                "task_id": task_id,
                "task": task if isinstance(task, Task) else Task(task_id=task_id, task_name="unknown")
            }

        except Exception as e:
            logger.error(f"等待任务 {task_id} 结果失败: {str(e)}")
            # 返回错误而不是抛出异常
            return {
                "status": "error",
                "error": f"等待任务 {task_id} 结果失败: {str(e)}",
                "task_id": task_id,
                "task": task if isinstance(task, Task) else Task(task_id=task_id, task_name="unknown")
            }

    async def get_task_status(self, task: Union[Task, str]) -> Dict[str, Any]:
        """获取任务状态

        Args:
            task: 任务对象或任务ID

        Returns:
            任务状态信息
        """
        if not self._started or not self.broker:
            await self.startup()

        # 获取任务ID
        task_id = task.task_id if isinstance(task, Task) else task

        # 尝试获取结果，如果没有结果则任务可能仍在运行
        try:
            result = await self.broker.result_backend.get_result(task_id)

            if result is None:
                return {
                    "status": "pending",
                    "task_id": task_id,
                    "task": task if isinstance(task, Task) else Task(task_id=task_id, task_name="unknown")
                }

            if result.is_err:
                return {
                    "status": "error",
                    "error": str(result.error),
                    "task_id": task_id,
                    "task": task if isinstance(task, Task) else Task(task_id=task_id, task_name="unknown")
                }

            return {
                "status": "success",
                "result": result.return_value,
                "task_id": task_id,
                "task": task if isinstance(task, Task) else Task(task_id=task_id, task_name="unknown")
            }

        except Exception as e:
            logger.error(f"获取任务 {task_id} 状态失败: {str(e)}")
            return {
                "status": "unknown",
                "task_id": task_id,
                "task": task if isinstance(task, Task) else Task(task_id=task_id, task_name="unknown")
            }

    async def cancel_task(self, task: Union[Task, str]) -> bool:
        """取消任务

        目前Taskiq不直接支持取消任务，此方法为占位符。

        Args:
            task: 任务对象或任务ID

        Returns:
            是否成功取消
        """
        # 获取任务ID
        task_id = task.task_id if isinstance(task, Task) else task

        logger.warning(f"Taskiq不支持直接取消任务: {task_id}")
        return False
