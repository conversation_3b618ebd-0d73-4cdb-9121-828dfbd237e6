"""
抽象任务调度器接口

定义任务调度的核心接口，作为API层和Worker层之间的桥梁。
"""

from abc import ABC, abstractmethod
from typing import Any, Callable, Dict, Union
from nebula.core.tasks.interface.task import Task

class AbstractTaskScheduler(ABC):
    """抽象任务调度器接口
    
    定义任务调度的核心方法，包括任务发送、结果获取等。
    作为API层和Worker层之间的桥梁，确保两者解耦。
    """
    
    @abstractmethod
    async def startup(self) -> None:
        """启动调度器
        
        在应用启动时调用，用于初始化连接等资源。
        """
        pass
        
    @abstractmethod
    async def shutdown(self) -> None:
        """关闭调度器
        
        在应用关闭时调用，用于释放资源。
        """
        pass
        
    @abstractmethod
    async def schedule_task(self,
                           task_identifier: Callable,
                           *args: Any,
                           **kwargs: Any) -> Task:
        """调度任务
        
        Args:
            task_name: 任务名称
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            任务对象
        """
        pass
        
    @abstractmethod
    async def wait_task_result(self,
                              task: Union[Task, str],
                              timeout: int = 30) -> Dict[str, Any]:
        """等待任务结果
        
        Args:
            task: 任务对象或任务ID
            timeout: 超时时间（秒）
            
        Returns:
            任务结果
            
        Raises:
            TimeoutError: 等待超时
        """
        pass
        
    @abstractmethod
    async def get_task_status(self, task: Union[Task, str]) -> Dict[str, Any]:
        """获取任务状态
        
        Args:
            task: 任务对象或任务ID
            
        Returns:
            任务状态信息
        """
        pass
        
    @abstractmethod
    async def cancel_task(self, task: Union[Task, str]) -> bool:
        """取消任务
        
        Args:
            task: 任务对象或任务ID
            
        Returns:
            是否成功取消
        """
        pass
