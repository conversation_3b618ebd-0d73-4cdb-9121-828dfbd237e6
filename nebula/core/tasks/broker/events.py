"""
Taskiq Broker事件处理

处理Broker的事件，如启动、关闭等。
"""

import aiohttp
import logging
from typing import Optional, List
from taskiq.events import TaskiqEvents
from nebula.core.tasks.broker.config import broker, CustomJSONFormatter
from nebula.core.tasks.middleware import SchedulerMiddleware, RetryMiddleware, LoggingMiddleware, NebulaTaskMiddleware, ChainMiddleware


logger = logging.getLogger(__name__)

# 加载应用上下文
async def load_context(components: Optional[List[str]] = ['*']):
    """加载应用上下文"""
    from nebula.core.context import init_application
    app_ctx = await init_application(
        app_name="Nebula Worker",
        debug=False,
        components=components,
        reg_signal=False
    )
    return app_ctx

@broker.on_event(TaskiqEvents.CLIENT_STARTUP)
async def on_broker_startup(state):
    """Broker启动事件处理

    在Broker启动时触发，用于初始化中间件和上下文。

    Args:
        state: Broker状态
    """
    global broker

    broker = broker.with_middlewares(SchedulerMiddleware())

    if broker.is_scheduler_process:
        logger.info(f'来自调度器进程: 加载将任务写入数据库的必要组件')
        await load_context(['database','taskman','cache'])

        


@broker.on_event(TaskiqEvents.WORKER_STARTUP)
async def on_worker_startup(state):
    """Worker启动事件处理

    在Worker进程启动时触发，用于初始化中间件和上下文。

    Args:
        state: Worker状态
    """
    # worker进程才会触发

    # 添加中间件
    global broker

    from nebula.core.context.app_context import ApplicationContext
    broker.add_dependency_context({ApplicationContext: await load_context()})

    from nebula.core.tasks.logging import inject_task_log_format
    inject_task_log_format()

    # 配置中间件 (添加顺序就是执行顺序,注意时序问题)
    broker = broker.with_formatter(CustomJSONFormatter())
    broker = broker.with_middlewares(LoggingMiddleware())
    broker = broker.with_middlewares(ChainMiddleware())
    broker = broker.with_middlewares(
        RetryMiddleware(
            retry_intervals=[5, 30, 120, 180],  # 5秒, 30秒, 2分钟, 3分钟
            retryable_exceptions=[
                ConnectionError,  # 网络连接错误可以重试
                TimeoutError,     # 超时错误可以重试
                Exception,       # 其他异常也可以重试
                aiohttp.client_exceptions.ClientPayloadError,
            ],
            default_retry_label=True,
            use_jitter=True,
        )
    )
    broker = broker.with_middlewares(NebulaTaskMiddleware())

    logger.info("Worker startup...")
    logger.info("注册任务:")
    for task_name, t in broker.get_all_tasks().items():
        logger.info(f"{task_name}")
    print(f"loaded middlewares: {len(broker.middlewares)}")  
    for i, middleware in enumerate(broker.middlewares):  
        print(f"{i+1}. {type(middleware).__name__}")


@broker.on_event(TaskiqEvents.WORKER_SHUTDOWN)
async def on_worker_shutdown(state):
    """Worker关闭事件处理

    在Worker进程关闭时触发，用于清理资源。

    Args:
        state: Worker状态
    """
    logger.info("Worker shutdown...")
