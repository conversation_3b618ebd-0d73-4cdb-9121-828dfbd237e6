"""
Taskiq Broker配置

配置Taskiq Broker实例，作为任务调度的核心组件。
"""
import logging
from typing import Optional
from taskiq_aio_pika import AioPikaBroker
from taskiq.message import TaskiqMessage
from taskiq.formatters.json_formatter import J<PERSON>NFormatter
from taskiq import TaskiqScheduler
from nebula.core.infrastructure.config import get_settings
from nebula.core.tasks.backend import StreamsRedisResultBackend



logger = logging.getLogger(__name__)
# 移除调试打印
settings = get_settings()

class CustomJSONFormatter(JSONFormatter):
    def loads(self, message: bytes) -> TaskiqMessage:
        """
        Custom loads method that handles task_id in labels.
        """
        try:
            return super().loads(message)
        except Exception as e:
            logger.error(f"JSONDecodeError: {str(e)}", exc_info=True)
            raise e

_broker_instance = None
def _get_broker():
    """获取Taskiq Broker实例

    根据配置创建适当的Broker实例。

    Returns:
        Broker实例
    """
    global _broker_instance
    if _broker_instance:
        return _broker_instance

    import os
    pid = os.getpid()
    logger.info(f"初始化Broker实例({pid})")

    # 使用AioPikaBroker作为Broker
    broker_url = settings.taskiq.broker_url
    result_backend_url = settings.taskiq.result_backend_url
    queue_name = settings.taskiq.queue_name
    routing_key = settings.taskiq.routing_key

    logger.info(f"使用AioPikaBroker: {broker_url}")
    # 使用支持Redis Streams的结果后端
    result_backend = StreamsRedisResultBackend(
        redis_url=result_backend_url
    )

    # 创建Broker
    broker = AioPikaBroker(
        broker_url,
        queue_name=queue_name,
        routing_key=routing_key
    ).with_result_backend(result_backend)

    _broker_instance = broker
    return _broker_instance

broker = _get_broker()



# 事件处理将在broker/__init__.py中导入