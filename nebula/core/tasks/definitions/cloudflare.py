"""
Cloudflare任务定义

定义Cloudflare相关的任务函数。
"""
import logging
from typing import Dict, Any, Optional
from taskiq import Context, TaskiqDepends

from nebula.core.repository.cloudflare import Entity
from nebula.core.service.cloudflare import CloudflareService
from nebula.core.infrastructure import exceptions
from nebula.core.tasks.broker.config import broker

logger = logging.getLogger(__name__)

# 使用provider中的函数获取Cloudflare服务实例
from nebula.core.service.provider import get_cloudflare_service

@broker.task(retry_on_error=True, max_retries=3, description="保存Cloudflare配置")
async def save_config(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """保存Cloudflare配置

    Args:
        metadata: 配置数据
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始保存配置: {metadata}")

    # 获取服务实例
    cloudflare_service = get_cloudflare_service()

    # 创建账户对象
    account = Entity.Account(**metadata)

    # 保存配置
    await cloudflare_service.set_account(account)

    return {
        "status": True,
        "message": "保存配置完成"
    }

@broker.task(retry_on_error=True, max_retries=3, description="同步域名列表")
async def sync_domains(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """同步域名列表

    Args:
        metadata: 配置数据
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始同步域名: {metadata}")

    # 获取服务实例
    cloudflare_service = get_cloudflare_service()

    # 获取账户
    cloudflare = await cloudflare_service.get_account()

    # 同步域名列表
    await cloudflare_service.sync_domain_list(cloudflare)

    return {
        "status": True,
        "message": "同步域名完成"
    }

@broker.task(retry_on_error=True, max_retries=3, description='创建DNS记录 {record[sub]}.{domain_id} -> {record[content]}')
async def create_dns_record(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """创建DNS记录

    Args:
        metadata: 包含账户ID、域名ID和记录数据的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始创建DNS记录: {metadata}")

    # 获取服务实例
    cloudflare_service = get_cloudflare_service()

    # 提取参数
    account_id = metadata["account_id"]
    domain_id = metadata["domain_id"]
    record = Entity.DnsRecord.from_dict(metadata["record"])

    # 获取账户
    account = await cloudflare_service.get_account(account_id)

    # 创建DNS记录
    result = await cloudflare_service.create_dns_record(account, domain_id, record)

    # 添加通知标签
    context.message.labels["notification"] = {
        "persistent": True,
        "title": f"创建 {record.type} 记录 {record.sub}.{domain_id} 指向 {record.content}",
        "source": "Cloudflare"
    }

    return {
        "status": True,
        "message": "创建DNS记录完成",
        "record": result.to_dict() if result else None
    }

@broker.task(retry_on_error=True, max_retries=3, description='更新DNS记录 {record[sub]}.{domain_id} -> {record[content]}')
async def update_dns_record(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """更新DNS记录

    Args:
        metadata: 包含账户ID、域名ID、记录ID和记录数据的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始更新DNS记录: {metadata}")

    # 获取服务实例
    cloudflare_service = get_cloudflare_service()

    # 提取参数
    account_id = metadata["account_id"]
    domain_id = metadata["domain_id"]
    record = Entity.DnsRecord.from_dict(metadata["record"])
    record.record_id = metadata["record_id"]

    # 获取账户
    account = await cloudflare_service.get_account(account_id)

    # 更新DNS记录
    result = await cloudflare_service.update_dns_record(account, domain_id, record)

    # 添加通知标签
    context.message.labels["notification"] = {
        "persistent": True,
        "title": f"修改 {record.type} 记录 {record.sub}.{domain_id} 指向 {record.content}",
        "source": "Cloudflare"
    }

    return {
        "status": True,
        "message": "更新DNS记录完成",
        "record": result.to_dict() if result else None
    }

@broker.task(retry_on_error=True, max_retries=3, description='删除DNS记录 {domain_id} -> {record_id}')
async def delete_dns_record(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """删除DNS记录

    Args:
        metadata: 包含账户ID、域名ID和记录ID的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始删除DNS记录: {metadata}")

    # 获取服务实例
    cloudflare_service = get_cloudflare_service()

    # 提取参数
    account_id = metadata["account_id"]
    domain_id = metadata["domain_id"]
    record_id = metadata["record_id"]

    # 获取账户
    account = await cloudflare_service.get_account(account_id)

    # 删除DNS记录
    result = await cloudflare_service.delete_dns_record(account, domain_id, record_id)

    return {
        "status": True,
        "message": "删除DNS记录完成",
        "record": result
    }

@broker.task(retry_on_error=True, max_retries=3, description="验证API密钥 {account_id} : {api_key}")
async def validate_api_key(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """验证API密钥

    Args:
        metadata: 包含账户ID和API密钥的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始验证API密钥: {metadata}")

    # 获取服务实例
    cloudflare_service = get_cloudflare_service()

    # 创建临时账户对象
    account = Entity.Account(
        account_id=metadata.get("account_id"),
        api_key=metadata.get("api_key")
    )

    # 验证API配置
    await cloudflare_service.validate_api_config(account)

    return {
        "status": True,
        "message": "验证API密钥完成"
    }
