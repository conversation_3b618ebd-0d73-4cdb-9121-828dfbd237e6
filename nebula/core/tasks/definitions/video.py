"""
Video任务定义

定义Video相关的任务函数。
"""
import logging
from typing import Dict, Any, Optional, List
from taskiq import Context, TaskiqDepends

from nebula.core.service.video import VideoService
from nebula.core.repository.video import Entity
from nebula.core.infrastructure import exceptions
from nebula.core.tasks.broker.config import broker
from nebula.core.service.provider import get_video_service
from nebula.core.tasks.chain import TaskChainComposer

logger = logging.getLogger(__name__)

@broker.task(retry_on_error=True, max_retries=3, description="创建视频: {play_url}")
async def create_video(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """创建视频

    Args:
        metadata: 视频数据
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始创建视频: {metadata}")

    # 获取服务实例
    video_service = get_video_service()

    # 提取参数
    title = metadata.get("title", "")
    desc = metadata.get("desc", "")
    play_url = metadata.get("play_url", "")

    # 添加通知标签
    context.message.labels["notification"] = {
        "persistent": True,
        "title": f"上传视频 来自 {play_url}",
        "source": "Video"
    }

    # 创建视频
    await video_service.create_from_file_url(play_url, title, desc)

    return {
        "status": True,
        "message": "创建视频并分析内容完成",
        "play_url": play_url,
        "title": title,
        "desc": desc
    }

@broker.task(retry_on_error=True, max_retries=3, description="更新视频: {id}")
async def update_video(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """更新视频

    Args:
        metadata: 包含视频ID和更新数据的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始更新视频: {metadata}")

    # 获取服务实例
    video_service = get_video_service()

    # 提取参数
    video_id = metadata.pop("id")

    # 更新视频
    await video_service.update_video(video_id, metadata)

    return {
        "status": True,
        "message": "更新视频完成",
        "id": video_id,
        "video_id": video_id,
        "updated_fields": list(metadata.keys())
    }

@broker.task(retry_on_error=True, max_retries=3, description="删除视频: {id}")
async def delete_video(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """删除视频

    Args:
        metadata: 包含视频ID的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始删除视频: {metadata}")

    # 获取服务实例
    video_service = get_video_service()

    # 提取参数
    video_id = metadata.get("id")

    # 删除视频
    await video_service.delete_video(video_id)

    return {
        "status": True,
        "message": "删除视频完成",
        "id": video_id,
        "video_id": video_id,
        "deleted": True
    }

@broker.task(retry_on_error=True, max_retries=3, description="为视频添加标签: {id} -> {tags}")
async def add_tags(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """添加标签

    Args:
        metadata: 包含视频ID和标签的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始添加标签: {metadata}")

    # 获取服务实例
    video_service = get_video_service()

    # 提取参数
    video_id = metadata.get("id")
    tags = metadata.get("tags", [])

    # 添加标签
    await video_service.add_tags(video_id, tags)

    return {
        "status": True,
        "message": "添加标签完成",
        "id": video_id,
        "video_id": video_id,
        "added_tags": tags
    }

@broker.task(retry_on_error=True, max_retries=3, description="移除视频标签: {id} -> {tags}")
async def remove_tags(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """移除标签

    Args:
        metadata: 包含视频ID和标签的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始移除标签: {metadata}")

    # 获取服务实例
    video_service = get_video_service()

    # 提取参数
    video_id = metadata.get("id")
    tags = metadata.get("tags", [])

    # 移除标签
    await video_service.remove_tags(video_id, tags)

    return {
        "status": True,
        "message": "移除标签完成",
        "id": video_id,
        "video_id": video_id,
        "removed_tags": tags
    }

@broker.task(retry_on_error=True, max_retries=3, description="更新字幕: {id}")
async def update_subtitles(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """更新字幕

    Args:
        metadata: 包含视频ID和字幕数据的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始更新字幕: {metadata}")

    # 获取服务实例
    video_service = get_video_service()

    # 提取参数
    video_id = metadata.get("id")
    subtitles = metadata.get("subtitles", "")
    chunks = metadata.get("chunks")

    # 更新字幕
    await video_service.update_subtitles(video_id, subtitles, chunks)

    return {
        "status": True,
        "message": "更新字幕完成",
        "id": video_id,
        "video_id": video_id,
        "subtitles_updated": True,
        "chunks_count": len(chunks) if chunks else 0
    }

@broker.task(retry_on_error=True, max_retries=3, description="分析视频内容: {video_id}")
async def analyze_content(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """分析视频内容

    Args:
        metadata: 包含视频ID的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始分析视频内容: {metadata}")

    # 获取服务实例
    video_service = get_video_service()

    # 提取参数
    video_id = metadata.get("video_id")

    # 添加通知标签
    context.message.labels["notification"] = {
        "persistent": True,
        "title": f"视频 {video_id} 调用Gemini视频理解生成属性",
        "source": "Video"
    }

    # 分析内容
    await video_service.update_content_analysis(video_id)

    return {
        "status": True,
        "message": "内容分析完成",
        "video_id": video_id,
        "analysis_completed": True
    }

@broker.task(retry_on_error=True, max_retries=3, description="提取分享信息: {share_text}")
async def extract_share_info(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """提取分享信息

    Args:
        metadata: 包含分享文本的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始提取分享信息: {metadata}")

    # 获取服务实例
    video_service = get_video_service()

    # 提取参数
    share_text = metadata.get("share_text", "")

    # 提取视频信息
    video_info = await video_service.extract_share_info(share_text)

    return {
        "status": True,
        "message": "视频信息提取成功",
        **video_info
    }

@broker.task(retry_on_error=True, max_retries=3, description="从分享链接导入视频: {watch_url}")
async def import_from_share(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """从分享链接导入视频

    Args:
        metadata: 包含视频信息的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始从分享链接导入视频: {metadata}")

    # 获取服务实例
    video_service = get_video_service()

    # 提取参数
    title = metadata.get("title", "")
    desc = metadata.get("desc", "")
    cover_url = metadata.get("cover_url", "")
    player_url = metadata.get("player_url", "")
    video_id = metadata.get("video_id", "")
    watch_url = metadata.get("watch_url", "")
    duration = metadata.get("duration", 0)
    from_platform = metadata.get("from_platform", "")

    # 添加通知标签
    context.message.labels["notification"] = {
        "persistent": True,
        "title": f"从[{from_platform}]分享链接[{watch_url}]导入视频",
        "source": "Video"
    }

    # 导入视频
    video = await video_service.import_from_share(
        title=title,
        desc=desc,
        cover_url=cover_url,
        player_url=player_url,
        video_id=video_id,
        watch_url=watch_url,
        duration=duration,
        from_platform=from_platform
    )

    return {
        "status": True,
        "message": "视频导入成功",
        "video_id": video.id,
        "title": video.title,
        "video_path": video.video_path,
        "cover_path": video.cover_path
    }

# 显式定义任务函数，确保Taskiq能够发现
@broker.task(
    retry_on_error=True,
    max_retries=3,
    description="从分享链接创建视频: {share_text}"
)
async def create_video_from(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """从分享链接创建视频的完整流程任务链

    执行步骤：
    1. extract_share_info - 提取分享信息
    2. import_from_share - 导入视频
    3. analyze_content - 分析内容

    支持断点续传，重试时从失败点继续执行。
    """
    return await (
        TaskChainComposer("create_video_from_share", "从分享链接创建视频的完整流程")
        .add_task(extract_share_info, description="提取分享信息")
        .add_task(import_from_share, description="导入视频")
        .add_task(analyze_content, description="理解视频内容,更新视频属性")
    ).execute(metadata, context)