"""
Telegram任务定义

定义Telegram相关的任务函数。
"""

import logging
from typing import Dict, Any, Optional
from taskiq import Context, TaskiqDepends

from nebula.core.service.telegram import TelegramService
from nebula.core.infrastructure import exceptions
from nebula.core.tasks.broker.config import broker
from nebula.core.service.provider import get_telegram_service

logger = logging.getLogger(__name__)

@broker.task(retry_on_error=True, max_retries=3, description="Telegram任务: 发送文本消息")
async def send_message(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """发送文本消息

    Args:
        metadata: 包含聊天ID和消息文本的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始发送文本消息: {metadata}")

    # 获取服务实例
    telegram_service = get_telegram_service()

    # 提取参数
    chat_id = metadata.get("chat_id")
    text = metadata.get("text")

    # 验证参数
    if not chat_id or not text:
        raise exceptions.NebulaException("缺少必要参数: chat_id 或 text")

    # 发送消息
    await telegram_service.bot.send_message(
        chat_id=chat_id,
        text=text
    )

    return {
        "status": True,
        "message": "消息发送成功"
    }

@broker.task(retry_on_error=True, max_retries=3, description="Telegram任务: 发送图片")
async def send_photo(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """发送图片

    Args:
        metadata: 包含聊天ID、图片和可选标题的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始发送图片: {metadata}")

    # 获取服务实例
    telegram_service = get_telegram_service()

    # 提取参数
    chat_id = metadata.get("chat_id")
    photo = metadata.get("photo")
    caption = metadata.get("caption")

    # 验证参数
    if not chat_id or not photo:
        raise exceptions.NebulaException("缺少必要参数: chat_id 或 photo")

    # 发送图片
    await telegram_service.bot.send_photo(
        chat_id=chat_id,
        photo=photo,
        caption=caption
    )

    return {
        "status": True,
        "message": "图片发送成功"
    }

@broker.task(retry_on_error=True, max_retries=3, description="Telegram任务: 发送文档")
async def send_document(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """发送文档

    Args:
        metadata: 包含聊天ID、文档和可选标题的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始发送文档: {metadata}")

    # 获取服务实例
    telegram_service = get_telegram_service()

    # 提取参数
    chat_id = metadata.get("chat_id")
    document = metadata.get("document")
    caption = metadata.get("caption")

    # 验证参数
    if not chat_id or not document:
        raise exceptions.NebulaException("缺少必要参数: chat_id 或 document")

    # 发送文档
    await telegram_service.bot.send_document(
        chat_id=chat_id,
        document=document,
        caption=caption
    )

    return {
        "status": True,
        "message": "文档发送成功"
    }

@broker.task(retry_on_error=True, max_retries=3, description="处理Telegram事件: {event_type}")
async def process_event(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """处理Telegram事件

    Args:
        metadata: 包含事件类型和更新数据的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始处理Telegram事件: {metadata}")

    # 获取服务实例
    telegram_service = get_telegram_service()

    # 提取参数
    event_type = metadata.get("event_type")
    update_data = metadata.get("update")

    # 验证参数
    if not event_type or not update_data:
        raise exceptions.NebulaException("缺少必要参数: event_type 或 update")

    # 解析更新数据
    event, _ = telegram_service.parse_update(update_data)

    # 检查是否需要发送通知
    if event.notification_needed:
        # 添加通知标签
        context.message.labels["notification"] = {
            "persistent": True,
            "title": event.notification_title,
            "source": "Telegram"
        }

    # 处理事件
    await telegram_service.process_event(event)

    return {
        "status": True,
        "message": f"事件 {event_type} 处理成功"
    }
