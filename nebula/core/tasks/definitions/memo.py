"""
Memo任务定义

定义Memo相关的任务函数。
"""
print ("*"*100)
print ("tasks definitions memo load...")
print ("*"*100)
import logging
from typing import Dict, Any, Optional
from taskiq import Context, TaskiqDepends
from nebula.core.tasks.broker.config import broker
from nebula.core.repository.memo import Entity
from nebula.core.service.provider import get_memo_service
from nebula.core.infrastructure import exceptions, provider

logger = logging.getLogger(__name__)


@broker.task(retry_on_error=True, max_retries=3, description="创建文本备忘录")
async def create_text_memo(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """创建文本备忘录

    Args:
        metadata: 备忘录数据
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始创建文本备忘录: {metadata}")

    # 获取服务实例
    memo_service = get_memo_service()

    # 创建备忘录对象
    memo = Entity.TextMemo(**metadata)

    # 创建备忘录
    result = await memo_service.create_memo(memo)

    return {
        "status": True,
        "message": "创建文本备忘录完成",
        "memo": result.to_dict() if result else None
    }

@broker.task(retry_on_error=True, max_retries=3, description="创建图片备忘录")
async def create_image_memo(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """创建图片备忘录

    Args:
        metadata: 备忘录数据
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始创建图片备忘录: {metadata}")

    # 获取服务实例
    memo_service = get_memo_service()

    # 创建备忘录对象
    memo = Entity.ImageMemo(**metadata)

    # 创建备忘录
    result = await memo_service.create_memo(memo)

    return {
        "status": True,
        "message": "创建图片备忘录完成",
        "memo": result.to_dict() if result else None
    }

@broker.task(retry_on_error=True, max_retries=3, description="创建文件备忘录")
async def create_file_memo(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """创建文件备忘录

    Args:
        metadata: 备忘录数据
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始创建文件备忘录: {metadata}")

    # 获取服务实例
    memo_service = get_memo_service()

    # 创建备忘录对象
    memo = Entity.FileMemo(**metadata)

    # 创建备忘录
    result = await memo_service.create_memo(memo)

    return {
        "status": True,
        "message": "创建文件备忘录完成",
        "memo": result.to_dict() if result else None
    }

@broker.task(retry_on_error=True, max_retries=3, description="创建分享备忘录")
async def create_share_memo(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """创建分享备忘录

    Args:
        metadata: 备忘录数据
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始创建分享备忘录: {metadata}")

    # 获取服务实例
    memo_service = get_memo_service()

    # 创建备忘录对象
    memo = Entity.ShareMemo(**metadata)

    # 创建备忘录
    result = await memo_service.create_memo(memo)

    return {
        "status": True,
        "message": "创建分享备忘录完成",
        "memo": result.to_dict() if result else None
    }

@broker.task(retry_on_error=True, max_retries=3, description="更新备忘录 {id} 的内容")
async def update_memo(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """更新备忘录

    Args:
        metadata: 包含备忘录ID和更新数据的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始更新备忘录: {metadata}")

    # 获取服务实例
    memo_service = get_memo_service()

    # 提取参数
    memo_id = metadata.pop("id")

    # 更新备忘录
    result = await memo_service.update_memo(memo_id, metadata)

    return {
        "status": True,
        "message": "更新备忘录完成",
        "memo": result.to_dict() if result else None
    }

@broker.task(retry_on_error=True, max_retries=3, description="删除备忘录 {id}")
async def delete_memo(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """删除备忘录

    Args:
        metadata: 包含备忘录ID的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始删除备忘录: {metadata}")

    # 获取服务实例
    memo_service = get_memo_service()

    # 提取参数
    memo_id = metadata["id"]

    # 删除备忘录
    await memo_service.delete_memo(memo_id)

    return {
        "status": True,
        "message": "删除备忘录完成"
    }

@broker.task(retry_on_error=True, description="分析备忘录 {id} 的内容")
async def analyze_content(metadata: Dict[str, Any], context: Context = TaskiqDepends()) -> Dict[str, Any]:
    """分析备忘录内容

    Args:
        metadata: 包含备忘录ID的字典
        context: 任务上下文

    Returns:
        处理结果
    """
    logger.info(f"开始分析备忘录内容: {metadata}")

    # 获取服务实例
    memo_service = get_memo_service()

    # 提取参数
    memo_id = metadata["id"]

    # 添加通知标签
    context.message.labels["notification"] = {
        "persistent": True,
        "title": f"备忘录 {memo_id} 调用Gemini内容理解生成属性",
        "source": "Memo",
    }

    # 分析内容
    updated_memo = await memo_service.update_content_analysis(memo_id)

    return {
        "status": True,
        "message": "内容分析完成",
        "memo": updated_memo.to_dict() if updated_memo else None
    }
