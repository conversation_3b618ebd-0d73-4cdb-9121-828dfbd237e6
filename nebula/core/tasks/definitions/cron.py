"""
Cron任务定义

定义Cron相关的任务函数。
"""
import logging
from typing import Dict, Any, Optional, List
from taskiq import Context, TaskiqDepends

from nebula.core.repository.cloudflare import Entity
from nebula.core.service.cloudflare import CloudflareService
from nebula.core.infrastructure import exceptions
from nebula.core.tasks.broker.config import broker

logger = logging.getLogger(__name__)

# 使用provider中的函数获取Cloudflare服务实例
from nebula.core.service.provider import get_misc_service


@broker.task(schedule=[{
    'cron': '0 5 * * *'
}], description="刷新潮汐数据", retry_on_error=False, max_retries=3)
  # 每分钟运行一次
async def fetch_tide():
    misc_service = get_misc_service()
    await misc_service.get_tide_data()
    logger.info("刷新潮汐数据完成")
    return {
        "status": True,
        "message": "刷新完成"
    }



# @broker.task(schedule=[{
#     'cron': '*/5 * * * *'
# }], description="测试计划任务", retry_on_error=False, max_retries=3)
#   # 每分钟运行一次
# async def test_scheduler_task():
#     logger.info("测试计划任务")
#     return {
#         "status": True,
#         "message": "ok"
#     }


# @broker.task(schedule=[{
#     'cron': '*/5 * * * *'  # 每5分钟执行一次
# }], description="定时任务测试 - 每5分钟", retry_on_error=False, max_retries=3)
# async def test_interval_task_5min():
#     logger.info("执行定时任务 - 每5分钟执行一次")
#     return {
#         "status": True,
#         "message": "每5分钟任务执行完成"
#     }

@broker.task(schedule=[{
    'cron': '5 */2 * * *'  # 每2小时执行一次
}], description="定时任务测试 - 每2小时", retry_on_error=False, max_retries=3)
async def test_interval_task_2hour():
    logger.info("执行定时任务 - 每2小时执行一次")
    return {
        "status": True,
        "message": "每2小时任务执行完成"
    }

@broker.task(schedule=[{
    'cron': '30 */3 * * *'  # 每3小时第30分钟执行
}], description="定时任务测试 - 每3小时第30分钟", retry_on_error=False, max_retries=3)
async def test_interval_task_3hour_30min():
    logger.info("执行定时任务 - 每3小时第30分钟执行一次")
    return {
        "status": True,
        "message": "每3小时第30分钟任务执行完成"
    }

@broker.task(schedule=[{
    'cron': '0 0 */2 * *'  # 每2天0:00执行
}], description="定时任务测试 - 每2天0:00", retry_on_error=False, max_retries=3)
async def test_interval_task_2day():
    logger.info("执行定时任务 - 每2天0:00执行一次")
    return {
        "status": True,
        "message": "每2天0:00任务执行完成"
    }

@broker.task(schedule=[{
    'cron': '28 10 * * *'  # 每天10:15执行
}], description="定时任务测试 - 每天10:15", retry_on_error=False, max_retries=3)
async def test_interval_task_daily_1015():
    logger.info("执行定时任务 - 每天10:15执行一次")
    return {
        "status": True,
        "message": "每天10:15任务执行完成"
    }

@broker.task(schedule=[{
    'cron': '0 9 * * 1'  # 每周一9:00执行
}], description="定时任务测试 - 每周一9:00", retry_on_error=False, max_retries=3)
async def test_interval_task_weekly_monday():
    logger.info("执行定时任务 - 每周一9:00执行一次")
    return {
        "status": True,
        "message": "每周一9:00任务执行完成"
    }

@broker.task(schedule=[{
    'cron': '0 9 * * 1,3,5'  # 每周一、三、五9:00执行
}], description="定时任务测试 - 每周一三五9:00", retry_on_error=False, max_retries=3)
async def test_interval_task_weekly_135():
    logger.info("执行定时任务 - 每周一、三、五9:00执行一次")
    return {
        "status": True,
        "message": "每周一、三、五9:00任务执行完成"
    }

@broker.task(schedule=[{
    'cron': '0 0 1 * *'  # 每月1日0:00执行
}], description="定时任务测试 - 每月1日0:00", retry_on_error=False, max_retries=3)
async def test_interval_task_monthly():
    logger.info("执行定时任务 - 每月1日0:00执行一次")
    return {
        "status": True,
        "message": "每月1日0:00任务执行完成"
    }

@broker.task(schedule=[{
    'cron': '57 13 * * *'  # 每天13:57执行
}], description="定时任务测试 - 每天13:57", retry_on_error=False, max_retries=3)
async def test_interval_task_daily_1357():
    logger.info("执行定时任务 - 每天13:57执行一次")
    return {
        "status": True,
        "message": "每天13:57任务执行完成"
    }


@broker.task(schedule=[{
    'cron': '51 */2 * * *'  # 每小时第30分钟执行
}], description="定时任务测试 ", retry_on_error=False, max_retries=3)
async def test_interval_task_hourly_30min():
    logger.info("执行定时任务 - 每小时第30分钟执行一次")
    return {
        "status": True,
        "message": "每小时第30分钟任务执行完成"
    }
