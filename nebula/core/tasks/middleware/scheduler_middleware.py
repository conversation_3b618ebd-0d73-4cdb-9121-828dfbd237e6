"""
Nebula任务中间件

处理任务执行前后的逻辑，包括日志记录、通知发送等。
"""

import logging
import time
from typing import Any, Dict, Type
from taskiq import TaskiqMessage, TaskiqResult, TaskiqMiddleware
from nebula.core.tasks.interface.task import Task
from nebula.core.service.taskman import TaskService
from nebula.core.repository.taskinfo import Entity as TaskEntity
from nebula.core.infrastructure import provider

logger = logging.getLogger(__name__)

class SchedulerMiddleware(TaskiqMiddleware):
    """Nebula任务中间件

    处理任务执行前后的逻辑，包括日志记录、通知发送等。
    """

    def __init__(self):

        logger.info("SchedulerMiddleware initialized")

    async def pre_send(self, message):
        """任务发送前的处理

        Args:
            message: 任务消息

        Returns:

        """

        # 创建任务记录
        try:
            task_service = provider.get(TaskService)
            if task_service:
                # 创建任务参数字典
                params = {
                    "args": message.args,
                    "kwargs": message.kwargs,
                    "labels": message.labels,
                }

                task_type = TaskEntity.TaskType.SCHEDULER.value if message.labels.get("schedule_id") else TaskEntity.TaskType.WORKER.value

                # 创建任务信息
                task_obj = Task(
                    task_id=message.task_id,
                    task_name=message.task_name,
                    task_type=task_type,
                    labels=message.labels
                )
                await task_service.create_task_info(task_obj, params)
                logger.info(f"已创建任务信息: {message.task_id}")
        except Exception as e:
            logger.error(f"创建任务信息失败: {str(e)}")
        
        return message

