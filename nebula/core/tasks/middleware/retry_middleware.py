import datetime
from typing import List, Optional, Any, Type  
from taskiq import SmartRetryMiddleware, ScheduleSource  
from taskiq.message import TaskiqMessage
from taskiq.exceptions import NoResultError
from taskiq.kicker import AsyncKicker
from taskiq.result import TaskiqResult
from nebula.core.protocol import ResponseMode
from nebula.core.infrastructure.exceptions import NebulaException
from nebula.core.infrastructure.logging import get_logger

logger = get_logger(__name__)
  
class RetryMiddleware(SmartRetryMiddleware):  
    """支持特定重试间隔和异常过滤的中间件"""  
      
    def __init__(  
        self,  
        max_retries: Optional[int] = None,
        retry_intervals: List[int] = [5, 30, 120, 180],  
        retryable_exceptions: List[Type[Exception]] = [ConnectionError, TimeoutError],  
        default_retry_label: bool = False,  
        no_result_on_retry: bool = False,  
        use_jitter: bool = False,  
        schedule_source: Optional[ScheduleSource] = None,  
    ) -> None:  
        super().__init__(  
            default_retry_count=max_retries or len(retry_intervals)  ,  
            default_retry_label=default_retry_label,  
            no_result_on_retry=no_result_on_retry,  
            use_jitter=use_jitter,  
            use_delay_exponent=False,  
            schedule_source=schedule_source,  
        )  
        self.retry_intervals = retry_intervals  
        self.retryable_exceptions = retryable_exceptions
          

    def __repr__(self):
        return "<RetryMiddleware>"

    def make_delay(self, message: TaskiqMessage, retries: int) -> float:  
        """使用特定的重试间隔"""  
        interval_index = retries - 1  
          
        if 0 <= interval_index < len(self.retry_intervals):  
            delay = self.retry_intervals[interval_index]  
        else:  
            delay = super().make_delay(message, retries)  
              
        if self.use_jitter:  
            import random  
            delay += random.random()  
              
        return delay
    
    def is_retry_on_error(self, message: TaskiqMessage) -> bool:
        """
        Check if retry is enabled for this task.

        Looks for `retry_on_error` label, falls back to default.

        :param message: Original task message.
        :return: True if should retry on error.
        """
        if message.labels.get("response_mode") == ResponseMode.SYNC.value:
            logger.info(f"同步模式任务 {message.task_id} 失败，不进行重试")
            return False
        return super().is_retry_on_error(message)
          
    async def on_error(  
        self,  
        message: "TaskiqMessage",  
        result: "TaskiqResult[Any]",  
        exception: BaseException,  
    ) -> None:  
        if isinstance(exception, NoResultError):
            return

        retry_on_error = self.is_retry_on_error(message)
        result.labels["_last_error"] = str(exception)
        
        if not retry_on_error:
            logger.info(f"任务 {message.task_name} 失败，重试功能未打开，不进行重试")
            result.labels["_terminated"] = True
            return
        
        # 不符合条件的异常不重试
        
        # 条件1: 异常在可重试异常列表中
        # 条件2: 异常是NebulaException的子类,并且should_retry为True
        if exception not in self.retryable_exceptions and not (isinstance(exception, NebulaException) and exception.should_retry):
            logger.info(f"异常 {exception} 不符合重试条件，不进行重试")
            result.labels["_terminated"] = True
            return

        result.labels["_max_retries"] = self.default_retry_count

        retries = int(message.labels.get("_retries", 0)) + 1
        max_retries = int(message.labels.get("max_retries", self.default_retry_count))

        if retries <= max_retries:
            delay = self.make_delay(message, retries)

            logger.info (f"{int(delay)}s后,开始第{retries}/{max_retries} 次重试任务: {message.task_name}")

            kicker: AsyncKicker[Any, Any] = (
                AsyncKicker(
                    task_name=message.task_name,
                    broker=self.broker,
                    labels=message.labels,
                )
                .with_task_id(message.task_id)
                .with_labels(_retries=retries)
            )

            await self.on_send(kicker, message, delay)

            
            result.labels["_retries"] = retries
            result.labels["_delay"] = int(delay)
            result.labels["_next_retry_at"] = datetime.datetime.now() + datetime.timedelta(seconds=delay)
            result.labels["_terminated"] = False

            if self.no_result_on_retry:
                result.error = NoResultError()

        else:
            logger.info (f'达到最大重试{max_retries}次数，不再重试, 任务失败, 错误:{exception}')
            result.error = exception
            result.labels["_terminated"] = True
            result.labels["_retries"] = max_retries