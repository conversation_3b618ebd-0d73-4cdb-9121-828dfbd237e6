

import logging
import time
from nebula.core.infrastructure import logging as xlog
from nebula.core.tasks.context import task_id_var, retry_index_var, step_var

class TaskIDFilter(logging.Filter):
    def filter(self, record):
        task_id = task_id_var.get()
        if task_id is not None:
            record.task_id = task_id
            record.retry_index = retry_index_var.get(0)
            record.step = step_var.get("N/A")
        return True

class SafeFormatter(logging.Formatter):
    def format(self, record):
        if not hasattr(record, "task_id"):
            # 非任务日志,调用系统格式器
            return xlog.formatter.format(record)
        else:
            # 设置默认字段值
            for key, default in {
                "task_id": "-",
                "retry_index": 0,
                "step": "N/A",
            }.items():
                if not hasattr(record, key):
                    setattr(record, key, default)
            record.order_no = time.monotonic_ns()
            return super().format(record)

# 注入任务日志格式
def inject_task_log_format():
    print ("----- 注入任务日志格式 -----")
    taskformatter = SafeFormatter('TASK-LOG >> [%(asctime)s.%(msecs)03d] (task_id=%(task_id)s, order_no=%(order_no)s, retry_index=%(retry_index)s, step=%(step)s) %(levelname)s %(name)s - %(message)s << TASK-LOG', datefmt='%Y-%m-%d %H:%M:%S')
    task_filter = TaskIDFilter()
    xlog.console_handler.setFormatter(taskformatter)
    xlog.console_handler.addFilter(task_filter)
