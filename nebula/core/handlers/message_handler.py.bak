#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通用消息处理器模块
提供消息处理的基础设施和接口
"""

import logging
from typing import Dict, Any, Optional, Generic, TypeVar, Protocol
from nebula.core.protocol.base import MessageEntity, MessageType, ResponseMode
from nebula.core.protocol.web import WebNotification
from nebula.core.infrastructure import exceptions, calculate_time, provider
from datetime import datetime


T = TypeVar('T', bound=MessageEntity)

class MessageServiceProtocol(Protocol):
    """消息服务接口协议"""
    async def ensure_connection(self) -> None: ...
    async def connect(self) -> None: ...
    async def close(self) -> None: ...
    def register_callback(self, topic: str, callback: callable) -> None: ...
    async def start_consuming(self, topic: str) -> None: ...
    async def publish_queue_message(self, message: Dict[str, Any], priority: int = 0) -> None: ...

class MessageHandler(Generic[T]):
    """消息处理器基类"""
    
    def __init__(self, message_type: type[T]):
        """
        初始化处理器
        
        Args:
            message_type: 消息类型类
        """
        self.message_type = message_type
        self.topic = message_type.get_topic()
        self.message_service = None
        
        # 日志配置
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.setLevel(logging.INFO)
    
    async def setup(self, message_service: MessageServiceProtocol):
        """
        设置处理器，包括注册回调等
        
        Args:
            message_service: 消息服务实例
        """
        # 保存消息服务实例
        self.message_service = message_service
        
        if message_service is None:
            self.logger.warning("消息服务为空，跳过设置")
            return
            
        # 确保连接可用
        await self.message_service.ensure_connection()
        
        # 设置预取数量为1，确保消息按顺序处理
        await self.message_service.channel.set_qos(prefetch_count=1)
        
        # 注册消息回调
        self.message_service.register_callback(self.topic, self.handle_message)
        self.logger.info(f"已注册主题 {self.topic} 的处理方法")
    
    async def shutdown(self):
        """关闭处理器"""
        self.logger.info(f"正在关闭处理器: {self.__class__.__name__}")
        self.message_service = None
    
    async def handle_message(self, message_entity: MessageEntity) -> None:
        """
        处理消息的基础方法
        
        Args:
            message: 消息实体
        """
        typed_message = None
        try:
            # 将原始消息数据转换为类型化的消息对象
            typed_message = self.message_type.parse_obj(message_entity.data)
            self.logger.info(f"从队列{self.topic}中取到新消息: {message_entity}")
            await self.pre_process(typed_message)
            await self.process(typed_message)
        except Exception as e:
            await self.handle_error(message_entity, typed_message, e)
        finally:
            await self.post_process(typed_message)
    
    async def pre_process(self, message: T) -> None:
        """
        消息处理前的预处理
        
        Args:
            message: 类型化的消息对象
        """
        self.logger.info(f"开始处理消息: {self.topic}")
        # 构建完整动作名称
        full_action = f"{message.__class__.__name__}.{message.action}"
        self.logger.info(f"处理任务: task_id={message.task_id}, action={full_action}, response_mode={message.response_mode}")
        # 创建通知对象的初始化参数，仅在需要时创建
        message.headers = {}
        if message.response_mode == ResponseMode.WEBSOCKET or message.response_mode == ResponseMode.CALLBACK:
            message.headers["notify"] = {
                "event": full_action,
                "from_id": message.task_id,
                "response_mode": message.response_mode,
                "result": None,
                "to_web_topic": "GENERAL",
                "failed": False,
                "type": "success",
                "persistent": False
            }
        message.headers["full_action"] = full_action
    
    async def process(self, message: T) -> None:
        """
        处理任务消息的通用方法

        Args:
            message: 任务消息
        """
        full_action = message.headers["full_action"]
        # 根据动作类型调用对应的处理函数
        if message.action in self._action_handlers:
            async with calculate_time(f"{full_action}", logger=self.logger):
                result = await self._action_handlers[message.action](message)
        else:
            raise ValueError(f"未知的动作类型: {message.action}")

        # self.logger.info(f"处理任务完成: {result}")
        self.logger.info(f"处理任务完成: task_id={message.task_id}, action={full_action}, response_mode={message.response_mode}")
        # 处理成功结果
        await self._handle_result(message, result)

    
    async def post_process(self, message: T) -> None:
        """
        消息处理后的后处理
        根据消息类型和响应模式发送任务结果
        
        Args:
            message: 类型化的消息对象
        """
        self.logger.info(f"消息处理完成: {self.topic}")
        full_action = message.headers["full_action"]
        if message.response_mode == ResponseMode.SYNC:
            failed = message.headers["failed"]
            errcode = message.headers["errcode"]
            msg = message.headers["msg"]
            result = message.headers.get("result", {})
            await self.publish_task_result(message.task_id, failed, errcode, msg, full_action, result)
        try:
            await self._send_notification(message.headers["notify"], message)            
        except Exception as e:
            self.logger.error(f"发送WebSocket通知失败: {str(e)}", exc_info=True)
    

    async def _handle_result(self, message: T, result: Dict[str, Any]) -> None:
        """
        处理成功结果

        Args:
            message: 任务消息
            full_action: 完整动作名称
            result: 处理结果
            notification: 通知对象

        Returns:
            处理后的通知对象
        """
        message.headers["failed"] = False
        message.headers["errcode"] = 0
        message.headers["msg"] = "ok"
        message.headers["result"] = result

        full_action = message.headers["full_action"]
        if message.response_mode == ResponseMode.WEBSOCKET or message.response_mode == ResponseMode.SYNC:
            # 发布处理结果
            if message.response_mode == ResponseMode.SYNC:
                message.headers["notify"] = None
            if message.response_mode == ResponseMode.WEBSOCKET:
                message.headers["notify"]["result"] = result
                message.headers["notify"]["type"] = "success"
                message.headers["notify"]["text"] = "任务处理完成"

        if message.response_mode == ResponseMode.CALLBACK:
            # http callback - 待实现
            pass

    async def handle_error(self, message_entity: MessageEntity, message: T, error: Exception) -> None:
        """
        处理消息处理过程中的错误
        
        Args:
            message: 消息实体
            error: 异常对象
        """
        self.logger.error(f"处理消息失败: {str(error)}", exc_info=True)
        message.headers["failed"] = True
        if isinstance(error, exceptions.BusinessError):
            message.headers["failed"] = False # 业务逻辑错误, 任务成功
            message.headers["errcode"] = 1
            message.headers["msg"] = f"业务错误: {str(error)}"
            if message.response_mode == ResponseMode.WEBSOCKET:
                message.headers["notify"]["text"] = str(error)
                message.headers["notify"]["type"] = "error"
        else:
            message.headers["errcode"] = 2
            message.headers["msg"] = f"系统错误: {str(error)}"
            if message.response_mode == ResponseMode.WEBSOCKET:
                message.headers["notify"]["text"] = str(error)
                message.headers["notify"]["type"] = "error"
        
        if message.response_mode == ResponseMode.WEBSOCKET or message.response_mode == ResponseMode.CALLBACK:
            # 异步任务,可以重试
            if hasattr(error, 'should_retry') and getattr(error, 'should_retry', False):
                await self._handle_task_retry(message_entity, error)
    
    def get_message_data(self, message: MessageEntity, key: str, default: Any = None) -> Any:
        """
        安全地获取消息数据
        
        Args:
            message: 消息实体
            key: 数据键名
            default: 默认值
            
        Returns:
            消息数据值
        """
        return message.data.get(key, default)
    
    async def _handle_task_retry(self, message_entity: MessageEntity, error: Exception) -> None:
        """
        处理任务重试

        Args:
            message_entity: 任务消息
            error: 导致失败的异常
        """
        # 记录任务重试前的状态
        self.logger.info(
            f"处理任务重试: task_id={message_entity.task_id}, "
            f"retry_count={message_entity.retry_count}, "
            f"max_retry_count={message_entity.max_retry_count}, "
            f"is_delayed={message_entity.is_delayed}, "
            f"error={str(error)}"
        )

        # 检查是否是同步模式，同步模式不进行重试
        if message_entity.response_mode == ResponseMode.SYNC:
            self.logger.info(f"同步模式任务 {message_entity.task_id} 失败，不进行重试")
            return

        # 检查是否已经是延迟执行的消息
        if message_entity.is_delayed and message_entity.retry_count >= message_entity.max_retry_count:
            self.logger.warning(
                f"延迟任务 {message_entity.task_id} 已达到最大重试次数 {message_entity.max_retry_count}，不再重试"
            )
            return

        # 检查消息服务是否可用
        if not self.message_service:
            self.logger.error("消息服务未初始化，无法进行任务重试")
            return

        try:
            # 记录重试前的计数
            old_retry_count = message_entity.retry_count

            # 发送延迟消息
            await self.message_service.publish_delayed_message(message_entity, error)

            # 记录重试后的状态
            self.logger.info(
                f"已安排任务 {message_entity.task_id} 延迟重试，"
                f"重试计数: {old_retry_count} -> {message_entity.retry_count}, "
                f"最大重试次数: {message_entity.max_retry_count}, "
                f"下次重试时间: {message_entity.next_retry_time.isoformat() if message_entity.next_retry_time else 'None'}"
            )
        except Exception as e:
            self.logger.error(f"安排任务 {message_entity.task_id} 延迟重试失败: {str(e)}", exc_info=True)
            # 这里不再抛出异常，避免影响正常的错误处理流程
    
    async def _send_notification(self, notify: Dict[str, Any], message: T) -> None:
        """
        发送通知
        
        Args:
            notify: 通知对象
            message: 消息对象
        """
        if self.message_service:
            # 这里是websocket回调
            notification = WebNotification(
                event=notify["event"],
                from_id=notify["from_id"],
                response_mode=notify["response_mode"],
                failed=notify["failed"],
                to_web_topic=notify["to_web_topic"],
                text=notify["text"],
                metadata=notify["result"]
            )
            await self.message_service.publish_typed_message(notification)
            self.logger.info(f"已发送WebSocket通知: GENERAL -> {notification.event}")

    async def publish_task_result(self, task_id: str,failed: bool,errcode: int, msg: str,action: str, attach: Dict[str, Any] = {}):
        """
        发布任务处理结果
        
        Args:
            result: 处理结果
            priority: 优先级
        """
        if not self.message_service:
            self.logger.error("未初始化消息服务，无法发布任务结果")
            return

        await self.message_service.publish_task_result(self.topic, task_id, failed, errcode, msg, action, attach)