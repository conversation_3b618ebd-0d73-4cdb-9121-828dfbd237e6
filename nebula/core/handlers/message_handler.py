#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通用消息处理器模块
提供消息处理的基础设施和接口
"""

import logging
from typing import Dict, Any, Optional, Generic, TypeVar, Protocol
from nebula.core.protocol.base import MessageEntity, MessageType
from datetime import datetime

T = TypeVar('T', bound=MessageEntity)

class MessageServiceProtocol(Protocol):
    """消息服务接口协议"""
    async def ensure_connection(self) -> None: ...
    async def connect(self) -> None: ...
    async def close(self) -> None: ...
    def register_callback(self, topic: str, callback: callable) -> None: ...
    async def start_consuming(self, topic: str) -> None: ...
    async def publish_queue_message(self, message: Dict[str, Any], priority: int = 0) -> None: ...

class MessageHandler(Generic[T]):
    """消息处理器基类"""
    
    def __init__(self, message_type: type[T]):
        """
        初始化处理器
        
        Args:
            message_type: 消息类型类
        """
        self.message_type = message_type
        self.topic = message_type.get_topic()
        self.message_service = None
        
        # 日志配置
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.setLevel(logging.INFO)
    
    async def setup(self, message_service: MessageServiceProtocol):
        """
        设置处理器，包括注册回调等
        
        Args:
            message_service: 消息服务实例
        """
        # 保存消息服务实例
        self.message_service = message_service
        
        if message_service is None:
            self.logger.warning("消息服务为空，跳过设置")
            return
            
        # 确保连接可用
        await self.message_service.ensure_connection()
        
        # 设置预取数量为1，确保消息按顺序处理
        await self.message_service.channel.set_qos(prefetch_count=1)
        
        # 注册消息回调
        self.message_service.register_callback(self.topic, self.handle_message)
        self.logger.info(f"已注册主题 {self.topic} 的处理方法")
    
    async def shutdown(self):
        """关闭处理器"""
        self.logger.info(f"正在关闭处理器: {self.__class__.__name__}")
        self.message_service = None
    
    async def handle_message(self, message: MessageEntity) -> None:
        """
        处理消息的基础方法
        
        Args:
            message: 消息实体
        """
        try:
            # 将原始消息数据转换为类型化的消息对象
            typed_message = self.message_type.parse_obj(message.data)
            self.logger.info(f"从队列{self.topic}中取到新消息: {message}")
            await self.pre_process(typed_message)
            await self.process(typed_message)
            await self.post_process(typed_message)
        except Exception as e:
            await self.handle_error(message, e)
            raise  # 重新抛出异常，让 MessageService 处理消息确认/拒绝
    
    async def pre_process(self, message: T) -> None:
        """
        消息处理前的预处理
        
        Args:
            message: 类型化的消息对象
        """
        self.logger.info(f"开始处理消息: {self.topic}")
    
    async def process(self, message: T) -> None:
        """
        具体的消息处理逻辑，子类需要实现此方法
        
        Args:
            message: 类型化的消息对象
        """
        raise NotImplementedError("子类必须实现process方法")
    
    async def post_process(self, message: T) -> None:
        """
        消息处理后的后处理
        
        Args:
            message: 类型化的消息对象
        """
        self.logger.info(f"消息处理完成: {self.topic}")
    
    async def handle_error(self, message: MessageEntity, error: Exception) -> None:
        """
        处理消息处理过程中的错误
        
        Args:
            message: 消息实体
            error: 异常对象
        """
        self.logger.error(f"处理消息失败: {str(error)}", exc_info=True)
        # TODO: 实现错误处理逻辑，如重试、发送通知等
    
    def get_message_data(self, message: MessageEntity, key: str, default: Any = None) -> Any:
        """
        安全地获取消息数据
        
        Args:
            message: 消息实体
            key: 数据键名
            default: 默认值
            
        Returns:
            消息数据值
        """
        return message.data.get(key, default)
    
    def validate_required_fields(self, message: MessageEntity, required_fields: list) -> bool:
        """
        验证消息是否包含所有必需字段
        
        Args:
            message: 消息实体
            required_fields: 必需字段列表
            
        Returns:
            bool: 是否包含所有必需字段
        """
        for field in required_fields:
            if field not in message.data:
                self.logger.error(f"消息缺少必需字段: {field}")
                return False
        return True

  