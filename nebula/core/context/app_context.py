"""
应用上下文管理模块 - 提供应用生命周期和服务定位
"""

import logging
import asyncio
from typing import Dict, Any, Type, List, Optional, Callable, TypeVar, Awaitable, Set, Union
import os
import signal
import sys
import time
import importlib
from nebula.core.infrastructure.exceptions import RuntimeError
from nebula.core.infrastructure import provider

logger = logging.getLogger(__name__)

T = TypeVar('T')
ComponentType = Union[str, Type]

# 全局上下文实例
_app_context = None

# 支持的组件列表
SUPPORTED_COMPONENTS = {
    'database': 'nebula.core.infrastructure.database.MongoDB',
    'cache': 'nebula.core.infrastructure.cache.RedisCache',
    'storage': 'nebula.core.infrastructure.storage.StorageService',
    'gemini': 'nebula.core.infrastructure.gemini.GeminiService',
    'browser': 'nebula.core.infrastructure.browser.BrowserService',
    'message': 'nebula.core.infrastructure.message.MessageService',
    'cloudflare': 'nebula.core.service.cloudflare.CloudflareService',
    'auth': 'nebula.core.service.auth.AuthService',
    'memo': 'nebula.core.service.memo.MemoService',
    'video': 'nebula.core.service.video.VideoService',
    'notification': 'nebula.core.service.notification.NotificationService',
    'telegram': 'nebula.core.service.telegram.TelegramService',
    'misc': 'nebula.core.service.misc.MiscService',
    'taskman': 'nebula.core.service.taskman.TaskService'
}

class ApplicationContext:
    """应用上下文管理器，负责应用的初始化、启动和关闭"""

    def __init__(self, settings: Optional[Any] = None):
        """
        初始化应用上下文

        Args:
            settings: 应用配置，如果为None则通过get_settings()获取
        """
        logger.info("初始化应用上下文")

        # 延迟导入，避免循环依赖
        from nebula.core.infrastructure.config import get_settings
        self.settings = settings or get_settings()
        self._components_type = {}
        self._shutdown_handlers = []
        self._started = False
        self._stopping = False
        self._auto_start_components: Set[str] = set()
        self._app_name = "nebula"

    async def initialize(self,components: Optional[List[str]] = None):
        """初始化应用上下文"""
        try:
            # 配置日志
            await self._configure_logging()

            logger.info("正在初始化应用上下文...")

            # 注册组件
            await self._register_components(components)

            logger.info("应用上下文初始化完成")
        except Exception as e:
            logger.error(f"应用上下文初始化失败: {str(e)}", exc_info=True)
            # 延迟导入，避免循环依赖
            from nebula.core.infrastructure.exceptions import ConfigError
            raise ConfigError(f"应用上下文初始化失败: {str(e)}")

    async def _configure_logging(self):
        """配置日志系统"""
        try:
            # 获取日志配置
            log_config = self.settings.logging
            log_level = getattr(logging, log_config.level.upper(), logging.INFO)
            log_file = log_config.file
            stacktrace_file = log_config.stacktrace_file
            console_show_exc = log_config.console_show_exc

            # 延迟导入，避免循环依赖
            from nebula.core.infrastructure.logging import setup_logging

            # 配置日志
            setup_logging(
                level=log_level,
                log_file=log_file,
                log_format=log_config.format,
                text_format=getattr(log_config, 'text_format', None),
                date_format=getattr(log_config, 'date_format', None),
                stacktrace_file=stacktrace_file,
                console_show_exc=console_show_exc
            )

            logger.info(f"日志已配置: 级别={log_level}, 文件={log_file}, 堆栈跟踪文件={stacktrace_file}, 控制台显示异常={console_show_exc}, 格式={log_config.format}")
        except Exception as e:
            # 基本配置，确保有日志输出
            logging.basicConfig(level=logging.INFO)
            logger.warning(f"日志配置失败: {str(e)}")
            raise

    def get_component(self,name: str) -> Any:
        """获取组件实例"""
        return provider.get(self._components_type[name])

    async def _register_components(self,components: Optional[List[str]] = []):
        """注册基础设施组件"""
        # 延迟导入基础设施组件
        # 使用字符串反射动态导入组件

        def load_module(name: str, class_path: str):
            try:
                module_path, class_name = class_path.rsplit('.', 1)
                module = importlib.import_module(module_path)
                return getattr(module, class_name)
            except (ImportError, AttributeError) as e:
                raise ImportError(f"动态导入组件 {name}: {class_path} 失败: {str(e)}")

        # 检查请求的组件是否在已定义的组件映射中
        # 使用集合操作计算未知组件
        unknown_components = set(components) - set(SUPPORTED_COMPONENTS.keys())
        if unknown_components:
            logger.error(f"请求的组件(SUPPORTED_COMPONENTS)不存在: {', '.join(unknown_components)}", exc_info=True)
            raise RuntimeError(f"请求的组件(SUPPORTED_COMPONENTS)不存在: {', '.join(unknown_components)}")

        # 过滤掉未知组件
        components = list(set(components) & set(SUPPORTED_COMPONENTS.keys()))
        try:
            for name, cls_path in SUPPORTED_COMPONENTS.items():
                if name not in components:
                    continue
                cls = load_module(name, cls_path)
                instance = cls(settings=self.settings)
                self._components_type[name] = cls
                try:
                    # 直接使用provider注册组件
                    provider.register(instance)
                    logger.debug(f"已注册组件: {name}")
                except Exception as e:
                    logger.warning(f"注册组件 {name} 失败: {str(e)}")
        except ImportError as e:
            logger.error(f"导入组件失败: {str(e)}", exc_info=True)

    def register_shutdown_handler(self, handler: Callable[[], Awaitable[None]]):
        """
        注册关闭处理函数

        Args:
            handler: 关闭处理函数
        """
        self._shutdown_handlers.append(handler)

    def enable_component(self, component_name: str):
        """
        启用组件，使其在启动时自动初始化

        Args:
            component_name: 组件名称
        """
        if component_name in SUPPORTED_COMPONENTS:
            self._auto_start_components.add(component_name)
            logger.debug(f"已启用组件: {component_name}")
        else:
            logger.warning(f"未知组件: {component_name}")

    def disable_component(self, component_name: str):
        """
        禁用组件，使其在启动时不自动初始化

        Args:
            component_name: 组件名称
        """
        if component_name in self._auto_start_components:
            self._auto_start_components.remove(component_name)
            logger.debug(f"已禁用组件: {component_name}")

    async def _start_component(self, name: str):
        """启动单个组件

        Args:
            name: 组件名称
        """
        # 延迟导入以避免循环依赖
        from nebula.core.infrastructure import provider

        # 查找组件映射
        if name not in SUPPORTED_COMPONENTS:
            logger.warning(f"未知组件: {name}")
            return False

        # 加载完整的组件类
        cls_path = SUPPORTED_COMPONENTS[name]
        try:
            module_path, class_name = cls_path.rsplit('.', 1)
            module = importlib.import_module(module_path)
            cls = getattr(module, class_name)
        except (ImportError, AttributeError) as e:
            logger.error(f"加载组件 {name} 失败: {str(e)}")
            return False

        # 从provider获取组件实例
        component = provider.get(cls, raise_error=False)

        # 检查组件是否可用且支持connect方法
        if not component:
            logger.warning(f"{name} 组件不可用")
            return False

        if not hasattr(component, 'connect'):
            # logger.warning(f"{name} 组件不支持connect方法")
            return True

        try:
            # 调用组件的connect方法进行连接
            result = await component.connect()

            if result:
                # logger.info(f"{name} 组件已连接")
                return True
            else:
                logger.warning(f"{name} 组件连接失败")
                return False
        except Exception as e:
            logger.error(f"{name} 组件连接出错: {str(e)}", exc_info=True)
            return False

    async def start(self, components: Optional[List[str]] = None):
        """
        启动所有组件

        Args:
            components: 要启动的组件列表，如果为None则启动所有自动启动的组件
        """
        if self._started:
            logger.warning("应用上下文已经启动")
            return

        logger.info("正在启动应用上下文...")

        # 如果未指定组件列表，则使用自动启动的组件
        if components is None:
            components = list(self._auto_start_components)
        else:
            # 确保所有组件都是支持的
            for component in components:
                if component not in SUPPORTED_COMPONENTS:
                    logger.warning(f"未知组件: {component}")

        # 启动所有组件
        for component_name in components:
            enabled = await self._start_component(component_name)

            if enabled:
                logger.info(f"已启动组件: {component_name}")
            else:
                logger.warning(f"组件 {component_name} 启动失败")

        # 设置启动标志
        self._started = True
        logger.info("应用上下文启动完成")

    async def stop(self):
        """停止所有组件，关闭应用上下文"""
        if self._stopping:
            # 避免重复停止
            return

        self._stopping = True
        logger.info("正在关闭应用上下文...")

        # 调用所有关闭处理函数
        for handler in self._shutdown_handlers:
            try:
                await handler()
            except Exception as e:
                logger.error(f"关闭处理函数执行出错: {str(e)}", exc_info=True)

        # 关闭所有组件
        from nebula.core.infrastructure import provider
        components = provider.list_resources()

        for key, component in components.items():
            if component and hasattr(component, 'disconnect'):
                try:
                    await component.disconnect()
                    logger.info(f"已关闭组件: {key}")
                except Exception as e:
                    logger.error(f"关闭组件 {key} 时出错: {str(e)}", exc_info=True)

        logger.info("应用上下文已关闭")
        self._started = False
        self._stopping = False


# 公共API

def get_app_context() -> ApplicationContext:
    """获取应用上下文实例"""
    global _app_context

    if _app_context is None:
        raise RuntimeError("应用上下文尚未初始化")

    return _app_context

async def init_application(app_name: str = "nebula", debug: bool = False,
                          components: Optional[List[str]] = None,
                          reg_signal: bool = True) -> ApplicationContext:
    """
    初始化应用

    Args:
        app_name: 应用名称
        debug: 是否为调试模式
        components: 要启动的组件列表

    Returns:
        ApplicationContext: 应用上下文实例
    """
    global _app_context

    if components == ["*"]:
        components = list(SUPPORTED_COMPONENTS.keys())

    # 创建应用上下文
    _app_context = ApplicationContext()
    _app_context._app_name = app_name

    # 初始化上下文
    await _app_context.initialize(components)

    if reg_signal:
        # 注册信号处理函数
        register_signal_handlers()

    # 启动组件
    await _app_context.start(components)

    return _app_context

def register_signal_handlers():
    """注册信号处理函数，用于优雅关闭应用"""
    if sys.platform != "win32":
        # 注册SIGTERM信号处理函数（用于容器优雅关闭）
        signal.signal(signal.SIGTERM, lambda sig, frame: asyncio.create_task(graceful_shutdown()))

        # 注册SIGINT信号处理函数（用于Ctrl+C）
        signal.signal(signal.SIGINT, lambda sig, frame: asyncio.create_task(graceful_shutdown()))

async def graceful_shutdown():
    """优雅关闭应用"""
    global _app_context

    if _app_context:
        await _app_context.stop()
