"""
通用装饰器模块

提供通用控制装饰器，用于控制函数行为。
"""

from functools import wraps
import random
from typing import Any, Callable, TypeVar, cast, Optional
import logging
import time

from nebula.core.infrastructure.exceptions import DebugRetryException
from nebula.core.infrastructure.logging import get_logger

# 获取日志记录器
logger = get_logger(__name__)

# 定义类型变量
F = TypeVar('F', bound=Callable[..., Any])


def control(
    retry_probability: float = 0,
    retry_message: str = "故意抛出异常",
    log_time: bool = False,
    log_level: int = logging.INFO,
    # 将来可以添加更多控制选项
) -> Callable[[F], F]:
    """
    通用控制装饰器

    这个装饰器提供各种控制功能，可以通过参数配置不同的行为。

    Args:
        retry_probability: 抛出重试异常的概率，0-1之间，0表示不抛出
        retry_message: 重试异常的消息
        log_time: 是否记录执行时间
        log_level: 日志级别，默认为INFO
        # 将来可以添加更多参数

    Returns:
        装饰器函数

    Example:
        ```python
        # 80%概率抛出重试异常
        @control(retry_probability=0.8)
        async def some_method(self):
            pass

        # 记录执行时间
        @control(log_time=True)
        async def another_method(self):
            pass

        # 同时使用多个控制选项
        @control(retry_probability=0.5, log_time=True, log_level=logging.DEBUG)
        async def complex_method(self):
            pass
        ```
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            # 记录开始时间（如果需要）
            start_time = time.perf_counter() if log_time else None

            # 处理重试概率
            rnt = random.random()
            logging.info(f"---------------(随机数: {rnt}) < 概率: {retry_probability} => {rnt < retry_probability} --------------")
            if retry_probability > 0 and rnt < retry_probability:
                logger.info(f"控制装饰器: 函数 {func.__name__} 将抛出异常 (概率: {retry_probability} | 随机数: {rnt})")
                raise DebugRetryException(retry_message)

            # 执行函数
            result = await func(*args, **kwargs)

            # 记录执行时间（如果需要）
            if log_time and start_time is not None:
                elapsed = time.perf_counter() - start_time
                logger.log(log_level, f"函数 {func.__name__} 执行时间: {elapsed:.4f}秒")

            return result

        return cast(F, wrapper)

    return decorator
