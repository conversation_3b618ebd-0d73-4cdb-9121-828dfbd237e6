"""
S3对象存储模块
"""
import os
import tempfile
import logging
from typing import BinaryIO, Dict, Optional, Tuple, Union, Any, List
import io
from datetime import timedelta, datetime

import aioboto3
from botocore.exceptions import ClientError
from botocore.client import Config
import contextlib

import nebula.core.infrastructure.logging as logging
import nebula.core.infrastructure.config as config

logger = logging.get_logger(__name__)

class StorageService:
    """存储服务，提供S3对象存储功能"""

    def __init__(self, settings: Optional[config.Settings] = None):
        """
        初始化存储服务

        Args:
            settings: 配置对象
        """
        self._settings = settings
        self._s3_client = None
        self._s3_resource = None
        self._session = None
        self._exit_stack = None
        self._initialized = False
        self._bucket = None
        self._endpoint = None
        self._public_endpoint = None
        self._enabled_sub_domain = False
        logger.debug("存储服务初始化")

    async def connect(self):
        """连接到存储服务"""
        if self._initialized:
            return self

        try:
            # 从配置获取S3设置
            if self._settings and hasattr(self._settings, 'storage'):
                storage_config = self._settings.storage
                self._endpoint = getattr(storage_config.s3, 'endpoint') or os.environ.get('MINIO_ENDPOINT')
                access_key = getattr(storage_config.s3, 'access_key') or os.environ.get('MINIO_ACCESS_KEY')
                secret_key = getattr(storage_config.s3, 'secret_key') or os.environ.get('MINIO_SECRET_KEY')
                region = getattr(storage_config.s3, 'region') or os.environ.get('MINIO_REGION')
                self._bucket = getattr(storage_config.s3, 'bucket') or os.environ.get('MINIO_BUCKET', 'nebula')
                self._public_endpoint = getattr(storage_config.s3, 'frontend') or os.environ.get('MINIO_FRONTEND')
                self._enabled_sub_domain = getattr(storage_config.s3, 'enabled_sub_domain', False)

            if not all([self._endpoint, access_key, secret_key]):
                logger.warning("存储服务配置不完整，服务将不可用")
                return self

            # 初始化S3客户端和资源 (异步版本)
            # 使用 AsyncExitStack 管理异步上下文管理器
            self._session = aioboto3.Session()
            self._exit_stack = contextlib.AsyncExitStack()

            # 创建 S3 客户端 - 使用异步上下文管理器
            s3_client_ctx = self._session.client(
                's3',
                endpoint_url=self._endpoint,
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_key,
                region_name=region,
                config=Config(signature_version='s3v4')
            )
            self._s3_client = await self._exit_stack.enter_async_context(s3_client_ctx)

            # 创建 S3 资源对象 - 使用异步上下文管理器
            s3_resource_ctx = self._session.resource(
                's3',
                endpoint_url=self._endpoint,
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_key,
                region_name=region,
                config=Config(signature_version='s3v4')
            )
            self._s3_resource = await self._exit_stack.enter_async_context(s3_resource_ctx)

            # 确保存储桶存在
            await self._ensure_bucket_exists()

            self._initialized = True
            logger.info(f"存储服务已连接: endpoint={self._endpoint}, bucket={self._bucket}")
        except Exception as e:
            # 如果初始化过程中出错，关闭所有已打开的资源
            if hasattr(self, '_exit_stack') and self._exit_stack:
                await self._exit_stack.aclose()
                self._exit_stack = None
            logger.error(f"存储服务连接失败: {str(e)}")
            raise

        return self

    async def _ensure_bucket_exists(self):
        """确保存储桶存在"""
        if not self._s3_client:
            return

        try:
            try:
                await self._s3_client.head_bucket(Bucket=self._bucket)
                logger.debug(f"存储桶已存在: {self._bucket}")
            except ClientError:
                # 桶不存在，创建它
                await self._s3_client.create_bucket(Bucket=self._bucket)

                # 设置公共访问策略
                policy = {
                    "Version": "2012-10-17",
                    "Statement": [
                        {
                            "Effect": "Allow",
                            "Principal": {"AWS": "*"},
                            "Action": ["s3:GetObject"],
                            "Resource": [f"arn:aws:s3:::{self._bucket}/*"]
                        }
                    ]
                }
                await self._s3_client.put_bucket_policy(
                    Bucket=self._bucket,
                    Policy=str(policy).replace("'", '"')
                )
                logger.info(f"已创建存储桶: {self._bucket}")
        except Exception as e:
            logger.error(f"确保存储桶存在失败: {str(e)}")

    async def close(self):
        """关闭存储服务连接"""
        if hasattr(self, '_exit_stack') and self._exit_stack:
            await self._exit_stack.aclose()
            self._exit_stack = None
        self._s3_client = None
        self._s3_resource = None
        self._session = None
        self._initialized = False
        logger.info("存储服务已关闭")

    async def create_bucket(self, bucket_name: str) -> bool:
        """创建存储桶

        Args:
            bucket_name: 存储桶名称

        Returns:
            如果创建成功则返回True，否则返回False
        """
        if not self._s3_client:
            logger.error("存储服务未初始化")
            return False

        try:
            await self._s3_client.create_bucket(Bucket=bucket_name)
            return True
        except ClientError as e:
            logger.error(f"创建存储桶失败: {str(e)}")
            return False

    async def bucket_exists(self, bucket_name: str) -> bool:
        """检查存储桶是否存在

        Args:
            bucket_name: 存储桶名称

        Returns:
            如果存储桶存在则返回True，否则返回False
        """
        if not self._s3_client:
            logger.error("存储服务未初始化")
            return False

        try:
            await self._s3_client.head_bucket(Bucket=bucket_name)
            return True
        except ClientError:
            return False

    async def upload_file(
        self,
        file_path: str,
        object_name: str,
        bucket_name: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None,
        content_type: Optional[str] = None
    ) -> str:
        """上传文件

        Args:
            file_path: 文件路径
            object_name: 对象名称/键
            bucket_name: 存储桶名称，如果为None则使用默认桶
            metadata: 元数据
            content_type: 内容类型

        Returns:
            上传后的公共URL
        """
        if not self._s3_client:
            logger.error("存储服务未初始化")
            raise RuntimeError("存储服务未初始化")

        bucket = bucket_name or self._bucket

        try:
            # 如果未指定content_type，尝试从文件扩展名推断
            if content_type is None:
                content_type = self._guess_content_type(file_path)

            # 准备上传参数
            extra_args = {
                'ContentType': content_type
            }
            if metadata:
                extra_args['Metadata'] = metadata

            # 使用 aioboto3 的 upload_file 方法上传文件
            await self._s3_client.upload_file(
                Filename=file_path,
                Bucket=bucket,
                Key=object_name,
                ExtraArgs=extra_args
            )

            # 构建公共URL
            url = self._build_url(object_name, bucket_name)

            return url
        except Exception as e:
            logger.error(f"上传文件失败: {str(e)}")
            raise

    async def upload_bytes(
        self,
        data: bytes,
        object_name: str,
        bucket_name: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None,
        content_type: Optional[str] = None
    ) -> str:
        """上传二进制数据

        Args:
            data: 二进制数据
            object_name: 对象名称/键
            bucket_name: 存储桶名称，如果为None则使用默认桶
            metadata: 元数据
            content_type: 内容类型

        Returns:
            上传后的公共URL
        """
        if not self._s3_client:
            logger.error("存储服务未初始化")
            raise RuntimeError("存储服务未初始化")

        bucket = bucket_name or self._bucket

        try:
            # 如果未指定content_type，尝试从对象名称推断
            if content_type is None:
                content_type = self._guess_content_type(object_name)

            # 准备上传参数
            extra_args = {
                'ContentType': content_type
            }
            if metadata:
                extra_args['Metadata'] = metadata

            # 上传数据 (异步方式)
            await self._s3_client.put_object(
                Bucket=bucket,
                Key=object_name,
                Body=data,
                **extra_args
            )

            # 构建公共URL
            url = self._build_url(object_name, bucket_name)

            return url
        except Exception as e:
            logger.error(f"上传二进制数据失败: {str(e)}")
            raise

    async def get_temp_meta_from_url(self, file_url: str) -> bool:
        """判断对象是否为临时对象

        Args:
            file_url: 文件URL

        Returns:

        """
        if not self._s3_client:
            logger.error("存储服务未初始化")
            raise RuntimeError("存储服务未初始化")
        
        if 'fee.red' not in file_url:
            return None
        
        if "/temp/" not in file_url:
            return None
        
        object_name =  "temp/" + file_url.split('/temp/')[-1]
        meta = await self.get_object_metadata(object_name)
        meta["object_name"] = object_name
        return meta

    async def upload_byte_to_temp(
        self,
        data: bytes,
        ext_name: Optional[str] = "",
        expires_in: Optional[int] = 86400,
        bucket_name: Optional[str] = None
    ) -> str:
        """
        上传文件到指定桶的临时存储区域，带有过期时间

        - **file**: 要上传的文件
        - **expires_in**: 过期时间(秒)，默认24小时
        - **bucket_name**: 存储桶名称，不指定则使用默认桶
        """
        # 使用tempfile.mkstemp()生成临时文件名
        _, temp_name = tempfile.mkstemp()
        temp_name = os.path.basename(temp_name)  # 只获取文件名部分

        # 添加时间戳前缀，使用yyyy-MM-dd格式
        date_prefix = datetime.now().strftime("%Y-%m-%d")

        # 构建临时文件路径，使用固定格式
        temp_path = f"temp/{date_prefix}/{temp_name}{ext_name}"

        # 计算过期时间
        expiry_date = datetime.now() + timedelta(seconds=expires_in)

        # 准备元数据，包含过期时间
        metadata = {
            "expires_at": expiry_date.isoformat(),
            "is_temp": "true"
        }

        # 使用upload_bytes方法直接上传字节数据，设置元数据
        url = await self.upload_bytes(
            data=data,
            bucket_name=bucket_name,  # 使用指定的桶或默认桶
            object_name=temp_path,
            metadata=metadata
        )
        logger.info(f"上传临时文件成功: {url}")
        return url, temp_path

    async def get_object(self, object_name: str, bucket_name: Optional[str] = None) -> bytes:
        """获取对象数据

        Args:
            object_name: 对象名称/键
            bucket_name: 存储桶名称，如果为None则使用默认桶

        Returns:
            对象数据
        """
        if not self._s3_client:
            logger.error("存储服务未初始化")
            raise RuntimeError("存储服务未初始化")

        bucket = bucket_name or self._bucket

        try:
            response = await self._s3_client.get_object(
                Bucket=bucket,
                Key=object_name
            )
            async with response['Body'] as stream:
                return await stream.read()
        except Exception as e:
            logger.error(f"获取对象失败: {str(e)}")
            raise

    async def download_file(self, object_name: str, file_path: str, bucket_name: Optional[str] = None) -> bool:
        """下载对象到文件

        Args:
            object_name: 对象名称/键
            file_path: 文件保存路径
            bucket_name: 存储桶名称，如果为None则使用默认桶

        Returns:
            是否成功下载
        """
        if not self._s3_client:
            logger.error("存储服务未初始化")
            return False

        bucket = bucket_name or self._bucket

        try:
            # 使用 aioboto3 的 download_file 方法下载文件
            await self._s3_client.download_file(
                Bucket=bucket,
                Key=object_name,
                Filename=file_path
            )
            return True
        except Exception as e:
            logger.error(f"下载对象失败: {str(e)}")
            return False

    async def delete_object(self, object_name: str, bucket_name: Optional[str] = None) -> bool:
        """删除对象

        Args:
            object_name: 对象名称/键
            bucket_name: 存储桶名称，如果为None则使用默认桶

        Returns:
            是否成功删除
        """
        if not self._s3_client:
            logger.error("存储服务未初始化")
            return False

        bucket = bucket_name or self._bucket

        try:
            r = await self._s3_client.delete_object(
                Bucket=bucket,
                Key=object_name
            )
            logger.info(f"---------------{bucket}/{object_name}--------------")
            logger.info(f"删除对象响应: {r}")
            return True
        except Exception as e:
            logger.error(f"删除对象失败: {str(e)}")
            return False

    async def get_presigned_url(self, object_name: str, expires: int = 3600, bucket_name: Optional[str] = None) -> str:
        """获取预签名URL

        Args:
            object_name: 对象名称/键
            expires: 过期时间（秒）
            bucket_name: 存储桶名称，如果为None则使用默认桶

        Returns:
            预签名URL
        """
        if not self._s3_client:
            logger.error("存储服务未初始化")
            raise RuntimeError("存储服务未初始化")

        bucket = bucket_name or self._bucket

        try:
            # 使用异步客户端生成预签名URL
            url = await self._s3_client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': bucket,
                    'Key': object_name
                },
                ExpiresIn=expires
            )

            # 记录日志，说明预签名URL需要使用原始S3 endpoint
            # logger.info(f"生成预签名URL: {url}")
            # logger.info(f"注意：预签名URL必须使用原始S3服务地址，不能替换域名，否则签名验证将失败")

            # 同时提供非预签名的前端URL参考（这个URL不带签名，可能无法访问受限资源）
            if self._public_endpoint:
                frontend_url = self._build_url(object_name, bucket_name)
                # logger.info(f"对应的frontend URL（不带签名）: {frontend_url}")

            # 返回原始预签名URL，不做任何修改
            return url
        except Exception as e:
            logger.error(f"获取预签名URL失败: {str(e)}")
            raise

    async def get_object_url(self,object_name,bucket_name: Optional[str]=None):
        return self._build_url(object_name,bucket_name)

    async def get_object_metadata(self, object_name: str, bucket_name: Optional[str] = None) -> Dict[str, str]:
        """获取对象的元数据

        Args:
            object_name: 对象名称/键
            bucket_name: 存储桶名称，如果为None则使用默认桶

        Returns:
            对象的元数据字典
        """
        if not self._s3_client:
            logger.error("存储服务未初始化")
            raise RuntimeError("存储服务未初始化")

        bucket = bucket_name or self._bucket

        try:
            # 获取对象的元数据 (异步方式)
            head_response = await self._s3_client.head_object(
                Bucket=bucket,
                Key=object_name
            )

            # logger.info(f"获取对象元数据: {head_response}")

            # 提取用户自定义元数据
            metadata = head_response.get("ResponseMetadata", {}).get("HTTPHeaders", {})
            metadata.update(head_response.get("Metadata", {}))

            return metadata
        except Exception as e:
            logger.error(f"获取对象元数据失败: {str(e)}")
            raise

    def _guess_content_type(self, file_path: str) -> str:
        """根据文件扩展名猜测内容类型

        Args:
            file_path: 文件路径

        Returns:
            内容类型
        """
        ext = os.path.splitext(file_path.lower())[1]
        content_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
            '.mp4': 'video/mp4',
            '.avi': 'video/x-msvideo',
            '.mov': 'video/quicktime',
            '.pdf': 'application/pdf',
            '.txt': 'text/plain',
            '.html': 'text/html',
            '.css': 'text/css',
            '.js': 'application/javascript',
            '.json': 'application/json',
            '.xml': 'application/xml',
        }
        return content_types.get(ext, 'application/octet-stream')

    def _build_url(self, object_name: str, bucket_name: Optional[str] = None) -> str:
        """构建对象URL

        Args:
            object_name: 对象名称/键
            bucket_name: 存储桶名称，如果为None则使用默认桶

        Returns:
            对象的公共访问URL
        """
        bucket = bucket_name or self._bucket

        # 构建公共URL
        if self._public_endpoint:
            if self._enabled_sub_domain:
                # 子域名模式下，不需要在路径中包含bucket
                return f"https://{bucket}.{self._public_endpoint}/{object_name}"
            else:
                # 非子域名模式下，需要在路径中包含bucket
                return f"https://{self._public_endpoint}/{bucket}/{object_name}"
        else:
            # 根据是否启用子域名模式构建不同格式的URL
            if self._enabled_sub_domain:
                # 从endpoint中提取域名部分，去掉协议和端口
                endpoint = self._endpoint.lower()
                if '://' in endpoint:
                    endpoint = endpoint.split('://', 1)[1]
                if '/' in endpoint:
                    endpoint = endpoint.split('/', 1)[0]
                if ':' in endpoint:
                    endpoint = endpoint.split(':', 1)[0]
                return f"https://{bucket}.{endpoint}/{object_name}"
            else:
                return f"{self._endpoint}/{bucket}/{object_name}"

    async def list_objects(self, prefix: Optional[str] = None, bucket_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取存储桶中的对象列表

        Args:
            prefix: 前缀过滤器
            bucket_name: 存储桶名称，如果为None则使用默认桶

        Returns:
            对象列表
        """
        if not self._s3_client:
            logger.error("存储服务未初始化")
            raise RuntimeError("存储服务未初始化")

        bucket = bucket_name or self._bucket

        try:
            # 准备参数
            params = {
                'Bucket': bucket
            }

            if prefix:
                params['Prefix'] = prefix

            # 获取对象列表 (异步方式)
            response = await self._s3_client.list_objects_v2(**params)

            # 提取对象信息
            objects = []
            if 'Contents' in response:
                objects = response['Contents']

            return objects
        except Exception as e:
            logger.error(f"获取对象列表失败: {str(e)}")
            raise

    async def list_objects_with_exclusion(self, prefix: Optional[str] = None, exclude_prefix: Optional[str] = None,
                                         bucket_name: Optional[str] = None, max_keys: int = 1000,
                                         continuation_token: Optional[str] = None) -> Dict[str, Any]:
        """获取存储桶中的对象列表，并排除特定前缀的对象，支持原生分页

        Args:
            prefix: 前缀过滤器
            exclude_prefix: 要排除的前缀，以此前缀开头的对象将被过滤掉
            bucket_name: 存储桶名称，如果为None则使用默认桶
            max_keys: 每次请求返回的最大对象数量，默认1000（S3 API的最大值）
            continuation_token: 用于分页的令牌，从上一次调用返回的NextContinuationToken获取

        Returns:
            包含以下字段的字典:
            - objects: 过滤后的对象列表
            - next_token: 下一页的令牌，如果没有更多页面则为None
            - is_truncated: 是否还有更多对象
        """
        if not self._s3_client:
            logger.error("存储服务未初始化")
            raise RuntimeError("存储服务未初始化")

        bucket = bucket_name or self._bucket

        try:
            # 准备参数
            params = {
                'Bucket': bucket,
                'MaxKeys': max_keys
            }

            if prefix:
                params['Prefix'] = prefix

            # 如果提供了continuation_token，添加到参数中
            if continuation_token:
                params['ContinuationToken'] = continuation_token

            # 调用S3 API获取对象列表
            response = await self._s3_client.list_objects_v2(**params)

            # 处理返回的对象
            filtered_objects = []
            if 'Contents' in response:
                if exclude_prefix:
                    # 过滤掉以exclude_prefix开头的对象
                    filtered_objects = [obj for obj in response['Contents']
                                      if not obj['Key'].startswith(exclude_prefix)]
                else:
                    filtered_objects = response['Contents']

            # 检查是否还有更多对象
            is_truncated = response.get('IsTruncated', False)
            next_token = response.get('NextContinuationToken') if is_truncated else None

            # 返回结果
            return {
                'objects': filtered_objects,
                'next_token': next_token,
                'is_truncated': is_truncated
            }
        except Exception as e:
            logger.error(f"获取对象列表失败: {str(e)}")
            raise

    async def move_object(
        self,
        source_object_name: str,
        target_object_name: str,
        bucket_name: Optional[str] = None,
        keep_metadata: bool = True
    ) -> str:
        """在同一存储桶内移动对象（修改对象键）

        Args:
            source_object_name: 源对象名称/键
            target_object_name: 目标对象名称/键
            bucket_name: 存储桶名称，如果为None则使用默认桶
            keep_metadata: 是否保留源对象的元数据

        Returns:
            移动后对象的公共URL
        """
        if not self._s3_client:
            logger.error("存储服务未初始化")
            raise RuntimeError("存储服务未初始化")

        bucket = bucket_name or self._bucket

        try:
            # 获取源对象的元数据
            metadata = {}
            content_type = None

            if keep_metadata:
                try:
                    # 获取源对象的元数据 (异步方式)
                    head_response = await self._s3_client.head_object(
                        Bucket=bucket,
                        Key=source_object_name
                    )

                    # 提取用户自定义元数据
                    if 'Metadata' in head_response:
                        metadata = head_response['Metadata']

                    # 提取内容类型
                    if 'ContentType' in head_response:
                        content_type = head_response['ContentType']
                except Exception as e:
                    logger.warning(f"获取源对象元数据失败，将使用默认元数据: {str(e)}")

            # 构建复制源
            copy_source = {
                'Bucket': bucket,
                'Key': source_object_name
            }

            # 准备复制参数
            copy_args = {}
            if metadata:
                copy_args['Metadata'] = metadata
                copy_args['MetadataDirective'] = 'REPLACE'

            if content_type:
                copy_args['ContentType'] = content_type

            logger.info(f"移动对象: {source_object_name} -> {target_object_name}")
            # 复制对象 (异步方式)
            await self._s3_client.copy_object(
                Bucket=bucket,
                Key=target_object_name,
                CopySource=copy_source,
                **copy_args
            )

            # 删除源对象 (异步方式)
            await self._s3_client.delete_object(
                Bucket=bucket,
                Key=source_object_name
            )

            # 构建新对象的URL
            url = self._build_url(target_object_name, bucket_name)

            return url
        except Exception as e:
            logger.error(f"移动对象失败: {str(e)}")
            raise

