"""
数据字段类型 - 提供自定义字段类型
"""

from typing import Any, Annotated
from bson import ObjectId
from pydantic import BeforeValidator, PlainSerializer

# 定义验证函数
def validate_object_id(v: Any) -> str:
    """验证并转换ObjectId"""
    if isinstance(v, ObjectId):
        return str(v)  # 转换为字符串
    elif isinstance(v, str) and ObjectId.is_valid(v):  # 使用ObjectId.is_valid检查有效性
        return v
    raise ValueError(f"无效的ObjectId: {v}")

# 定义序列化函数
def serialize_object_id(v: str) -> str:
    """序列化ObjectId为字符串"""
    return str(v)

# 使用Pydantic v2的Annotated类型
ObjectIdField = Annotated[
    str,
    BeforeValidator(validate_object_id),
    PlainSerializer(serialize_object_id, return_type=str),
]

# # 为了向后兼容，保留原来的类，但使用新的实现
# class ObjectIdFieldLegacy(str):
#     """ObjectId字段类型，用于处理MongoDB的ObjectId (向后兼容)

#     用法示例:
#     ```
#     from pydantic import BaseModel
#     from typing import Optional
#     from nebula.core.infrastructure.fields import ObjectIdField

#     class MyModel(BaseModel):
#         id: Optional[ObjectIdField] = None
#     ```
#     """
#     @classmethod
#     def __get_validators__(cls):
#         yield cls.validate

#     @classmethod
#     def validate(cls, v, *args, **kwargs):
#         return validate_object_id(v)