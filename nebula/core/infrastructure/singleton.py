"""
单例模式工具
"""

from typing import Any, Dict, Type
from functools import wraps

def singleton(cls):
    """
    单例模式装饰器
    
    用于确保一个类只有一个实例，并提供全局访问点
    
    Args:
        cls: 要装饰的类
        
    Returns:
        获取单例实例的函数
    """
    instances: Dict[Type, Any] = {}
    
    @wraps(cls)
    def get_instance(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]
    
    return get_instance 