"""
日志系统模块 - 提供基本的日志记录功能
"""

from functools import wraps
import logging
import json
import traceback
import sys
import os
from typing import Dict, Any, Optional
import uuid
from contextvars import ContextVar
import time
from contextlib import asynccontextmanager, contextmanager

# 存储控制台处理器的全局变量
console_handler = None
formatter = None


# 设置一个基本的日志配置，在应用上下文初始化前使用
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[logging.StreamHandler()]
)


# 上下文变量，用于跟踪请求/任务
request_id_var: ContextVar[str] = ContextVar('request_id', default='')
user_id_var: ContextVar[Optional[str]] = ContextVar('user_id', default=None)
correlation_id_var: ContextVar[str] = ContextVar('correlation_id', default='')




class JsonFormatter(logging.Formatter):
    """JSON格式日志格式器"""

    def format(self, record):
        """将日志记录格式化为JSON"""
        log_data = {
            "timestamp": self.formatTime(record),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": traceback.format_exception(*record.exc_info)
            }

        return json.dumps(log_data, ensure_ascii=False)

class TextFormatter(logging.Formatter):
    """文本格式日志格式器"""
    # 基本构造函数，支持自定义格式
    def __init__(self, fmt=None, datefmt=None):
        if fmt is None:
            fmt = '%(asctime)s [%(levelname)s] %(name)s - %(message)s'
        if datefmt is None:
            datefmt = '%Y-%m-%d %H:%M:%S'
        super().__init__(fmt=fmt, datefmt=datefmt)

class ExceptionFilter(logging.Filter):
    """用于过滤带有异常信息的日志记录"""

    def __init__(self, include_exceptions=True):
        """
        初始化过滤器

        Args:
            include_exceptions: 如果为True，只包含带有异常的日志；如果为False，排除带有异常的日志
        """
        super().__init__()
        self.include_exceptions = include_exceptions

    def filter(self, record):
        """过滤带有exc_info的日志记录"""
        has_exception = record.exc_info is not None
        return has_exception if self.include_exceptions else not has_exception

def setup_logging(level=logging.INFO, log_file=None, log_format="text", text_format=None, date_format=None, stacktrace_file=None, console_show_exc=False):
    """设置应用日志系统

    Args:
        level: 日志级别
        log_file: 日志文件路径
        log_format: 日志格式，可选json或text
        text_format: 文本日志格式字符串
        date_format: 日期格式字符串
        stacktrace_file: 堆栈跟踪日志文件路径
        console_show_exc: 控制台是否显示异常堆栈信息，默认为False
    """
    # 清除所有现有处理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 设置级别
    root_logger.setLevel(level)

    # 创建格式器
    global formatter
    if log_format.lower() == "json":
        formatter = JsonFormatter()
    else:
        formatter = TextFormatter(fmt=text_format, datefmt=date_format)

    global console_handler
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)

    print ("------setup_logging------")
    print (console_handler)

    # 如果设置为不在控制台显示异常堆栈且指定了堆栈跟踪文件，则添加过滤器
    if not console_show_exc and stacktrace_file:
        console_handler.addFilter(ExceptionFilter(include_exceptions=False))

    root_logger.addHandler(console_handler)

    # 如果指定了日志文件，添加文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        # 创建常规日志文件处理器，排除带有异常信息的日志
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        if stacktrace_file:
            # 如果有堆栈跟踪文件，则常规日志排除异常信息
            file_handler.addFilter(ExceptionFilter(include_exceptions=False))
        root_logger.addHandler(file_handler)

    # 如果指定了堆栈跟踪日志文件，添加专门的处理器
    if stacktrace_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(stacktrace_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        # 创建专门的异常日志文件处理器
        stacktrace_handler = logging.FileHandler(stacktrace_file, encoding='utf-8')
        stacktrace_handler.setFormatter(formatter)
        stacktrace_handler.addFilter(ExceptionFilter(include_exceptions=True))
        root_logger.addHandler(stacktrace_handler)

    return root_logger

def get_logger(name):
    """获取日志记录器"""
    return logging.getLogger(name)

def log_context(func):
    """日志上下文装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # 设置请求上下文
        request_id = str(uuid.uuid4())
        request_id_var.set(request_id)

        logger = logging.getLogger(func.__module__)

        # 过滤敏感参数
        safe_kwargs = {k: '***' if k in ('password', 'token', 'secret') else str(v)[:100]
                      for k, v in kwargs.items()}

        start_time = time.perf_counter()
        try:
            result = await func(*args, **kwargs)
            elapsed = time.perf_counter() - start_time
            logger.info(f"完成执行 {func.__name__} ({int(elapsed * 1000)}ms)", extra={
                "elapsed_ms": int(elapsed * 1000),
                "function": func.__name__,
                "parameters": safe_kwargs
            })
            return result
        except Exception as e:
            elapsed = time.perf_counter() - start_time
            logger.error(f"执行 {func.__name__} 失败", exc_info=True, extra={
                "elapsed_ms": int(elapsed * 1000),
                "error_type": e.__class__.__name__,
                "function": func.__name__,
                "parameters": safe_kwargs
            })
            raise

    return wrapper


@asynccontextmanager
async def calculate_time(operation_name: str, microsecond: bool = True, log_level: int = logging.INFO, logger: logging.Logger = None):
    """
    异步上下文管理器，用于计算操作执行时间

    用法:
    ```
    async with calculate_time("处理消息"):
        # 异步操作
    ```

    Args:
        operation_name: 操作名称，用于日志记录
        unit: 时间单位，默认为"ms"（毫秒）
        log_level: 日志级别，默认为INFO
        logger: 日志记录器，默认为None，表示使用当前模块的日志记录器

    Yields:
        None
    """
    if logger is None:
        logger = get_logger(__name__)
    start_time = time.perf_counter()
    try:
        yield
    finally:
        t = time.perf_counter() - start_time
        elapsed = int(t*1000) if microsecond else int(t*1000)/1000
        unit = "ms" if microsecond else "s"
        logger.log(log_level, f"<{operation_name}> (耗时: {elapsed}{unit})")



