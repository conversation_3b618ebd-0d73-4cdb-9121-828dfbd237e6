"""
配置管理模块 - 提供统一的配置管理功能
"""

from pydantic import Field, BaseModel
from pydantic_settings import BaseSettings
from functools import lru_cache
import os
import logging
from typing import Optional, Dict, Any, List, Union, Tuple
from enum import Enum


# 环境相关配置
class Environment(str, Enum):
    """环境类型枚举"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"

# 基础配置类
class BaseConfigSettings(BaseModel):
    """基础配置类，提供通用功能"""

    @classmethod
    def from_env(cls, **defaults):
        """
        从环境变量创建配置实例

        Args:
            defaults: 默认值

        Returns:
            配置实例
        """
        instance = cls()
        for field_name, field in cls.__fields__.items():
            env_name = field.field_info.extra.get('env')
            if env_name and env_name in os.environ:
                setattr(instance, field_name, os.environ[env_name])
        return instance

# 具体配置类
class LoggingSettings(BaseConfigSettings):
    """日志配置"""
    level: str = Field("INFO", env="LOG_LEVEL")
    file: str = Field("logs/{app}_{env}.log", env="LOG_FILE")
    stacktrace_file: str = Field("logs/{app}_{env}_stacktrace.log", env="LOG_STACKTRACE_FILE")
    console_show_exc: bool = Field(False, env="LOG_CONSOLE_SHOW_EXC")
    max_size_mb: int = Field(10, env="LOG_MAX_SIZE_MB")
    backup_count: int = Field(5, env="LOG_BACKUP_COUNT")
    format: str = Field("json", env="LOG_FORMAT")
    text_format: str = Field("%(asctime)s [%(levelname)s] %(name)s - %(message)s", env="LOG_TEXT_FORMAT")
    date_format: str = Field("%Y-%m-%d %H:%M:%S", env="LOG_DATE_FORMAT")

class RedisSettings(BaseConfigSettings):
    """Redis配置"""
    host: str = Field("localhost", env="REDIS_HOST")
    port: int = Field(6379, env="REDIS_PORT")
    username: Optional[str] = Field(None, env="REDIS_USERNAME")
    password: Optional[str] = Field(None, env="REDIS_PASSWORD")
    db: int = Field(0, env="REDIS_DB")
    pool_size: int = Field(10, env="REDIS_POOL_SIZE")
    socket_timeout: int = Field(5, env="REDIS_TIMEOUT")
    socket_connect_timeout: int = Field(5, env="REDIS_CONNECT_TIMEOUT")

class RabbitMQSettings(BaseConfigSettings):
    """RabbitMQ配置"""
    host: str = Field("localhost", env="RABBITMQ_HOST")
    port: int = Field(5672, env="RABBITMQ_PORT")
    username: str = Field("guest", env="RABBITMQ_USER")
    password: str = Field("guest", env="RABBITMQ_PASSWORD")
    vhost: str = Field("/", env="RABBITMQ_VHOST")
    connection_attempts: int = Field(3, env="RABBITMQ_CONNECTION_ATTEMPTS")
    retry_delay: int = Field(5, env="RABBITMQ_RETRY_DELAY")
    heartbeat: int = Field(60, env="RABBITMQ_HEARTBEAT")

    # 延迟消息配置 - 使用rabbitmq_delayed_message_exchange插件
    delayed_exchange: str = Field("nebula.delayed", env="RABBITMQ_DELAYED_EXCHANGE")  # 延迟交换机名称
    delayed_exchange_type: str = Field("x-delayed-message", env="RABBITMQ_DELAYED_EXCHANGE_TYPE")  # 延迟交换机类型
    max_retry_count: int = Field(3, env="RABBITMQ_MAX_RETRY_COUNT")  # 最大重试次数
    retry_intervals: List[int] = Field(default_factory=lambda: [60, 300, 1800], env="RABBITMQ_RETRY_INTERVALS")  # 默认重试间隔：1分钟、5分钟、30分钟

class S3Settings(BaseConfigSettings):
    """S3存储配置"""
    access_key: str = Field("", env="MINIO_ACCESS_KEY")
    secret_key: str = Field("", env="MINIO_SECRET_KEY")
    region: str = Field("", env="MINIO_REGION")
    endpoint: str = Field("", env="MINIO_ENDPOINT")
    frontend: str = Field("", env="MINIO_FRONTEND")
    bucket: str = Field("nebula", env="MINIO_BUCKET")
    enabled_sub_domain: bool = Field(False, env="MINIO_ENABLED_SUB_DOMAIN")

class StorageSettings(BaseConfigSettings):
    """存储配置"""
    type: str = Field("s3", env="STORAGE_TYPE")
    s3: S3Settings = S3Settings()

class ApiSettings(BaseConfigSettings):
    """API服务配置"""
    host: str = Field("0.0.0.0", env="API_HOST")
    port: int = Field(8080, env="API_PORT")
    reload: bool = Field(True, env="API_RELOAD")
    workers: int = Field(4, env="API_WORKERS")
    cors_origins: List[str] = ["*"]

class WebSocketSettings(BaseConfigSettings):
    """WebSocket配置"""
    host: str = Field("0.0.0.0", env="WS_HOST")
    port: int = Field(8092, env="WS_PORT")
    ping_interval: int = Field(30, env="WS_PING_INTERVAL")
    ping_timeout: int = Field(10, env="WS_PING_TIMEOUT")

class FeaturesSettings(BaseConfigSettings):
    """特性开关配置"""
    enable_websocket: bool = Field(True, env="ENABLE_WEBSOCKET")
    enable_message_queue: bool = Field(True, env="ENABLE_MESSAGE_QUEUE")
    enable_storage: bool = Field(True, env="ENABLE_STORAGE")
    enable_cache: bool = Field(True, env="ENABLE_CACHE")

class JWTSettings(BaseConfigSettings):
    """JWT配置"""
    secret_key: str = Field("nebula-secret-key", env="JWT_SECRET")
    algorithm: str = Field("HS256", env="JWT_ALGORITHM")
    expire_minutes: int = Field(1440, env="JWT_EXPIRE_MINUTES")

class GenAISettings(BaseConfigSettings):
    """GenAI配置"""
    bucket: str = Field("", env="GENAI_BUCKET")
    credentials: str = Field("google.apikey.json", env="GENAI_CREDENTIALS")
    project: str = Field("", env="GENAI_PROJECT")
    location: str = Field("us-central1", env="GENAI_LOCATION")
    model: str = Field("gemini-1.0-pro", env="GENAI_MODEL")
    temperature: float = Field(1.0, env="GENAI_TEMPERATURE")
    top_p: float = Field(0.95, env="GENAI_TOP_P")
    max_output_tokens: int = Field(8192, env="GENAI_MAX_OUTPUT_TOKENS")
    response_modalities: List[str] = Field(default_factory=lambda: ["TEXT"])
    safety_settings: List[Dict[str, str]] = Field(default_factory=list)

class PlaywrightSettings(BaseConfigSettings):
    """Playwright配置"""
    browser_type: str = Field("chromium", env="PLAYWRIGHT_BROWSER_TYPE")
    endpoint: str = Field("", env="PLAYWRIGHT_ENDPOINT")

    # 超时配置
    page_timeout: int = Field(30000, env="PLAYWRIGHT_PAGE_TIMEOUT")  # 页面加载超时(ms)
    element_timeout: int = Field(10000, env="PLAYWRIGHT_ELEMENT_TIMEOUT")  # 元素等待超时(ms)
    script_timeout: int = Field(30000, env="PLAYWRIGHT_SCRIPT_TIMEOUT")  # 脚本执行超时(ms)

    # 重试配置
    max_retries: int = Field(3, env="PLAYWRIGHT_MAX_RETRIES")  # 最大重试次数
    retry_delay: float = Field(1.0, env="PLAYWRIGHT_RETRY_DELAY")  # 重试延迟(秒)
    retry_backoff: float = Field(2.0, env="PLAYWRIGHT_RETRY_BACKOFF")  # 重试退避倍数

    # 并发控制
    max_concurrent_pages: int = Field(5, env="PLAYWRIGHT_MAX_CONCURRENT_PAGES")  # 最大并发页面数
    page_pool_size: int = Field(3, env="PLAYWRIGHT_PAGE_POOL_SIZE")  # 页面池大小

    # 用户代理配置
    user_agents: List[str] = Field(
        default_factory=lambda: [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ],
        env="PLAYWRIGHT_USER_AGENTS"
    )

    # 代理配置
    proxy_server: str = Field("", env="PLAYWRIGHT_PROXY_SERVER")  # 代理服务器地址
    proxy_username: str = Field("", env="PLAYWRIGHT_PROXY_USERNAME")  # 代理用户名
    proxy_password: str = Field("", env="PLAYWRIGHT_PROXY_PASSWORD")  # 代理密码

    # 资源加载控制
    block_images: bool = Field(False, env="PLAYWRIGHT_BLOCK_IMAGES")  # 阻止图片加载
    block_stylesheets: bool = Field(False, env="PLAYWRIGHT_BLOCK_STYLESHEETS")  # 阻止CSS加载
    block_fonts: bool = Field(False, env="PLAYWRIGHT_BLOCK_FONTS")  # 阻止字体加载
    block_media: bool = Field(False, env="PLAYWRIGHT_BLOCK_MEDIA")  # 阻止媒体加载

    # 等待策略
    wait_until: str = Field("domcontentloaded", env="PLAYWRIGHT_WAIT_UNTIL")  # 页面等待策略

    # 健康检查
    health_check_interval: int = Field(300, env="PLAYWRIGHT_HEALTH_CHECK_INTERVAL")  # 健康检查间隔(秒)

    # 缓存配置
    enable_cache: bool = Field(False, env="PLAYWRIGHT_ENABLE_CACHE")  # 启用缓存
    cache_ttl: int = Field(300, env="PLAYWRIGHT_CACHE_TTL")  # 缓存TTL(秒)

class BrowserSettings(BaseConfigSettings):
    """Browser配置"""
    playwright: PlaywrightSettings = PlaywrightSettings()

class VideoSettings(BaseConfigSettings):
    """Video配置"""
    bucket: str = Field("", env="VIDEO_BUCKET")
    cover_path: str = Field("", env="VIDEO_COVER_PATH")
    video_path: str = Field("", env="VIDEO_VIDEO_PATH")
    youtube_proxy: str = Field("", env="VIDEO_YOUTUBE_PROXY")
    twitter_proxy: str = Field("", env="VIDEO_TWITTER_PROXY")
    download_timeout: int = Field(1800, env="VIDEO_DOWNLOAD_TIMEOUT")

class TelegramSettings(BaseConfigSettings):
    """Telegram配置"""
    token: str = Field("", env="TELEGRAM_TOKEN")
    webhook_url: str = Field("", env="TELEGRAM_WEBHOOK_URL")
    webhook_path: str = Field("", env="TELEGRAM_WEBHOOK_PATH")
    allowed_user_ids: List[int] = Field(default_factory=list, env="TELEGRAM_ALLOWED_USER_IDS")
    s3_images_bucket: str = Field("", env="TELEGRAM_S3_IMAGES_BUCKET")

class AppSettings(BaseConfigSettings):
    """应用通用配置"""
    debug: bool = Field(False, env="DEBUG")
    secret_key: str = Field("nebula-app-secret-key", env="SECRET_KEY")

class MongoDBSettings(BaseConfigSettings):
    """MongoDB配置"""
    # URI方式配置
    uri: Optional[str] = Field(default=None, env="MONGODB_URI")

    # 传统方式配置
    host: str = Field(default="localhost", env="MONGODB_HOST")
    port: int = Field(default=27017, env="MONGODB_PORT")
    username: Optional[str] = Field(default=None, env="MONGODB_USERNAME")
    password: Optional[str] = Field(default=None, env="MONGODB_PASSWORD")
    database: str = Field(default="nebula", env="MONGODB_DATABASE")

    # 连接池配置
    min_pool_size: int = Field(default=1, env="MONGODB_MIN_POOL_SIZE")
    max_pool_size: int = Field(default=10, env="MONGODB_MAX_POOL_SIZE")

    def get_connection_uri(self) -> str:
        """获取MongoDB连接URI"""
        if self.uri:
            # 如果uri有值，解析uri并更新相关属性
            from urllib.parse import urlparse
            parsed_uri = urlparse(self.uri)

            # 解析用户名和密码
            if '@' in parsed_uri.netloc:
                userinfo, hostport = parsed_uri.netloc.split('@', 1)
                if ':' in userinfo:
                    self.username, self.password = userinfo.split(':', 1)
            else:
                hostport = parsed_uri.netloc

            # 解析主机和端口
            if ':' in hostport:
                self.host, port_str = hostport.split(':', 1)
                self.port = int(port_str)
            else:
                self.host = hostport

            return self.uri

        # 构建认证部分
        auth = ""
        if self.username and self.password:
            auth = f"{self.username}:{self.password}@"

        # 构建完整的URI
        return f"mongodb://{auth}{self.host}:{self.port}"

class MemoSettings(BaseConfigSettings):
    """Memo配置"""
    bucket: str = Field("memo", env="MEMO_BUCKET")
    file_path: str = Field("files/{unique_id}{ext}", env="MEMO_FILE_PATH")

class TaskiqSettings(BaseConfigSettings):
    """Taskiq配置"""
    broker_url: str = Field("redis://localhost:6379/0", env="TASKIQ_BROKER_URL")
    result_backend_url: str = Field("redis://localhost:6379/0", env="TASKIQ_RESULT_BACKEND_URL")
    queue_name: str = Field("nebula", env="TASKIQ_QUEUE_NAME")
    routing_key: str = Field("#", env="TASKIQ_ROUTING_KEY")
    max_retry_count: int = Field(3, env="TASKIQ_MAX_RETRY_COUNT")
    retry_intervals: List[int] = Field(default_factory=lambda: [60, 300, 1800], env="TASKIQ_RETRY_INTERVALS")
    worker_concurrency: int = Field(4, env="TASKIQ_WORKER_CONCURRENCY")
    task_timeout: int = Field(3600, env="TASKIQ_TASK_TIMEOUT")  # 默认任务超时时间（秒）
    result_ttl: int = Field(86400, env="TASKIQ_RESULT_TTL")  # 结果保存时间（秒）

class Settings(BaseModel):
    """主配置类，聚合所有配置"""
    logging: LoggingSettings = LoggingSettings()
    redis: RedisSettings = RedisSettings()
    rabbitmq: RabbitMQSettings = RabbitMQSettings()
    storage: StorageSettings = StorageSettings()
    api: ApiSettings = ApiSettings()
    websocket: WebSocketSettings = WebSocketSettings()
    features: FeaturesSettings = FeaturesSettings()
    app: AppSettings = AppSettings()
    jwt: JWTSettings = JWTSettings()
    mongodb: MongoDBSettings = Field(default_factory=MongoDBSettings)
    genai: GenAISettings = GenAISettings()
    browser: BrowserSettings = BrowserSettings()
    video: VideoSettings = VideoSettings()
    telegram: TelegramSettings = TelegramSettings()
    memo: MemoSettings = MemoSettings()
    taskiq: TaskiqSettings = TaskiqSettings()

    def load_from_file(self, path: str):
        """从配置文件加载配置"""
        if not os.path.exists(path):
            logging.warning(f"配置文件不存在: {path}")
            return

        try:
            import yaml
            with open(path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            if not config_data:
                logging.warning(f"配置文件为空: {path}")
                return

            # 加载配置
            self._load_config_data(config_data)
            logging.info(f"已加载配置文件: {path}")
        except Exception as e:
            logging.error(f"加载配置文件失败: {str(e)}")

    def _load_config_data(self, config_data: Dict[str, Any]):
        """加载配置数据

        Args:
            config_data: 配置数据
        """
        for key, value in config_data.items():
            if not hasattr(self, key):
                logging.warning(f"未知配置项: {key}")
                continue

            current_setting = getattr(self, key)
            if not isinstance(current_setting, BaseModel):
                setattr(self, key, value)
                continue

            if not isinstance(value, dict):
                logging.warning(f"配置项 {key} 不是字典类型")
                continue

            # 处理嵌套配置
            self._update_nested_config(current_setting, value)

    def _update_nested_config(self, config_obj: BaseModel, config_data: Dict[str, Any], prefix: str = ""):
        """更新嵌套配置

        Args:
            config_obj: 配置对象
            config_data: 配置数据
            prefix: 配置前缀，用于日志
        """
        for key, value in config_data.items():
            if not hasattr(config_obj, key):
                logging.warning(f"未知配置项: {prefix + '.' if prefix else ''}{key}")
                continue

            # 获取当前配置值
            current_value = getattr(config_obj, key)

            # 如果当前值是BaseModel且新值是字典，则递归更新
            if isinstance(current_value, BaseModel) and isinstance(value, dict):
                new_prefix = f"{prefix + '.' if prefix else ''}{key}"
                self._update_nested_config(current_value, value, new_prefix)
            else:
                # 直接更新值
                setattr(config_obj, key, value)

@lru_cache()
def get_settings() -> Settings:
    """获取配置单例，支持缓存"""
    env = get_environment()
    settings = Settings()

    # 配置文件搜索路径
    config_paths = [
        "config.yaml",                                # 当前目录默认配置
        f"config.{env.value}.yaml",                   # 环境特定配置
        os.path.expanduser("~/.nebula/config.yaml"),  # 用户目录配置
        os.environ.get("NEBULA_CONFIG", "")           # 环境变量指定配置
    ]

    # 加载配置文件
    for path in config_paths:
        if path and os.path.exists(path):
            settings.load_from_file(path)

    return settings

def get_environment() -> Environment:
    """获取当前环境"""
    env = os.getenv("NEBULA_ENV", "development").lower()
    if env in [e.value for e in Environment]:
        return Environment(env)
    return Environment.DEVELOPMENT