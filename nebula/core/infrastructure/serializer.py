"""
消息序列化模块 - 提供统一的序列化/反序列化机制
"""

from enum import Enum
from typing import Any, Dict, Type, Optional, Union
import json
from datetime import datetime, date
import uuid
from pydantic import BaseModel
import logging

logger = logging.getLogger(__name__)

class SerializationFormat(str, Enum):
    """序列化格式枚举"""
    JSON = "json"
    MSGPACK = "msgpack"
    PICKLE = "pickle"

class Serializer:
    """统一的序列化/反序列化服务"""
    
    def __init__(self, default_format: SerializationFormat = SerializationFormat.JSON):
        """
        初始化序列化器
        
        Args:
            default_format: 默认序列化格式
        """
        self.default_format = default_format
        self._encoders = {
            datetime: lambda dt: dt.isoformat(),
            date: lambda d: d.isoformat(),
            uuid.UUID: lambda u: str(u),
            set: lambda s: list(s)
        }
        
    def register_encoder(self, type_: Type, encoder_func):
        """
        注册自定义类型的编码器
        
        Args:
            type_: 要注册的类型
            encoder_func: 编码函数
        """
        self._encoders[type_] = encoder_func
        
    def _default_encoder(self, obj):
        """
        默认编码器，处理内置类型无法序列化的对象
        
        Args:
            obj: 要序列化的对象
            
        Returns:
            可序列化的值
        """
        for base_type, encoder in self._encoders.items():
            if isinstance(obj, base_type):
                return encoder(obj)
        if isinstance(obj, BaseModel):
            return obj.dict()
        if isinstance(obj, Enum):
            return obj.value
        raise TypeError(f"Object of type {obj.__class__.__name__} is not serializable")
    
    def serialize(self, data: Any, format_: Optional[SerializationFormat] = None) -> bytes:
        """
        序列化数据为指定格式
        
        Args:
            data: 要序列化的数据
            format_: 序列化格式
            
        Returns:
            序列化后的字节数据
        """
        format_ = format_ or self.default_format
        
        try:
            if format_ == SerializationFormat.JSON:
                return json.dumps(data, default=self._default_encoder, ensure_ascii=False).encode('utf-8')
            elif format_ == SerializationFormat.MSGPACK:
                try:
                    import msgpack
                    return msgpack.packb(data, default=self._default_encoder)
                except ImportError:
                    logger.warning("msgpack库未安装，回退到JSON格式")
                    return json.dumps(data, default=self._default_encoder, ensure_ascii=False).encode('utf-8')
            elif format_ == SerializationFormat.PICKLE:
                try:
                    import pickle
                    return pickle.dumps(data)
                except ImportError:
                    logger.warning("pickle模块不可用，回退到JSON格式")
                    return json.dumps(data, default=self._default_encoder, ensure_ascii=False).encode('utf-8')
            else:
                raise ValueError(f"不支持的序列化格式: {format_}")
        except Exception as e:
            logger.error(f"序列化失败: {str(e)}", exc_info=True)
            # 回退到简单的JSON
            return json.dumps({"error": "序列化失败", "message": str(e)}).encode('utf-8')
            
    def deserialize(self, data: bytes, format_: Optional[SerializationFormat] = None) -> Any:
        """
        从指定格式反序列化数据
        
        Args:
            data: 要反序列化的数据
            format_: 序列化格式
            
        Returns:
            反序列化后的对象
        """
        if not data:
            return None
            
        format_ = format_ or self.default_format
        
        try:
            if format_ == SerializationFormat.JSON:
                return json.loads(data.decode('utf-8'))
            elif format_ == SerializationFormat.MSGPACK:
                try:
                    import msgpack
                    return msgpack.unpackb(data, raw=False)
                except ImportError:
                    logger.warning("msgpack库未安装，尝试以JSON格式解析")
                    return json.loads(data.decode('utf-8'))
            elif format_ == SerializationFormat.PICKLE:
                try:
                    import pickle
                    return pickle.loads(data)
                except ImportError:
                    logger.warning("pickle模块不可用，尝试以JSON格式解析")
                    return json.loads(data.decode('utf-8'))
            else:
                raise ValueError(f"不支持的序列化格式: {format_}")
        except Exception as e:
            logger.error(f"反序列化失败: {str(e)}", exc_info=True)
            return {"error": "反序列化失败", "message": str(e)}
            
    def from_model(self, model: BaseModel, format_: Optional[SerializationFormat] = None) -> bytes:
        """
        从Pydantic模型序列化
        
        Args:
            model: Pydantic模型实例
            format_: 序列化格式
            
        Returns:
            序列化后的字节数据
        """
        return self.serialize(model.dict(), format_)
        
    def to_model(self, data: bytes, model_class: Type[BaseModel], format_: Optional[SerializationFormat] = None) -> Union[BaseModel, Dict[str, Any]]:
        """
        反序列化为Pydantic模型
        
        Args:
            data: 要反序列化的数据
            model_class: Pydantic模型类
            format_: 序列化格式
            
        Returns:
            模型实例或错误信息
        """
        try:
            data_dict = self.deserialize(data, format_)
            if isinstance(data_dict, dict) and "error" in data_dict:
                return data_dict
            return model_class.parse_obj(data_dict)
        except Exception as e:
            logger.error(f"转换为模型失败: {str(e)}", exc_info=True)
            return {"error": "转换为模型失败", "message": str(e)}
        
    def serialize_to_str(self, data: Any) -> str:
        """
        序列化数据为字符串
        
        Args:
            data: 要序列化的数据
            
        Returns:
            序列化后的字符串
        """
        if self.default_format == SerializationFormat.JSON:
            return json.dumps(data, default=self._default_encoder, ensure_ascii=False)
        else:
            # 对于非JSON格式，先序列化再转为base64字符串
            import base64
            return base64.b64encode(self.serialize(data)).decode('utf-8')

# 创建全局序列化器实例
serializer = Serializer() 