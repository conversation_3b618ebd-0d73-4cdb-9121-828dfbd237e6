"""
消息服务
提供RabbitMQ消息队列服务
"""

import json
import logging
import asyncio
import datetime
from typing import Dict, List, Callable, Any, Optional, TypeVar, Generic, Type
import aio_pika
from nebula.core.protocol import (
    MessageEntity,
    CloudflareTaskMessage,
    WebNotification,
    MemoTaskMessage,
    VideoTaskMessage,
    TelegramTaskMessage
)
from nebula.core.infrastructure.config import get_settings, Settings
from nebula.core.infrastructure.exceptions import MessageError, TimeoutError, error_boundary
from nebula.core.infrastructure.logging import get_logger, log_context, calculate_time
from nebula.core.infrastructure.serializer import serializer
from nebula.core.infrastructure import provider
from nebula.core.infrastructure.cache import RedisCache

# 获取日志记录器
logger = get_logger(__name__)

T = TypeVar('T', bound=MessageEntity)

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj: Any) -> Any:
        if isinstance(obj, datetime.datetime):
            # 将 datetime 转换为字符串，例如：'2025-04-04 10:00:00'
            return obj.isoformat()
        return super().default(obj)

class MessageService:
    """消息服务类"""

    # Redis结果存储前缀
    REDIS_RESULT_PREFIX = "task:results:"

    # 消息类型映射
    _message_classes: Dict[str, Type[MessageEntity]] = {
        # CloudflareTaskMessage.get_topic(): CloudflareTaskMessage,
        # MemoTaskMessage.get_topic(): MemoTaskMessage,
        # VideoTaskMessage.get_topic(): VideoTaskMessage,
        WebNotification.get_topic(): WebNotification,
        # TelegramTaskMessage.get_topic(): TelegramTaskMessage,
        # 其他消息类型...
    }

    def __init__(self, settings: Settings = None):
        """
        初始化消息服务

        Args:
            settings: 配置对象，通过依赖注入注入
        """
        # 使用依赖注入获取配置
        self.settings = settings or get_settings()

        # 获取日志记录器
        self.logger = logger

        # RabbitMQ连接参数 - 从配置获取
        rabbitmq_config = self.settings.rabbitmq
        self.host = rabbitmq_config.host
        self.port = rabbitmq_config.port
        self.user = rabbitmq_config.username
        self.password = rabbitmq_config.password
        self.virtual_host = rabbitmq_config.vhost

        # RabbitMQ连接和通道
        self.connection = None
        self.channel = None

        # 消息回调函数字典
        self.message_callbacks: Dict[str, List[Callable]] = {}

        # 获取Redis缓存服务
        self.cache = provider.get(RedisCache)

        self._consuming = False  # 使用标志来控制消费状态

        logger.info("消息服务初始化完成")

    async def connect(self) -> None:
        """建立RabbitMQ连接"""
        # 使用异常边界处理异常
        async with error_boundary("连接RabbitMQ"):
            # 创建连接
            self.connection = await aio_pika.connect_robust(
                host=self.host,
                port=self.port,
                login=self.user,
                password=self.password,
                virtualhost=self.virtual_host
            )

            # 创建通道
            self.channel = await self.connection.channel()
            await self.channel.set_qos(prefetch_count=1)

            # 声明延迟交换机 (x-delayed-message类型)
            self.delayed_exchange = await self.channel.declare_exchange(
                self.settings.rabbitmq.delayed_exchange,
                self.settings.rabbitmq.delayed_exchange_type,
                durable=True,
                arguments={
                    'x-delayed-type': 'direct'  # 底层交换机类型为direct
                }
            )
            self.logger.info(f"已声明延迟交换机: {self.settings.rabbitmq.delayed_exchange}")

            # self.logger.info("已连接到RabbitMQ")
        return True

    async def ensure_connection(self) -> None:
        """确保RabbitMQ连接可用"""
        if not self.connection or self.connection.is_closed:
            await self.connect()

    async def close(self) -> None:
        """关闭RabbitMQ连接"""
        if self.connection and not self.connection.is_closed:
            await self.connection.close()
            self.logger.info("已关闭RabbitMQ连接")

    def register_callback(self, topic: str, callback: Callable) -> None:
        """
        注册消息回调函数

        Args:
            topic: 消息主题
            callback: 回调函数
        """
        if topic not in self.message_callbacks:
            self.message_callbacks[topic] = []
        self.message_callbacks[topic].append(callback)
        self.logger.info(f"已注册回调到主题 {topic}")

    async def _handle_message(self, message: aio_pika.IncomingMessage):
        """
        处理接收到的消息

        Args:
            message: aio_pika消息对象
        """
        try:
            # 解析消息 - 使用统一序列化器
            message_data = serializer.deserialize(message.body)
            topic = message_data.get("topic", "")

            if not topic:
                self.logger.error("消息缺少topic字段")
                await message.reject(requeue=False)
                return

            # 根据topic获取消息类
            if topic in self._message_classes:
                message_class = self._message_classes[topic]
                message_entity = message_class.parse_obj(message_data)

                # 调用对应主题的回调函数
                if topic in self.message_callbacks:
                    async with message.process():
                        try:
                            for callback in self.message_callbacks[topic]:
                                # 这里消息将进入任务执行器, 如果意外中断会丢消息
                                # 可以在任务内补齐可靠性
                                asyncio.create_task(callback(message_entity))
                        except Exception as e:
                            self.logger.error(f"执行回调失败: {str(e)}")
                            # 如果是消息格式错误，不要重新入队
                            if isinstance(e, (ValueError, TypeError, AttributeError)):
                                await message.reject(requeue=False)
                                self.logger.error("消息格式错误，已丢弃")
                            else:
                                await message.reject(requeue=True)
                                self.logger.error("处理失败，消息重新入队")

                    self.logger.info(f"** 调度消息已进入执行器 **: {topic}, task_id={message_entity.task_id}")
                else:
                    self.logger.warning(f"未找到主题 {topic} 的处理函数")
                    await message.reject(requeue=False)
            else:
                self.logger.error(f"未知的消息类型: {topic}")
                await message.reject(requeue=False)
        except Exception as e:
            self.logger.error(f"消息解析失败: {str(e)}")
            await message.reject(requeue=False)  # 如果解析失败，不要重新入队

    @log_context
    async def _start_consuming(self, topic: str) -> None:
        """
        开始消费指定主题的消息

        Args:
            topic: 消息主题
        """
        # 声明队列
        queue = await self.channel.declare_queue(
            topic,
            durable=True,
            arguments={
                'x-max-priority': 10  # 支持0-10的消息优先级
            }
        )

        # 将队列绑定到延迟交换机
        await queue.bind(
            self.delayed_exchange,
            routing_key=topic
        )
        self.logger.info(f"已将队列 {topic} 绑定到延迟交换机 {self.settings.rabbitmq.delayed_exchange}")

        # 开始消费消息
        await queue.consume(self._handle_message)
        self.logger.info(f"开始消费主题: {topic}")



    @log_context
    async def start_consuming_all(self) -> None:
        """开始消费所有注册的主题"""
        await self.ensure_connection()

        # 为每个注册的主题启动消费
        for topic in self.message_callbacks.keys():
            await self._start_consuming(topic)


    async def publish_queue_message(self, message: Dict[str, Any], priority: int = 0) -> None:
        """
        发布队列消息

        Args:
            message: 消息数据
            priority: 优先级(0-10)
        """
        async with error_boundary("发布队列消息"):
            await self.ensure_connection()
            # 验证消息格式
            if "topic" not in message:
                raise MessageError("消息缺少topic字段")

            topic = message["topic"]

            # 使用序列化器
            message_body = serializer.serialize(message)
            # 创建消息对象
            message_obj = aio_pika.Message(
                message_body,
                delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
                priority=priority
            )

            # 发布消息
            async with calculate_time(f"发布消息到队列 -> {topic}", logger=self.logger):
                await self.channel.default_exchange.publish(
                    message_obj,
                    routing_key=topic
                )
                self.logger.info(f"已发布消息到队列: {topic}")

    async def publish_typed_message(self, message_entity: MessageEntity) -> None:
        """
        发布类型化消息

        Args:
            message: 消息实体对象
        """

        # 将消息转换为字典
        message_dict = message_entity.to_message_dict()
        # 发布消息
        await self.publish_queue_message(message_dict)

    async def publish_delayed_message(self, message_entity: MessageEntity, error: Exception = None) -> None:
        """
        发布延迟消息，用于失败任务的重试

        Args:
            message_entity: 消息实体对象 (注意与message : T做区分)
            error: 导致失败的异常
        """
        # 增加重试次数
        message_entity.retry_count += 1

        # 设置上次失败原因
        if error:
            message_entity.last_error = str(error)

        # 标记为延迟消息
        message_entity.is_delayed = True

        # 检查是否超过最大重试次数
        if message_entity.retry_count > message_entity.max_retry_count:
            self.logger.warning(
                f"任务 {message_entity.task_id} 已达到最大重试次数 {message_entity.max_retry_count}，不再重试"
            )
            return

        # 获取当前重试索引，确保不超过配置的重试间隔数组长度
        retry_index = min(message_entity.retry_count - 1, len(self.settings.rabbitmq.retry_intervals) - 1)

        # 计算下次重试时间
        delay_seconds = self.settings.rabbitmq.retry_intervals[retry_index]
        message_entity.next_retry_time = datetime.datetime.now() + datetime.timedelta(seconds=delay_seconds)

        # 获取消息主题
        topic = message_entity.get_topic()

        # 将消息转换为字典
        message_dict = message_entity.to_message_dict()

        # 序列化消息
        message_body = serializer.serialize(message_dict)

        # 确保连接可用
        await self.ensure_connection()

        # 创建消息对象，添加x-delay头部
        message_obj = aio_pika.Message(
            message_body,
            delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
            priority=message_entity.priority,
            headers={'x-delay': delay_seconds * 1000}  # 延迟时间，单位为毫秒
        )

        # 发布到延迟交换机
        await self.delayed_exchange.publish(
            message_obj,
            routing_key=topic  # 直接使用主题作为路由键
        )

        self.logger.info(
            f"已发布延迟消息: task_id={message_entity.task_id}, topic={topic}, "
            f"retry_count={message_entity.retry_count}, delay={delay_seconds}秒, "
            f"next_retry_time={message_entity.next_retry_time.isoformat()}"
        )

    @log_context
    async def wait_task_result(self, task_id: str, timeout: int = 30) -> Dict[str, Any]:
        """等待任务结果"""
        async with error_boundary("等待任务结果"):
            try:
                result = await self.cache.wait_task_result(
                    task_id,
                    self.REDIS_RESULT_PREFIX,
                    timeout
                )
                logger.debug(f"获取到任务 {task_id} 的结果: {result}")
                return result
            except TimeoutError:
                logger.error(f"等待任务 {task_id} 结果超时")
                raise TimeoutError(f"等待任务 {task_id} 结果超时")

    @log_context
    async def publish_task_result(self, topic: str, task_id: str, failed: bool,errcode: int, msg: str, action: str, result: Dict[str, Any] = {}) -> None:
        """发布任务结果"""

        async with error_boundary("发布任务结果"):
            if result:
                result["event"] = action

            message_data = {
                "topic": topic,
                "timestamp": datetime.datetime.now().isoformat(),
                "metadata": {},
                "task_id": task_id,
                "failed": failed,
                "errcode": errcode,
                "msg": msg,
                "result": result
            }
            message = json.dumps(message_data, cls=CustomJSONEncoder)

            await self.cache.publish_task_result(
                task_id,
                self.REDIS_RESULT_PREFIX,
                message,
                expire=20
            )

            logger.debug(f"已发布任务 {task_id} 的结果到Redis", extra={
                "task_id": task_id,
                "failed": failed,
                "action": action
            })

# message_service的实例会在app_context中创建
