"""
异常处理模块 - 提供统一的异常定义和处理机制
"""

from typing import Dict, Any, Optional
from contextlib import asynccontextmanager, contextmanager
import logging

class NebulaException(Exception):
    """所有应用异常的基类"""
    def __init__(self, message: str, status_code: int = 500, details: Optional[Dict[str, Any]] = None, should_retry: bool = False):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        self.should_retry = should_retry  # 标记是否应该重试
        super().__init__(self.message)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典，用于序列化"""
        result = {
            "error": self.__class__.__name__,
            "message": self.message,
            "status_code": self.status_code
        }
        if self.details:
            result["details"] = self.details
        return result
    
    def __str__(self):
        return f"{self.__class__.__name__}: {self.message}"

class ConfigError(NebulaException):
    """配置相关错误"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=500, details=details)

class TimeoutError(NebulaException):
    """超时错误"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None, should_retry: bool = False):
        super().__init__(message, status_code=504, details=details, should_retry=should_retry)

class RuntimeError(NebulaException):
    """运行时错误"""
    def __init__(self, message: str, original_error=None, details: Optional[Dict[str, Any]] = None):
        self.original_error = original_error
        if original_error and not details:
            details = {"original_error": str(original_error)}
        super().__init__(message, status_code=500, details=details)

class DatabaseError(NebulaException):
    """数据库操作错误"""
    def __init__(self, message: str, original_error=None, details: Optional[Dict[str, Any]] = None):
        self.original_error = original_error
        if original_error and not details:
            details = {"original_error": str(original_error)}
        super().__init__(message, status_code=500, details=details)

class TaskError(NebulaException):
    """任务处理错误"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=500, details=details)

class AuthenticationError(NebulaException):
    """认证相关错误"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=401, details=details)

class AuthorizationError(NebulaException):
    """授权相关错误"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=403, details=details)

class ValidationError(NebulaException):
    """输入验证错误"""
    def __init__(self, message: str, field_errors: Optional[Dict[str, str]] = None):
        details = {"field_errors": field_errors} if field_errors else {}
        super().__init__(message, status_code=400, details=details)

class NotFoundError(NebulaException):
    """资源未找到错误"""
    def __init__(self, resource_type: str, resource_id: str):
        message = f"{resource_type}: '{resource_id}' not found"
        details = {"resource_type": resource_type, "resource_id": resource_id}
        super().__init__(message, status_code=404, details=details)

class DuplicateError(NebulaException):
    """资源重复错误"""
    def __init__(self, resource_type: str, identifier: str):
        message = f"{resource_type} with identifier '{identifier}' already exists"
        details = {"resource_type": resource_type, "identifier": identifier}
        super().__init__(message, status_code=409, details=details)

class ExternalServiceError(NebulaException):
    """外部服务调用错误"""
    def __init__(self, service_name: str, message: str, details: Optional[Dict[str, Any]] = None, should_retry: bool = False):
        if not details:
            details = {}
        details["service_name"] = service_name
        super().__init__(message, status_code=502, details=details, should_retry=should_retry)

class MessageError(NebulaException):
    """消息处理错误"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=500, details=details)

class BusinessError(NebulaException):
    """业务逻辑错误，表示预期内的业务处理结果"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None, should_retry: bool = False):
        super().__init__(message, status_code=422, details=details, should_retry=should_retry)  # 使用422 Unprocessable Entity表示业务逻辑错误


class BrowserException(ExternalServiceError):
    """浏览器服务异常"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None, should_retry: bool = True):
        super().__init__("BrowserService", message, details, should_retry)

class YoutubeException(ExternalServiceError):
    """YouTube服务异常"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None, should_retry: bool = True):
        super().__init__("YouTubeService", message, details, should_retry)

class DebugRetryException(NebulaException):
    """调试重试异常，仅在开发模式下使用"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None, should_retry: bool = True):
        super().__init__(message, status_code=500, details=details, should_retry=should_retry)

@asynccontextmanager
async def error_boundary(context_name: str):
    """
    提供异步错误边界的上下文管理器

    用法:
    ```
    async with error_boundary("用户注册"):
        # 异步业务逻辑
    ```
    """
    try:
        yield
    except NebulaException as e:
        # 应用内部定义的异常，直接传递
        raise
    except Exception as e:
        # 未预期的异常，转换为应用异常
        logging.exception(f"{context_name}发生未预期错误:{str(e)}")
        raise NebulaException(f"执行{context_name}时发生内部错误:{str(e)}") from e

@contextmanager
def sync_error_boundary(context_name: str):
    """
    提供同步错误边界的上下文管理器

    用法:
    ```
    with sync_error_boundary("用户注册"):
        # 同步业务逻辑
    ```
    """
    try:
        yield
    except NebulaException as e:
        # 应用内部定义的异常，直接传递
        raise
    except Exception as e:
        # 未预期的异常，转换为应用异常
        logging.exception(f"{context_name}发生未预期错误:{str(e)}")
        raise NebulaException(f"执行{context_name}时发生内部错误:{str(e)}") from e