"""
资源提供者模块
提供通用的资源注册与获取机制，不依赖具体的组件类型
作为最底层的基础设施，彻底避免循环依赖
"""
from typing import Any, Optional, Dict
import logging
from nebula.core.infrastructure.exceptions import RuntimeError

# 获取日志记录器
logger = logging.getLogger(__name__)

class _ResourceProvider:
    """资源提供者，通用容器模式，不依赖具体组件类型"""
    
    def __init__(self):
        """初始化提供者"""
        self._components: Dict[str, Any] = {}
    
    def register(self, name: str, instance: Any) -> None:
        """
        注册资源实例
        
        Args:
            name: 资源名称
            instance: 资源实例
        """
        self._components[name] = instance
        logger.debug(f"资源已注册: {name}")
    
    def get(self, name: str, raise_error: bool = True) -> Any:
        """
        获取资源实例
        
        Args:
            name: 资源名称
            raise_error: 当资源不可用时是否抛出异常
            
        Returns:
            注册的资源实例或None
            
        Raises:
            RuntimeError: 当raise_error为True且资源不可用时
        """
        if name not in self._components:
            if raise_error:
                raise RuntimeError(f"资源未注册或不可用: {name}")
        return self._components.get(name)
    
    def is_available(self, name: str) -> bool:
        """
        检查资源是否可用
        
        Args:
            name: 资源名称
            
        Returns:
            资源是否可用
        """
        return name in self._components and self._components[name] is not None
    
    def list_resources(self) -> Dict[str, Any]:
        """
        列出所有已注册的资源
        
        Returns:
            已注册的资源字典
        """
        return self._components.copy()

# 单例实例
_provider = _ResourceProvider()

# 导出函数
def register(instance: Any) -> None:
    """
    注册资源实例
    
    Args:
        name: 资源名称
        instance: 资源实例
    """
    key = "{}.{}".format(instance.__module__, instance.__class__.__name__)
    _provider.register(key, instance)

def get(cls: Any, raise_error: bool = True) -> Any:
    """
    获取资源实例
    
    Args:
        cls: 资源类
        raise_error: 当资源不可用时是否抛出异常
        
    Returns:
        注册的资源实例或None
        
    Raises:
        RuntimeError: 当raise_error为True且资源不可用时
    """
    key = "{}.{}".format(cls.__module__, cls.__name__)
    return _provider.get(key, raise_error)

def is_available(cls: Any) -> bool:
    """
    检查资源是否可用
    
    Args:
        cls: 资源类
        
    Returns:
        资源是否可用
    """
    key = "{}.{}".format(cls.__module__, cls.__name__)
    return _provider.is_available(key)

def list_resources() -> Dict[str, Any]:
    """
    列出所有已注册的资源
    
    Returns:
        已注册的资源字典
    """
    return _provider.list_resources()

