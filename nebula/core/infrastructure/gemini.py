"""
Gemini服务模块 - 提供Google Gemini大模型生成内容的服务
"""

from typing import Dict, List, Optional, Any, Union, AsyncGenerator, AsyncIterable, Tuple
import asyncio
from datetime import datetime
import time
import logging
import os
import json
import uuid
import hashlib
import aiohttp
from google.genai import types, Client
from gcloud.aio.storage import Storage as GCSStorage

import nebula.core.infrastructure.config as config

from nebula.core.infrastructure.config import get_settings
from nebula.core.infrastructure.exceptions import NebulaException, ValidationError
from nebula.core.infrastructure.logging import get_logger, calculate_time


logger = get_logger(__name__)

MIME_TYPES = {
    "mp4": "video/mp4",
    "avi": "video/x-msvideo",
    "mov": "video/quicktime",
    "txt": "text/plain",
    "mp3": "audio/mpeg",
    "wav": "audio/wav",
    "pdf": "application/pdf",
    "png": "image/png",
    "jpg": "image/jpeg",
    "jpeg": "image/jpeg",
    "md": "text/markdown",
}

class GeminiService:
    """Gemini服务

    提供基于Google Gemini大模型的内容生成功能。
    """

    def __init__(self, settings: Optional[config.Settings] = None):
        """初始化Gemini服务

        Args:
            settings: 应用配置
        """
        self.settings = settings or get_settings()
        self.client = self._initialize_client()
        self.model = self.settings.genai.model
        self.generate_content_config = self._create_content_config()
        self._init_processors()
        if not self.settings.genai.bucket:
            raise NebulaException("genai.bucket 未配置")

    def _initialize_client(self) -> Client:
        """初始化Gemini客户端

        Returns:
            Client: Gemini客户端实例

        Raises:
            NebulaException: 初始化失败时抛出
        """
        try:
            if not self.settings.genai.project:
                raise NebulaException("请在配置文件中设置GENAI_PROJECT环境变量")
            # 获取服务账号密钥文件路径
            credentials_path = self.settings.genai.credentials
            if not os.path.isabs(credentials_path):
                # 如果是相对路径，尝试在几个可能的位置查找
                possible_paths = [
                    credentials_path,  # 当前目录
                    os.path.join(os.getcwd(), credentials_path),  # 工作目录
                    os.path.join(os.path.dirname(__file__), credentials_path),  # 模块目录
                ]

                for path in possible_paths:
                    if os.path.exists(path):
                        credentials_path = path
                        logger.info(f"找到服务账号密钥文件: {path}")
                        break
                else:
                    logger.error(f"无法找到服务账号密钥文件: {self.settings.genai.credentials}")
                    raise FileNotFoundError(f"无法找到服务账号密钥文件: {self.settings.genai.credentials}")

            # 设置环境变量指向服务账号密钥文件
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path

            # 初始化客户端
            client = Client(
                vertexai=True,
                project=self.settings.genai.project,
                location=self.settings.genai.location
            )
            logger.info("已初始化Gemini客户端")
            return client
        except Exception as e:
            error_msg = str(e)
            logger.error(f"初始化Gemini客户端失败: {error_msg}")

            # 如果是认证错误，抛出特定的异常
            if "credentials" in error_msg.lower() or "authentication" in error_msg.lower():
                raise NebulaException(
                    f"Gemini API认证失败: {error_msg}",
                    status_code=401
                )

            raise NebulaException(f"初始化Gemini客户端失败: {error_msg}")

    def _create_content_config(self) -> types.GenerateContentConfig:
        """创建内容生成配置

        Returns:
            types.GenerateContentConfig: 内容生成配置
        """
        safety_settings = []
        for setting in self.settings.genai.safety_settings:
            safety_settings.append(
                types.SafetySetting(
                    category=setting['category'],
                    threshold=setting['threshold']
                )
            )

        return types.GenerateContentConfig(
            temperature=self.settings.genai.temperature,
            top_p=self.settings.genai.top_p,
            max_output_tokens=self.settings.genai.max_output_tokens,
            response_modalities=self.settings.genai.response_modalities,
            safety_settings=safety_settings,
        )

    def _init_processors(self):
        """初始化各种文件处理器"""
        self.processors = {
            'mp4': VideoProcessor(),
            'avi': VideoProcessor(),
            'mov': VideoProcessor(),
            'txt': TextProcessor(),
            'mp3': AudioProcessor(),
            'wav': AudioProcessor(),
            'pdf': PDFProcessor(),
            'png': ImageProcessor(),
            'jpg': ImageProcessor(),
            'jpeg': ImageProcessor(),
            'md': MDProcessor(),
        }

    async def _upload_to_gcs(self, file_uri: str, mime_type: str) -> str:
        """将文件上传到Google Cloud Storage

        Args:
            file_uri: 原始文件URI，支持URL和本地路径
            mime_type: 文件的MIME类型

        Returns:
            str: Google Cloud Storage URL

        Raises:
            NebulaException: 上传失败时抛出
        """
        try:
            logger.info(f"准备将{file_uri}上传到Google Cloud Storage")

            # 判断是URL还是本地路径
            if file_uri.startswith(('http://', 'https://')):
                # 如果是URL，使用aiohttp下载
                async with aiohttp.ClientSession() as session:
                    async with calculate_time(f"下载文件内容 ({file_uri})", microsecond=False, logger=logger):
                        async with session.get(file_uri) as response:
                            if response.status != 200:
                                raise NebulaException(f"下载文件失败: HTTP {response.status} ({file_uri})")
                            file_data = await response.read()
            else:
                # 如果是本地路径，直接读取文件
                async with calculate_time(f"读取本地文件{file_uri}内容", microsecond=False, logger=logger):
                    with open(file_uri, 'rb') as f:
                        file_data = f.read()

            logger.info(f"文件读取完成，大小: {len(file_data)/1024/1024:.2f} MB")

            # 计算文件内容的MD5哈希值
            file_hash = hashlib.md5(file_data).hexdigest()

            # 创建GCS客户端
            bucket_name = self.settings.genai.bucket
            # 使用MD5哈希值作为文件名
            file_extension = os.path.splitext(file_uri)[1].lower()
            object_name = f"{file_hash}{file_extension}"

            # 创建异步会话并上传文件
            async with aiohttp.ClientSession() as session:
                # 创建GCS客户端
                storage_client = GCSStorage(session=session)

                # 检查文件是否已经存在
                try:
                    # 尝试获取文件元数据，如果文件存在，这个调用会成功
                    await storage_client.download_metadata(bucket_name, object_name, timeout=30)
                    # 如果文件存在，直接返回GCS URL，不需要再次上传
                    logger.info(f"文件已存在于GCS: gs://{bucket_name}/{object_name}")
                    return f"gs://{bucket_name}/{object_name}"
                except Exception as ex:
                    # 如果文件不存在，会抛出异常，继续上传
                    logger.info(f"文件不存在于GCS，开始上传: {object_name}")

                # 上传文件
                logger.info(f"上传文件到GCS: bucket={bucket_name}, object={object_name}, size={len(file_data)/1024/1024:.2f} MB")
                async with calculate_time(f"GCS Upload", microsecond=False, logger=logger):
                    await storage_client.upload(
                        bucket_name,
                        object_name,
                        file_data,
                        content_type=mime_type,
                        timeout=1200  # 设置超时时间为20minutes
                    )

                    # 构建GCS URL
                    gcs_url = f"gs://{bucket_name}/{object_name}"
                    logger.info(f"文件已上传到GCS: {gcs_url}")

                return gcs_url

        except Exception as e:
            logger.error(f"上传文件到GCS失败: {str(e)}", exc_info=True)
            raise NebulaException(f"上传文件到GCS失败: {str(e)}")

    async def _delete_from_gcs(self, gcs_url: str) -> None:
        """删除Google Cloud Storage中的对象

        Args:
            gcs_url: Google Cloud Storage URL，格式为 gs://{bucket}/{object_name}

        Raises:
            NebulaException: 删除失败时抛出
        """
        try:
            # 解析GCS URL
            if not gcs_url.startswith('gs://'):
                logger.warning(f"不是有效的GCS URL: {gcs_url}")
                return

            # 从URL中提取bucket和object_name
            # 格式: gs://{bucket}/{object_name}
            parts = gcs_url[5:].split('/', 1)
            if len(parts) != 2:
                logger.warning(f"无法从URL中提取bucket和object_name: {gcs_url}")
                return

            bucket = parts[0]
            object_name = parts[1]

            # 创建异步会话并删除对象
            async with aiohttp.ClientSession() as session:
                # 创建GCS客户端
                storage_client = GCSStorage(session=session)

                # 删除对象
                # await storage_client.delete(bucket, object_name)

            logger.info(f"已删除Google Cloud Storage对象: {gcs_url}")

        except Exception as e:
            logger.error(f"删除Google Cloud Storage对象失败: {str(e)}", exc_info=True)
            # 这里我们不抛出异常，因为这是清理操作，不应该影响主要功能

    def _generate_schema_from_example(self, example):
        """根据示例生成schema

        Args:
            example: 示例 JSON 对象

        Returns:
            dict: 生成的schema
        """
        schema = {
            "type": "OBJECT",
            "properties": {},
            "required": []
        }

        for key, value in example.items():
            # 将所有字段添加到required列表
            schema["required"].append(key)

            # 根据值的类型生成属性定义
            if isinstance(value, str):
                schema["properties"][key] = {"type": "STRING"}
            elif isinstance(value, bool):
                schema["properties"][key] = {"type": "BOOLEAN"}
            elif isinstance(value, (int, float)):
                schema["properties"][key] = {"type": "NUMBER"}
            elif isinstance(value, list):
                # 如果是列表，需要确定列表元素的类型
                item_type = "STRING"  # 默认元素类型
                if value and isinstance(value[0], str):
                    item_type = "STRING"
                elif value and isinstance(value[0], bool):
                    item_type = "BOOLEAN"
                elif value and isinstance(value[0], (int, float)):
                    item_type = "NUMBER"
                elif value and isinstance(value[0], dict):
                    # 如果列表元素是对象，递归生成schema
                    item_schema = self._generate_schema_from_example(value[0])
                    schema["properties"][key] = {
                        "type": "ARRAY",
                        "items": item_schema
                    }
                    continue

                schema["properties"][key] = {
                    "type": "ARRAY",
                    "items": {"type": item_type}
                }
            elif isinstance(value, dict):
                # 如果是对象，递归生成schema
                schema["properties"][key] = self._generate_schema_from_example(value)
            elif value is None:
                # 如果是空值，设置为可空的字符串
                schema["properties"][key] = {"type": "STRING", "nullable": True}

        return schema

    async def generate_content(self, contents, max_retries=5, backoff_factor=2, custom_config=None):
        """生成内容

        Args:
            contents: 内容生成的输入
            max_retries: 最大重试次数
            backoff_factor: 重试延迟因子
            custom_config: 自定义配置，如果提供，将覆盖默认配置

        Returns:
            str: 生成的内容

        Raises:
            NebulaException: 生成内容失败时抛出
        """
        retry = 0
        delay = 1  # 初始延迟时间（秒）

        # 使用自定义配置或默认配置
        config = custom_config if custom_config else self.generate_content_config

        while retry < max_retries:
            try:
                logger.info("开始调用Gemini模型生成内容")

                # 尝试使用非流式 API
                try:
                    # 使用非流式 generate_content API 的异步版本
                    response = await self.client.aio.models.generate_content(
                        model=self.model,
                        contents=contents,
                        config=config,
                    )
                    if hasattr(response, 'text'):
                        result = response.text
                    elif hasattr(response, 'content'):
                        result = response.content
                    else:
                        logger.warning(f"未知的响应格式: {response}")
                        result = str(response)

                    logger.info("内容生成完成 (非流式)")
                    return result

                except AttributeError:
                    # 如果非流式 API 不可用，回退到流式 API
                    logger.info("非流式 API 不可用，使用流式 API")
                    response = []

                    async for chunk in self.client.aio.models.generate_content_stream(
                        model=self.model,
                        contents=contents,
                        config=config,
                    ):
                        logger.debug(f"收到模型响应块: {chunk}")
                        if hasattr(chunk, 'text'):
                            response.append(chunk.text)
                        elif hasattr(chunk, 'content'):
                            response.append(chunk.content)
                        else:
                            logger.warning(f"未知的响应块格式: {chunk}")

                    result = ''.join(response)
                    logger.info("内容生成完成 (流式)")
                    return result

            except Exception as e:
                error_msg = str(e)

                # 如果是认证错误，直接抛出异常，不重试
                if "credentials" in error_msg.lower() or "authentication" in error_msg.lower():
                    logger.error(f"Gemini API认证失败: {error_msg}")
                    raise NebulaException(
                        f"Gemini API认证失败: {error_msg}",
                        status_code=401
                    )

                # 如果是配额耗尽，尝试重试
                if 'RESOURCE_EXHAUSTED' in error_msg:
                    logger.warning(f"配额耗尽，等待{delay}秒后重试... (第{retry + 1}次重试)")
                    time.sleep(delay)
                    retry += 1
                    delay *= backoff_factor
                else:
                    logger.error(f"调用Gemini模型生成内容失败: {error_msg}")
                    raise NebulaException(f"调用Gemini模型生成内容失败: {error_msg}")

        logger.error("达到最大重试次数，无法完成内容生成")
        raise NebulaException("Gemini模型生成内容失败，配额耗尽")

    async def stream_generate_content(self, contents, custom_config=None) -> AsyncIterable[str]:
        """流式生成内容

        Args:
            contents: 内容生成的输入
            custom_config: 自定义配置，如果提供，将覆盖默认配置

        Yields:
            str: 生成的内容块

        Raises:
            NebulaException: 生成内容失败时抛出
        """
        try:
            logger.info(f"开始流式调用Gemini模型生成内容: (generate_content_config: {custom_config})")

            # 使用自定义配置或默认配置
            config = custom_config if custom_config else self.generate_content_config

            # 使用generate_content_stream方法的异步版本生成流式内容
            stream = await self.client.aio.models.generate_content_stream(
                model=self.model,
                contents=contents,
                config=config
            )

            async for chunk in stream:
                yield chunk.text

            logger.info("流式内容生成完成")

        except Exception as e:
            error_msg = str(e)

            # 如果是认证错误，直接抛出异常
            if "credentials" in error_msg.lower() or "authentication" in error_msg.lower():
                logger.error(f"Gemini API认证失败: {error_msg}")
                raise NebulaException(
                    f"Gemini API认证失败: {error_msg}",
                    status_code=401
                )

            logger.error(f"流式调用Gemini模型生成内容失败: {error_msg}", exc_info=True)
            raise NebulaException(f"流式调用Gemini模型生成内容失败: {error_msg}")

    async def generate_content_for_file(self, file_uri: str, prompt: str, use_structured_response=False, response_format=None):
        """处理指定文件并生成内容

        Args:
            file_uri: 要处理的文件URI，可以是URL也可以是本地路径
            prompt: 内容生成的提示词
            use_structured_response: 是否使用结构化响应格式
            response_format: 响应格式示例，例如 {"title": "标题", "content": "内容"}，仅当 use_structured_response=True 时有效

        Returns:
            str: 生成的内容

        Raises:
            ValidationError: 参数验证失败时抛出
            NebulaException: 处理失败时抛出
        """
        if not prompt:
            raise ValidationError("prompt 参数缺失")
        if not file_uri:
            raise ValidationError("file_uri 参数缺失")

        gcs_url = None

        try:
            logger.info(f"处理文件: {file_uri}")
            file_extension = file_uri.split('?')[0].split(".")[-1].lower()
            mime_type = MIME_TYPES.get(file_extension, None)    # 获取文件的MIME类型
            # 检测是否为YouTube视频
            from urllib.parse import urlparse
            parsed_uri = urlparse(file_uri)
            is_youtube_video = parsed_uri.scheme in ['http', 'https'] and parsed_uri.netloc in ['www.youtube.com', 'youtube.com', 'youtu.be']
            if is_youtube_video:
                file_extension = "mp4"
                mime_type = "video/mp4"
            processor = self.processors.get(file_extension)
            if not processor:
                raise ValidationError(f"不支持的文件类型: {file_extension}({file_uri})")

            # 如果是视频处理器，并且不是YouTube视频，需要先上传到Google Cloud Storage
            if isinstance(processor, VideoProcessor) and not is_youtube_video:
                # 上传到Google Cloud Storage
                logger.info("检测到视频文件，上传到Google Cloud Storage")
                gcs_url = await self._upload_to_gcs(file_uri, mime_type)

                # 使用Google Cloud Storage URL替换原始URI
                logger.info(f"使用Google Cloud Storage URL: {gcs_url}")
                file_uri = gcs_url

            # 准备内容
            contents = processor.prepare_contents(file_uri, prompt, mime_type)

            # 如果需要结构化响应，创建自定义配置
            custom_config = None
            if use_structured_response:
                # 如果提供了response_format，将其转换为schema
                schema = None
                if response_format:
                    try:
                        # 根据示例生成schema
                        schema = self._generate_schema_from_example(response_format)
                        logger.info(f"根据示例生成schema: {schema}")
                    except Exception as e:
                        logger.error(f"生成schema失败: {e}")
                        raise ValidationError(f"无法根据示例生成schema: {e}")

                # 创建自定义配置
                custom_config = types.GenerateContentConfig(
                    temperature=self.settings.genai.temperature,
                    top_p=self.settings.genai.top_p,
                    max_output_tokens=self.settings.genai.max_output_tokens,
                    response_modalities=self.settings.genai.response_modalities,
                    safety_settings=self.generate_content_config.safety_settings,
                    response_mime_type="application/json",
                    response_schema=schema
                )

            # 生成内容
            result = await self.generate_content(contents, custom_config=custom_config)
            logger.info(f"生成内容: {result[:100]}...")
            return result

        except ValidationError as e:
            raise e
        except Exception as e:
            logger.error(f"处理文件生成内容时发生错误({type(e).__name__}): {e}", exc_info=True)
            raise NebulaException(f"处理文件生成内容时发生错误({type(e).__name__}): {e}")
        finally:
            # 如果上传了文件到GCS，无论成功还是失败，都删除它
            if gcs_url:
                await self._delete_from_gcs(gcs_url)

    async def stream_generate_content_for_file(self, file_uri: str, prompt: str, use_structured_response=False, response_format=None) -> AsyncIterable[str]:
        """流式处理指定文件并生成内容

        Args:
            file_uri: 要处理的文件URI，可以是URL也可以是本地路径
            prompt: 内容生成的提示词
            use_structured_response: 是否使用结构化响应格式
            response_format: 响应格式示例，例如 {"title": "标题", "content": "内容"}，仅当 use_structured_response=True 时有效
            mime_type: 文件的MIME类型，如果不提供则使用处理器的默认值

        Yields:
            str: 生成的内容块

        Raises:
            ValidationError: 参数验证失败时抛出
            NebulaException: 处理失败时抛出
        """
        if not prompt:
            raise ValidationError("prompt 参数缺失")
        if not file_uri:
            raise ValidationError("file_uri 参数缺失")

        gcs_url = None

        try:
            logger.info(f"处理文件: {file_uri}")
            file_extension = file_uri.split('?')[0].split(".")[-1].lower()
            mime_type = MIME_TYPES.get(file_extension, None)    # 获取文件的MIME类型
            # 检测是否为YouTube视频
            from urllib.parse import urlparse
            parsed_uri = urlparse(file_uri)
            is_youtube_video = parsed_uri.scheme in ['http', 'https'] and parsed_uri.netloc in ['www.youtube.com', 'youtube.com', 'youtu.be']
            if is_youtube_video:
                file_extension = "mp4"
                mime_type = "video/mp4"
            processor = self.processors.get(file_extension)
            if not processor:
                raise ValidationError(f"不支持的文件类型: {file_extension}({file_uri})")

            # 如果是视频处理器，并且不是YouTube视频，需要先上传到Google Cloud Storage
            if isinstance(processor, VideoProcessor) and not is_youtube_video:
                # 上传到Google Cloud Storage
                logger.info("检测到视频文件，上传到Google Cloud Storage")
                gcs_url = await self._upload_to_gcs(file_uri, mime_type)

                # 使用Google Cloud Storage URL替换原始URI
                logger.info(f"使用Google Cloud Storage URL: {gcs_url}")
                file_uri = gcs_url

            # 准备内容
            contents = processor.prepare_contents(file_uri, prompt, mime_type)

            # 如果需要结构化响应，创建自定义配置
            custom_config = None
            if use_structured_response:
                # 如果提供了response_format，将其转换为schema
                schema = None
                if response_format:
                    try:
                        # 根据示例生成schema
                        schema = self._generate_schema_from_example(response_format)
                        logger.info(f"根据示例生成schema: {schema}")
                    except Exception as e:
                        logger.error(f"生成schema失败: {e}")
                        raise ValidationError(f"无法根据示例生成schema: {e}")

                # 创建自定义配置
                custom_config = types.GenerateContentConfig(
                    temperature=self.settings.genai.temperature,
                    top_p=self.settings.genai.top_p,
                    max_output_tokens=self.settings.genai.max_output_tokens,
                    response_modalities=self.settings.genai.response_modalities,
                    safety_settings=self.generate_content_config.safety_settings,
                    response_mime_type="application/json",
                    response_schema=schema
                )

            # 流式生成内容
            # 调用stream_generate_content方法
            async for chunk in self.stream_generate_content(contents, custom_config=custom_config):
                yield chunk

        except ValidationError as e:
            raise e
        except Exception as e:
            logger.error(f"流式处理文件生成内容时发生错误: {e}", exc_info=True)
            raise NebulaException(f"流式处理文件生成内容时发生错误: {e}")
        finally:
            # 如果上传了文件到GCS，无论成功还是失败，都删除它
            if gcs_url:
                await self._delete_from_gcs(gcs_url)

    async def generate_content_for_text(self, text: str, prompt: str, history: Optional[List[Dict[str, Any]]] = None, use_structured_response=False, response_format=None):
        """处理文本并生成内容

        Args:
            text: 要处理的文本
            prompt: 内容生成的提示词
            history: 可选，历史消息，格式为 [{'role': 'user', 'content': '用户消息'}, {'role': 'model', 'content': 'AI回复'}]
            use_structured_response: 是否使用结构化响应格式
            response_format: 响应格式示例，例如 {"title": "标题", "content": "内容"}，仅当 use_structured_response=True 时有效

        Returns:
            str: 生成的内容

        Raises:
            ValidationError: 参数验证失败时抛出
            NebulaException: 处理失败时抛出
        """
        if not prompt:
            raise ValidationError("prompt 参数缺失")
        if not text:
            raise ValidationError("text 参数缺失")

        try:
            logger.info("处理文本内容")

            # 准备内容
            contents = []

            # 如果有历史消息，先添加历史消息
            if history:
                for msg in history:
                    role = msg.get('role', 'user')
                    content = msg.get('content', '')
                    if role and content:
                        contents.append(
                            types.Content(
                                role=role,
                                parts=[
                                    types.Part(text=content)
                                ]
                            )
                        )

            # 添加当前用户的消息
            contents.append(
                types.Content(
                    role="user",
                    parts=[
                        types.Part(text=text),
                        types.Part(text=prompt)
                    ]
                )
            )

            # 如果需要结构化响应，创建自定义配置
            custom_config = None
            if use_structured_response:
                # 如果提供了response_format，将其转换为schema
                schema = None
                if response_format:
                    try:
                        # 根据示例生成schema
                        schema = self._generate_schema_from_example(response_format)
                        logger.info(f"根据示例生成schema: {schema}")
                    except Exception as e:
                        logger.error(f"生成schema失败: {e}")
                        raise ValidationError(f"无法根据示例生成schema: {e}")

                # 创建自定义配置
                custom_config = types.GenerateContentConfig(
                    temperature=self.settings.genai.temperature,
                    top_p=self.settings.genai.top_p,
                    max_output_tokens=self.settings.genai.max_output_tokens,
                    response_modalities=self.settings.genai.response_modalities,
                    safety_settings=self.generate_content_config.safety_settings,
                    response_mime_type="application/json",
                    response_schema=schema
                )

            # 生成内容
            result = await self.generate_content(contents, custom_config=custom_config)
            logger.info(f"生成内容: {result[:100]}...")
            return result

        except Exception as e:
            logger.error(f"处理文本生成内容时发生错误: {e}")
            raise NebulaException(f"处理文本生成内容时发生错误: {e}")

    async def stream_generate_content_for_text(self, text: str, prompt: str, history: Optional[List[Dict[str, Any]]] = None, use_structured_response=False, response_format=None) -> AsyncIterable[str]:
        """流式处理文本并生成内容

        Args:
            text: 要处理的文本
            prompt: 内容生成的提示词
            history: 可选，历史消息，格式为 [{'role': 'user', 'content': '用户消息'}, {'role': 'model', 'content': 'AI回复'}]
            use_structured_response: 是否使用结构化响应格式
            response_format: 响应格式示例，例如 {"title": "标题", "content": "内容"}，仅当 use_structured_response=True 时有效

        Yields:
            str: 生成的内容块

        Raises:
            ValidationError: 参数验证失败时抛出
            NebulaException: 处理失败时抛出
        """
        if not prompt:
            raise ValidationError("prompt 参数缺失")
        if not text:
            raise ValidationError("text 参数缺失")

        try:
            logger.info("流式处理文本内容")

            # 准备内容
            contents = []

            # 如果有历史消息，先添加历史消息
            if history:
                for msg in history:
                    role = msg.get('role', 'user')
                    content = msg.get('content', '')
                    if role and content:
                        contents.append(
                            types.Content(
                                role=role,
                                parts=[
                                    types.Part(text=content)
                                ]
                            )
                        )

            # 添加当前用户的消息
            contents.append(
                types.Content(
                    role="user",
                    parts=[
                        types.Part(text=text),
                        types.Part(text=prompt)
                    ]
                )
            )

            # 如果需要结构化响应，创建自定义配置
            custom_config = None
            if use_structured_response:
                # 如果提供了response_format，将其转换为schema
                schema = None
                if response_format:
                    try:
                        # 根据示例生成schema
                        schema = self._generate_schema_from_example(response_format)
                        logger.info(f"根据示例生成schema: {schema}")
                    except Exception as e:
                        logger.error(f"生成schema失败: {e}")
                        raise ValidationError(f"无法根据示例生成schema: {e}")

                # 创建自定义配置
                custom_config = types.GenerateContentConfig(
                    temperature=self.settings.genai.temperature,
                    top_p=self.settings.genai.top_p,
                    max_output_tokens=self.settings.genai.max_output_tokens,
                    response_modalities=self.settings.genai.response_modalities,
                    safety_settings=self.generate_content_config.safety_settings,
                    response_mime_type="application/json",
                    response_schema=schema
                )

            # 流式生成内容
            # 调用stream_generate_content方法
            async for chunk in self.stream_generate_content(contents, custom_config=custom_config):
                yield chunk

        except Exception as e:
            logger.error(f"流式处理文本生成内容时发生错误: {e}", exc_info=True)
            raise NebulaException(f"流式处理文本生成内容时发生错误: {e}")


# 处理器类定义
class BaseProcessor:
    """基础处理器类，定义通用接口。"""

    def prepare_contents(self, file_uri: str, prompt: str, mime_type: str = None) -> list:
        """准备生成内容所需的内容列表。

        Args:
            file_uri: 文件的URI
            prompt: 提示词
            mime_type: 文件的MIME类型，如果不提供则使用默认值

        Returns:
            list: 生成内容的参数列表
        """
        raise NotImplementedError("子类必须实现此方法")


class TextProcessor(BaseProcessor):
    """文本文件处理器。"""

    def prepare_contents(self, file_uri: str, prompt: str, mime_type: str = "text/plain") -> list:
        """准备文本文件的内容列表。

        Args:
            file_uri: 文件的URI
            prompt: 提示词
            mime_type: 文本文件的MIME类型，默认为"text/plain"

        Returns:
            list: 生成内容的参数列表
        """
        # 直接创建Part对象
        # 使用Part.from_uri方法创建Part对象
        text_part = types.Part.from_uri(
            mime_type=mime_type,
            file_uri=file_uri
        )
        return [
            types.Content(
                role="user",
                parts=[
                    text_part,
                    types.Part.from_text(text=prompt)
                ]
            )
        ]


class MDProcessor(BaseProcessor):
    """Markdown文件处理器。"""

    def prepare_contents(self, file_uri: str, prompt: str, mime_type: str = "text/markdown") -> list:
        """准备Markdown文件的内容列表。

        Args:
            file_uri: 文件的URI
            prompt: 提示词
            mime_type: Markdown文件的MIME类型，默认为"text/markdown"

        Returns:
            list: 生成内容的参数列表
        """
        # 直接创建Part对象
        md_part = types.Part.from_uri(
            mime_type=mime_type,
            file_uri=file_uri
        )
        return [
            types.Content(
                role="user",
                parts=[
                    md_part,
                    types.Part.from_text(text=prompt)
                ]
            )
        ]


class ImageProcessor(BaseProcessor):
    """图像文件处理器。"""

    def prepare_contents(self, file_uri: str, prompt: str, mime_type: str = "image/jpeg") -> list:
        """准备图像文件的内容列表。

        Args:
            file_uri: 文件的URI
            prompt: 提示词
            mime_type: 图像文件的MIME类型，默认为"image/jpeg"

        Returns:
            list: 生成内容的参数列表
        """
        # 直接创建Part对象
        image_part = types.Part.from_uri(
            mime_type=mime_type,
            file_uri=file_uri
        )
        return [
            types.Content(
                role="user",
                parts=[
                    image_part,
                    types.Part.from_text(text=prompt)
                ]
            )
        ]


class AudioProcessor(BaseProcessor):
    """音频文件处理器。"""

    def prepare_contents(self, file_uri: str, prompt: str, mime_type: str = "audio/mpeg") -> list:
        """准备音频文件的内容列表。

        Args:
            file_uri: 文件的URI
            prompt: 提示词
            mime_type: 音频文件的MIME类型，默认为"audio/mpeg"

        Returns:
            list: 生成内容的参数列表
        """
        # 直接创建Part对象
        audio_part = types.Part.from_uri(
            mime_type=mime_type,
            file_uri=file_uri
        )
        return [
            types.Content(
                role="user",
                parts=[
                    audio_part,
                    types.Part.from_text(text=prompt)
                ]
            )
        ]


class VideoProcessor(BaseProcessor):
    """视频文件处理器。"""

    def prepare_contents(self, file_uri: str, prompt: str, mime_type: str = "video/mp4") -> list:
        """准备视频文件的内容列表。

        Args:
            file_uri: 文件的URI
            prompt: 提示词
            mime_type: 视频文件的MIME类型，默认为"video/mp4"

        Returns:
            list: 生成内容的参数列表
        """
        video_part = types.Part.from_uri(
            file_uri=file_uri,
            mime_type=mime_type,
        )
        return [
            types.Content(
                role="user",
                parts=[
                    video_part,
                    types.Part.from_text(text=prompt)
                ]
            )
        ]


class PDFProcessor(BaseProcessor):
    """PDF文件处理器。"""

    def prepare_contents(self, file_uri: str, prompt: str, mime_type: str = "application/pdf") -> list:
        """准备PDF文件的内容列表。

        Args:
            file_uri: 文件的URI
            prompt: 提示词
            mime_type: PDF文件的MIME类型，默认为"application/pdf"

        Returns:
            list: 生成内容的参数列表
        """
        # 直接创建Part对象
        pdf_part = types.Part.from_uri(
            mime_type=mime_type,
            file_uri=file_uri
        )
        return [
            types.Content(
                role="user",
                parts=[
                    pdf_part,
                    types.Part.from_text(text=prompt)
                ]
            )
        ]
