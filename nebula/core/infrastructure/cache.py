"""
Redis缓存模块
"""
import os
import json
from typing import Any, Dict, List, Optional, Union, AsyncIterator, Callable
import redis.asyncio as redis
import nebula.core.infrastructure.logging as logging
import asyncio

# 获取日志记录器
logger = logging.get_logger(__name__)

class Subscriber:
    """Redis订阅管理器"""

    def __init__(self, pubsub: redis.client.PubSub):
        """
        初始化订阅管理器

        Args:
            pubsub: Redis PubSub对象
        """
        self._pubsub = pubsub
        self._running = False

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self._running = True
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        self._running = False
        await self._pubsub.unsubscribe()
        await self._pubsub.close()

    async def __aiter__(self):
        """异步迭代器接口"""
        return self

    async def __anext__(self):
        """获取下一条消息"""
        if not self._running:
            raise StopAsyncIteration

        while self._running:
            try:
                message = await self._pubsub.get_message(ignore_subscribe_messages=True)
                if message is not None:
                    try:
                        # 尝试解析JSON数据
                        data = message['data']
                        try:
                            data = json.loads(data)
                        except (json.JSONDecodeError, TypeError):
                            pass
                        return message['channel'], data
                    except Exception as e:
                        logger.error(f"处理订阅消息时出错: {str(e)}")
                        continue
            except Exception as e:
                logger.error(f"获取订阅消息时出错: {str(e)}")
                self._running = False
                raise StopAsyncIteration

class RedisCache:
    """Redis缓存管理类"""

    def __init__(
        self,
        host: Optional[str] = None,
        port: Optional[int] = None,
        username: Optional[str] = None,
        password: Optional[str] = None,
        db: Optional[int] = None,
        settings = None
    ):
        """
        初始化Redis缓存管理器

        Args:
            host: Redis主机地址
            port: Redis端口
            username: Redis用户名
            password: Redis密码
            db: Redis数据库索引
            settings: 配置对象，如果设置了其他参数则忽略此项
        """
        # 优先使用参数，其次使用配置，最后使用环境变量
        if settings and not any([host, port, username, password, db]):
            redis_config = settings.redis
            self._host = redis_config.host
            self._port = redis_config.port
            self._username = redis_config.username
            self._password = redis_config.password
            self._db = redis_config.db or 0
            logger.debug("使用配置对象初始化Redis")
        else:
            self._host = host or os.environ.get('REDIS_HOST')
            self._port = port or int(os.environ.get('REDIS_PORT', 6379))
            self._username = username or os.environ.get('REDIS_USERNAME')
            self._password = password or os.environ.get('REDIS_PASSWORD')
            self._db = db or int(os.environ.get('REDIS_DB', 0))
            logger.debug("使用参数初始化Redis")

        if not self._host:
            raise ValueError("Redis host is required")

        self._client = None
        self._max_retries = 3
        self._retry_delay = 1  # 秒
        self._connection_lock = asyncio.Lock()
        logger.info("Redis缓存管理器初始化完成", extra={
            "host": self._host,
            "port": self._port,
            "db": self._db
        })

    async def connect(self):
        """连接到Redis服务器"""
        async with self._connection_lock:
            if self._client is None:
                try:
                    connection_kwargs = {
                        "host": self._host,
                        "port": self._port,
                        "db": self._db,
                        "decode_responses": True,  # 自动将二进制响应解码为字符串
                        "retry_on_timeout": True,  # 超时时自动重试
                        "socket_keepalive": True,  # 保持连接
                        "health_check_interval": 30  # 定期检查连接健康状况
                    }
                    if self._username:
                        connection_kwargs["username"] = self._username
                    if self._password:
                        connection_kwargs["password"] = self._password

                    self._client = redis.Redis(**connection_kwargs)
                    # 测试连接
                    await self._client.ping()
                    logger.info(f"Redis连接成功: endpoint={self._host}:{self._port}")
                except Exception as e:
                    logger.error(f"Redis连接失败: {str(e)}", exc_info=True)
                    if self._client:
                        await self._client.close()
                        self._client = None
                    raise
        return self

    @property
    def client(self) -> redis.Redis:
        """获取Redis客户端实例"""
        if not self._client:
            raise RuntimeError("Redis client not connected. Please call connect() first.")
        return self._client

    async def _execute_with_retry(self, operation: Callable, *args, **kwargs) -> Any:
        """执行Redis操作,支持重试

        Args:
            operation: 要执行的操作函数
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            Any: 操作结果

        Raises:
            Exception: 重试后仍然失败
        """
        last_error = None

        for attempt in range(self._max_retries):
            try:
                if not self._client:
                    await self.connect()
                return await operation(self._client, *args, **kwargs)

            except (redis.ConnectionError, redis.TimeoutError) as e:
                last_error = e
                logger.warning(f"Redis操作失败(尝试 {attempt + 1}/{self._max_retries}): {str(e)}")

                # 最后一次尝试不等待
                if attempt < self._max_retries - 1:
                    await asyncio.sleep(self._retry_delay * (attempt + 1))
                    # 确保重新连接
                    if self._client:
                        await self._client.close()
                    self._client = None

            except Exception as e:
                # 其他错误直接抛出
                logger.error(f"Redis操作失败: {str(e)}")
                raise

        raise last_error or Exception("Redis操作失败")

    async def get(self, key: str, default: Any = None) -> Any:
        """获取键值"""
        try:
            value = await self._execute_with_retry(lambda client, k: client.get(k), key)
            if value is None:
                return default

            # 尝试JSON解析
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value

        except Exception as e:
            logger.error(f"获取键值失败 - key:{key}, error:{str(e)}")
            raise

    async def getdel(self, key: str, default: Any = None) -> Any:
        """获取键值并删除该键

        Args:
            key: 键
            default: 默认值，当键不存在时返回

        Returns:
            Any: 键对应的值，如果键不存在则返回默认值
        """
        try:
            value = await self._execute_with_retry(lambda client, k: client.getdel(k), key)
            if value is None:
                return default

            # 尝试JSON解析
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value

        except Exception as e:
            logger.error(f"获取并删除键值失败 - key:{key}, error:{str(e)}")
            raise

    async def set(self, key: str, value: Any, ex: Optional[int] = None) -> bool:
        """
        设置键值对

        Args:
            key: 键
            value: 值
            ex: 过期时间（秒）

        Returns:
            是否成功设置
        """

        try:
            # 序列化值
            if isinstance(value, (dict, list)):
                value = json.dumps(value)

            return await self._execute_with_retry(
                lambda client, k, v, e: client.set(k, v, ex=e),
                key, value, ex
            )
        except Exception as e:
            logger.error(f"设置键值对失败 - key:{key}, error:{str(e)}")
            raise

    async def delete(self, key: str) -> int:
        """删除键"""
        try:
            return await self._execute_with_retry(lambda client, k: client.delete(k), key)
        except Exception as e:
            logger.error(f"删除键失败 - key:{key}, error:{str(e)}")
            raise

    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            return await self._execute_with_retry(lambda client, k: client.exists(k), key)
        except Exception as e:
            logger.error(f"检查键是否存在失败 - key:{key}, error:{str(e)}")
            raise

    async def expire(self, key: str, seconds: int) -> int:
        """设置键的过期时间

        Args:
            key: 键
            seconds: 过期时间（秒）

        Returns:
            如果设置成功则返回1，否则返回0
        """
        return await self.client.expire(key, seconds)

    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配模式的所有键

        Args:
            pattern: 匹配模式

        Returns:
            匹配的键列表
        """
        return await self.client.keys(pattern)

    async def hash_set(self, name: str, key: str, value: Any):
        """设置哈希表中的字段值

        Args:
            name: 哈希表名
            key: 字段名
            value: 字段值
        """
        if isinstance(value, (dict, list)):
            value = json.dumps(value)
        return await self.client.hset(name, key, value)

    async def hash_get(self, name: str, key: str, default: Any = None) -> Any:
        """获取哈希表中的字段值

        Args:
            name: 哈希表名
            key: 字段名
            default: 默认值

        Returns:
            字段值，如果字段不存在则返回默认值
        """
        value = await self.client.hget(name, key)
        if value is None:
            return default

        # 尝试反序列化JSON
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value

    async def hash_getall(self, name: str) -> Dict[str, Any]:
        """获取哈希表中的所有字段和值

        Args:
            name: 哈希表名

        Returns:
            字段和值的字典
        """
        result = await self.client.hgetall(name)
        if not result:
            return {}

        # 尝试将值反序列化为JSON
        for key, value in result.items():
            try:
                result[key] = json.loads(value)
            except (json.JSONDecodeError, TypeError):
                pass

        return result

    async def publish(self, channel: str, message: Any):
        """发布消息到指定频道

        Args:
            channel: 频道名
            message: 消息内容
        """
        if isinstance(message, (dict, list)):
            message = json.dumps(message)
        return await self.client.publish(channel, message)

    async def subscribe(self, *channels: str) -> Subscriber:
        """订阅一个或多个频道

        Args:
            *channels: 要订阅的频道名列表

        Returns:
            订阅管理器，可以使用 async for 来接收消息

        Example:
            ```python
            async with cache.subscribe("channel1", "channel2") as subscriber:
                async for channel, message in subscriber:
                    print(f"收到来自 {channel} 的消息: {message}")
            ```
        """
        pubsub = self.client.pubsub()
        await pubsub.subscribe(*channels)
        return Subscriber(pubsub)

    async def psubscribe(self, *patterns: str) -> Subscriber:
        """使用模式订阅一个或多个频道

        Args:
            *patterns: 频道名的模式列表，支持通配符

        Returns:
            订阅管理器，可以使用 async for 来接收消息

        Example:
            ```python
            async with cache.psubscribe("user.*", "chat.*") as subscriber:
                async for channel, message in subscriber:
                    print(f"收到来自 {channel} 的消息: {message}")
            ```
        """
        pubsub = self.client.pubsub()
        await pubsub.psubscribe(*patterns)
        return Subscriber(pubsub)

    async def close(self):
        """关闭Redis连接"""
        if self._client:
            try:
                await self._client.close()
                self._client = None
                logger.info("Redis连接已关闭")
            except Exception as e:
                logger.error(f"关闭Redis连接失败: {str(e)}")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()

    async def wait_task_result(self, task_id: str, result_prefix: str, timeout: int = 30) -> Any:
        """等待任务结果

        Args:
            task_id: 任务ID
            result_prefix: 结果key前缀
            timeout: 超时时间(秒)

        Returns:
            Any: 任务结果

        Raises:
            TimeoutError: 等待超时
        """
        result_key = f"{result_prefix}{task_id}"
        list_key = f"task:notify:{task_id}"

        try:
            # 先检查结果是否存在，如果存在则获取并删除
            result = await self.getdel(result_key)
            if result:
                return result

            # 等待通知
            try:
                notify = await self._execute_with_retry(
                    lambda client, k, t: client.blpop(k, timeout=t),
                    list_key,
                    timeout
                )
                if notify:
                    # 收到通知,获取结果并删除
                    result = await self.getdel(result_key)
                    if result:
                        return result

                raise TimeoutError(f"等待任务 {task_id} 结果超时")

            except redis.TimeoutError:
                raise TimeoutError(f"等待任务 {task_id} 结果超时")

        except Exception as e:
            logger.error(f"等待任务结果失败 - task_id:{task_id}, error:{str(e)}")
            raise
        finally:
            # 清理通知列表
            try:
                await self.delete(list_key)
            except Exception as e:
                logger.warning(f"清理任务通知列表失败 - task_id:{task_id}, error:{str(e)}")

    async def publish_task_result(self, task_id: str, result_prefix: str, result: Any, expire: int = 30) -> None:
        """发布任务结果

        Args:
            task_id: 任务ID
            result_prefix: 结果key前缀
            result: 结果数据
            expire: 过期时间(秒)
        """
        result_key = f"{result_prefix}{task_id}"
        list_key = f"task:notify:{task_id}"

        try:
            # 存储结果
            await self.set(result_key, result, ex=expire)
            # 发送通知
            await self._execute_with_retry(
                lambda client, k: client.rpush(k, "done"),
                list_key
            )
        except Exception as e:
            logger.error(f"发布任务结果失败 - task_id:{task_id}, error:{str(e)}")
            raise

