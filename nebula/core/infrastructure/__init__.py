"""
基础设施包 - 提供底层基础设施服务
"""

import nebula.core.infrastructure.config as config
import nebula.core.infrastructure.exceptions as exceptions
import nebula.core.infrastructure.logging as logging
import nebula.core.infrastructure.serializer as serializer_module
import nebula.core.infrastructure.singleton as singleton_module
import nebula.core.infrastructure.message as message_module
import nebula.core.infrastructure.decorators as decorators_module

# import nebula.core.infrastructure.injection as injection
# import nebula.core.infrastructure.cache as cache
# import nebula.core.infrastructure.database as database

# 重新导出配置管理
get_settings = config.get_settings
Settings = config.Settings
Environment = config.Environment
get_environment = config.get_environment

# 重新导出异常处理
NebulaException = exceptions.NebulaException
ConfigError = exceptions.ConfigError
DatabaseError = exceptions.DatabaseError
AuthenticationError = exceptions.AuthenticationError
AuthorizationError = exceptions.AuthorizationError
ValidationError = exceptions.ValidationError
NotFoundError = exceptions.NotFoundError
DuplicateError = exceptions.DuplicateError
ExternalServiceError = exceptions.ExternalServiceError
BrowserException = exceptions.BrowserException
YoutubeException = exceptions.YoutubeException
MessageError = exceptions.MessageError
TimeoutError = exceptions.TimeoutError
DebugRetryException = exceptions.DebugRetryException
error_boundary = exceptions.error_boundary
sync_error_boundary = exceptions.sync_error_boundary

# 重新导出日志系统
setup_logging = logging.setup_logging
get_logger = logging.get_logger
log_context = logging.log_context
calculate_time = logging.calculate_time


# 重新导出序列化器
serializer = serializer_module.serializer
Serializer = serializer_module.Serializer
SerializationFormat = serializer_module.SerializationFormat

# 重新导出单例装饰器
singleton = singleton_module.singleton

# 重新导出消息服务
MessageService = message_module.MessageService

# 重新导出装饰器
control = decorators_module.control


__all__ = [
    # 配置管理
    'get_settings', 'Settings', 'Environment', 'get_environment',

    # 异常处理
    'NebulaException', 'ConfigError', 'DatabaseError',
    'AuthenticationError', 'AuthorizationError', 'ValidationError',
    'NotFoundError', 'DuplicateError', 'ExternalServiceError',"BrowserException",
    'MessageError', 'error_boundary', 'sync_error_boundary',

    # 日志系统
    'setup_logging', 'get_logger', 'log_context', 'calculate_time', 'sync_calculate_time',

    # 消息序列化
    'serializer', 'Serializer', 'SerializationFormat',

    # 依赖注入 - 已移除未使用的导出
    # 'DI', 'Container', 'Inject', 'get_container',

    # 单例模式
    'singleton',

    # 消息服务
    'MessageService',

    # 装饰器
    'control',

    # 现有模块的类
    # 'RedisCache', 'MongoDB'
]
