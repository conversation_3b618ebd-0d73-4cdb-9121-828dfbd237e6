"""
MongoDB数据库连接管理模块
"""

from pymongo import MongoClient
from typing import Optional
import nebula.core.infrastructure.config as config
import nebula.core.infrastructure.logging as logging


logger = logging.get_logger(__name__)

class MongoDB:
    """MongoDB连接管理类"""
    
    def __init__(self, settings=None):
        """
        初始化数据库连接管理器
        
        Args:
            settings: 配置对象，如果为None则通过get_settings()获取
        """
        self._client = None
        self._config = None
        self.settings = settings or config.get_settings()
        logger.debug("MongoDB管理器初始化", extra={
            "client_status": "未初始化",
            "db_status": "未初始化"
        })
    
    async def connect(self):
        """连接到MongoDB数据库"""
        if self._client is None:
            self.client  # 触发客户端连接初始化
            logger.info("MongoDB连接已初始化")
        return self
    
    @property
    def client(self) -> MongoClient:
        """获取数据库客户端连接"""
        if not self._client:
            logger.debug("开始创建MongoDB客户端连接")
            try:
                uri = self.settings.mongodb.get_connection_uri()
                self._client = MongoClient(
                    uri,
                    minPoolSize=self.settings.mongodb.min_pool_size,
                    maxPoolSize=self.settings.mongodb.max_pool_size
                )
                logger.info(f"MongoDB客户端连接已创建: {self.settings.mongodb.host}:{self.settings.mongodb.port}/{self.settings.mongodb.database}")
            except Exception as e:
                logger.error(f"MongoDB客户端连接创建失败: {str(e)}", exc_info=True)
                raise
        else:
            logger.debug("使用现有MongoDB客户端连接")
        return self._client
        
    @property
    def db(self):
        """获取数据库实例"""
        if not self._config:
            logger.info("开始创建MongoDB数据库实例")
            try:
                self._config = self.client[self.settings.mongodb.database]
                logger.info(f"MongoDB数据库实例已创建: {self.settings.mongodb.database}")
            except Exception as e:
                logger.error(f"MongoDB数据库实例创建失败: {str(e)}", exc_info=True)
                raise
        else:
            logger.debug("使用现有MongoDB数据库实例")
        return self._config
        
    def get_collection(self, name: str):
        """获取集合
        
        Args:
            name: 集合名称
            
        Returns:
            MongoDB集合对象
        """
        logger.debug(f"请求获取集合: {name}", extra={
            "collection": name,
            "client_status": "已初始化" if self._client is not None else "未初始化",
            "db_status": "已初始化" if self._config is not None else "未初始化"
        })
        
        # 确保数据库连接已初始化
        if self._client is None or self._config is None:
            logger.info("数据库连接未初始化，正在初始化")
            try:
                self.db  # 触发数据库连接初始化
            except Exception as e:
                logger.error(f"数据库连接初始化失败: {str(e)}", exc_info=True)
                raise
        
        try:
            # 获取并返回集合
            collection = self._config[name]
            logger.debug(f"成功获取集合: {name}")
            return collection
        except Exception as e:
            logger.error(f"获取集合 {name} 失败: {str(e)}", exc_info=True)
            raise
        
    async def close(self):
        """关闭数据库连接"""
        if self._client:
            logger.debug("开始关闭MongoDB连接")
            try:
                self._client.close()
                self._client = None
                self._config = None
                logger.info("MongoDB连接已关闭")
            except Exception as e:
                logger.error(f"关闭MongoDB连接失败: {str(e)}", exc_info=True)
                raise
            
    def __del__(self):
        """析构函数，确保连接被正确关闭"""
        logger.debug("MongoDB管理器开始清理")
        if hasattr(self, '_client') and self._client:
            try:
                self._client.close()
            except:
                pass

# db实例会在app_context中创建 