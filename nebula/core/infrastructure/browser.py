#
# JSON.parse(decodeURIComponent($("#RENDER_DATA").innerHTML)).app.videoDetail

# Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36
"""
浏览器自动化服务模块
提供远程浏览器自动化功能
"""

import logging
import base64
import json
from typing import Dict, Optional, List, Any, Union
from datetime import datetime

# 导入基础设施组件
from nebula.core.infrastructure.exceptions import NebulaException, ValidationError, BrowserException, ConfigError
from nebula.core.infrastructure import config

# 获取日志记录器
logger = logging.getLogger(__name__)


class BrowserService:
    """浏览器服务

    提供远程浏览器自动化功能，用于网页内容获取和元素提取。
    """

    def __init__(self, settings: config.Settings = None):
        """初始化浏览器服务

        Args:
            settings: 应用配置
        """
        self.settings = settings or config.get_settings()
        if not self.settings.browser.playwright.endpoint:
            raise ConfigError("Browser服务配置项缺失: endpoint")
        # 从配置中获取WebSocket端点
        self.ws_endpoint = self.settings.browser.playwright.endpoint
        self.browser_type = self.settings.browser.playwright.browser_type
        self.playwright = None
        self.browser = None
        logger.info(f"浏览器服务初始化完成，WebSocket端点: {self.ws_endpoint}")

    async def _ensure_browser(self):
        """确保浏览器已连接

        Args:
            browser_type: 浏览器类型，可选值: chromium, firefox, webkit
        """
        # 导入Playwright
        from playwright.async_api import async_playwright

        if self.playwright is None:
            self.playwright = await async_playwright().start()

        if self.browser is None:
            # 根据浏览器类型连接到相应的浏览器
            if self.browser_type == "chromium":
                self.browser = await self.playwright.chromium.connect(self.ws_endpoint)
            elif self.browser_type == "firefox":
                self.browser = await self.playwright.firefox.connect(self.ws_endpoint)
            elif self.browser_type == "webkit":
                self.browser = await self.playwright.webkit.connect(self.ws_endpoint)
            else:
                raise BrowserException(f"不支持的浏览器类型: {self.browser_type}")

    async def _close_browser(self):
        """断开浏览器连接"""
        if self.browser:
            await self.browser.close()
            self.browser = None

    async def close(self):
        """关闭服务，释放资源"""
        await self._close_browser()
        if self.playwright:
            await self.playwright.stop()
            self.playwright = None

    async def get_page_html(self, url: str, screenshot: bool = False) -> Dict[str, Any]:
        """获取指定URL的HTML内容

        Args:
            url: 要访问的URL
            screenshot: 是否获取截图
            browser_type: 浏览器类型，可选值: chromium, firefox, webkit

        Returns:
            包含HTML内容和可选截图的字典

        Raises:
            BrowserException: 获取页面内容失败
        """
        try:
            # 确保浏览器已连接
            await self._ensure_browser()

            # 创建新页面
            page = await self.browser.new_page()

            try:
                # 导航到页面
                await page.goto(url, wait_until="domcontentloaded")
                # 获取HTML内容
                html = await page.content()

                result = {
                    "html": html
                }

                # 如果需要截图
                if screenshot:
                    screenshot_bytes = await page.screenshot(full_page=True)
                    result["screenshot"] = base64.b64encode(screenshot_bytes).decode('utf-8')
                return result
            finally:
                # 关闭页面
                await page.close()
        except Exception as e:
            raise BrowserException(f"获取页面HTML失败: {str(e)}")

    async def get_page_elements(self, url: str, elements: Dict[str, str], screenshot: bool = False) -> Dict[str, Any]:
        """获取指定URL的指定元素

        Args:
            url: 要访问的URL
            elements: 包含元素名称和XPath/CSS选择器的字典，格式如{"key1": "xpath1", "key2": "css_selector2"}
            screenshot: 是否获取元素截图
            browser_type: 浏览器类型，可选值: chromium, firefox, webkit

        Returns:
            包含元素内容和可选截图的字典

        Raises:
            BrowserException: 获取页面元素失败
        """
        try:
            # 确保浏览器已连接
            await self._ensure_browser()

            # 创建新页面
            page = await self.browser.new_page()

            try:
                # 导航到页面
                await page.goto(url, wait_until="domcontentloaded")

                result = {"elements": {}}

                # 获取每个元素的内容
                for name, selector in elements.items():
                    try:
                        # 处理XPath选择器
                        if selector.startswith("//"):
                            selector = f"xpath={selector}"

                        # 使用选择器查询元素
                        element = await page.wait_for_selector(selector, state="attached", timeout=5000)

                        if element:
                            # 获取元素文本
                            text = await element.text_content() or ""
                            # 获取元素HTML
                            html = await element.inner_html()
                            # 获取元素属性
                            attributes = {}
                            for attr in ["id", "class", "name", "type", "value"]:
                                value = await element.get_attribute(attr)
                                if value:
                                    attributes[attr] = value

                            result["elements"][name] = {
                                "text": text,
                                "html": html,
                                "outerHTML": await element.evaluate("el => el.outerHTML"),
                                "attributes": attributes
                            }

                            # 如果需要截图且元素存在
                            if screenshot:
                                if "elements_screenshots" not in result:
                                    result["elements_screenshots"] = {}

                                element_screenshot = await element.screenshot()
                                result["elements_screenshots"][name] = base64.b64encode(element_screenshot).decode('utf-8')
                        else:
                            result["elements"][name] = None
                    except Exception as e:
                        logger.warning(f"获取元素 {name} 失败: {str(e)}")
                        result["elements"][name] = None

                return result
            finally:
                # 关闭页面
                await page.close()
        except Exception as e:
            raise BrowserException(f"获取页面元素失败: {str(e)}")

    # 以下是扩展方法，可以根据需要使用

    async def execute_script(self, url: str, script: str) -> Any:
        """在页面上执行JavaScript脚本

        Args:
            url: 要访问的URL
            script: 要执行的JavaScript脚本
            browser_type: 浏览器类型，可选值: chromium, firefox, webkit

        Returns:
            脚本执行结果

        Raises:
            BrowserException: 执行脚本失败
        """
        try:
            # 确保浏览器已连接
            await self._ensure_browser()

            # 创建新页面
            page = await self.browser.new_page()

            try:
                # 导航到页面
                await page.goto(url, wait_until="domcontentloaded")

                # 执行脚本
                result = await page.evaluate(script)

                return result
            finally:
                # 关闭页面
                await page.close()
        except Exception as e:
            if isinstance(e, BrowserException):
                raise
            raise BrowserException(f"执行脚本失败: {str(e)}")

    async def take_screenshot(self, url: str, selector: Optional[str] = None, full_page: bool = False) -> bytes:
        """截取页面或元素的截图

        Args:
            url: 要访问的URL
            selector: 元素选择器，如果为None则截取整个页面
            full_page: 是否截取整个页面，仅在selector为None时有效
            browser_type: 浏览器类型，可选值: chromium, firefox, webkit

        Returns:
            截图的二进制数据

        Raises:
            BrowserException: 截图失败
        """
        try:
            # 确保浏览器已连接
            await self._ensure_browser()

            # 创建新页面
            page = await self.browser.new_page()

            try:
                # 导航到页面
                await page.goto(url, wait_until="domcontentloaded")

                # 截取截图
                if selector:
                    # 处理XPath选择器
                    if selector.startswith("//"):
                        selector = f"xpath={selector}"

                    element = await page.wait_for_selector(selector, state="attached", timeout=5000)
                    if not element:
                        raise BrowserException(f"未找到元素: {selector}")

                    screenshot = await element.screenshot()
                else:
                    screenshot = await page.screenshot(full_page=full_page)

                return screenshot
            finally:
                # 关闭页面
                await page.close()
        except Exception as e:
            if isinstance(e, BrowserException):
                raise
            raise BrowserException(f"截图失败: {str(e)}")

    async def get_resource_bytes(self, resource_url: str) -> Dict[str, Any]:
        """获取任意资源的二进制数据

        使用浏览器获取资源，避免直接HTTP请求可能遇到的403错误或其他访问限制

        Args:
            resource_url: 资源URL，可以是图片、视频、音频、文档等任意资源

        Returns:
            包含资源二进制数据和MIME类型的字典

        Raises:
            BrowserException: 获取资源失败
        """
        try:
            # 确保浏览器已连接
            await self._ensure_browser()

            # 创建新页面
            page = await self.browser.new_page()

            try:
                # 设置页面拦截器来捕获资源请求
                resource_data = None
                content_type = None

                async def handle_response(response):
                    nonlocal resource_data, content_type
                    if response.url == resource_url:
                        content_type = response.headers.get('content-type')
                        try:
                            resource_data = await response.body()
                        except Exception as e:
                            logger.warning(f"获取响应体失败(status={response.status}): {str(e)}")

                # 监听响应事件
                page.on('response', handle_response)

                # 导航到资源URL
                response = await page.goto(resource_url, wait_until="domcontentloaded")

                # 如果通过事件处理器没有获取到资源数据，尝试直接从响应获取
                if not resource_data and response.status == 200:
                    content_type = response.headers.get('content-type')
                    resource_data = await response.body()

                if not resource_data:
                    raise BrowserException(f"无法获取资源数据(status={response.status}): {resource_url}")

                return {
                    "data": resource_data,
                    "content_type": content_type
                }
            finally:
                # 关闭页面
                await page.close()
        except Exception as e:
            if isinstance(e, BrowserException):
                raise
            raise BrowserException(f"获取资源失败: {str(e)}")
