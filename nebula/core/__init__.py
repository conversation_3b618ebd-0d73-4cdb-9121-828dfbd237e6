"""
Core包 - 核心业务逻辑

提供Nebula系统的核心功能和业务逻辑。
"""

__version__ = '0.1.0'
__author__ = 'Nebula Team'

import nebula.core.infrastructure as infrastructure
import nebula.core.context as context

# 重新导出基础设施组件
Settings = infrastructure.Settings
Environment = infrastructure.Environment
NebulaException = infrastructure.NebulaException
ConfigError = infrastructure.ConfigError
DatabaseError = infrastructure.DatabaseError
AuthenticationError = infrastructure.AuthenticationError
AuthorizationError = infrastructure.AuthorizationError
ValidationError = infrastructure.ValidationError
NotFoundError = infrastructure.NotFoundError
DuplicateError = infrastructure.DuplicateError
ExternalServiceError = infrastructure.ExternalServiceError
MessageError = infrastructure.MessageError

# 移除未使用的依赖注入容器导出
# DI = infrastructure.DI
# Container = infrastructure.Container
# Inject = infrastructure.Inject

# 导出常用函数
get_settings = infrastructure.get_settings
get_environment = infrastructure.get_environment
error_boundary = infrastructure.error_boundary
# setup_logging = infrastructure.setup_logging
log_context = infrastructure.log_context
get_logger = infrastructure.get_logger
# 重新导出单例装饰器
singleton = infrastructure.singleton
# 移除未使用的依赖注入容器相关函数
# get_container = infrastructure.get_container

# 导出上下文相关函数
get_app_context = context.get_app_context
init_application = context.init_application
graceful_shutdown = context.graceful_shutdown

def get_version() -> str:
    """
    获取核心包版本

    Returns:
        版本字符串
    """
    return __version__