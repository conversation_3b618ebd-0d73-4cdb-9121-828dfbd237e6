from enum import Enum
from typing import Dict, Any, ClassVar
from .base import MessageEntity, MessageType

class VideoTaskMessage(MessageEntity):
    """视频任务消息

    用于处理视频相关的异步任务。
    """
    message_type: ClassVar[MessageType] = MessageType.QUEUE
    topic: ClassVar[str] = "nebula.video_tasks"

    class ActionEnum(str, Enum):
        """视频任务动作类型"""
        CREATE_VIDEO = "CREATE_VIDEO"
        UPDATE_VIDEO = "UPDATE_VIDEO"
        DELETE_VIDEO = "DELETE_VIDEO"
        ADD_TAGS = "ADD_TAGS"
        REMOVE_TAGS = "REMOVE_TAGS"
        UPDATE_SUBTITLES = "UPDATE_SUBTITLES"
        ANALYZE_CONTENT = "ANALYZE_CONTENT"
        EXTRACT_SHARE_INFO = "EXTRACT_SHARE_INFO"
        IMPORT_FROM_SHARE = "IMPORT_FROM_SHARE"

    action: ActionEnum

    @property
    def data(self) -> Dict[str, Any]:
        """获取消息数据

        Returns:
            消息数据字典
        """
        dt = super().data
        dt.update({"action": self.action})
        return dt