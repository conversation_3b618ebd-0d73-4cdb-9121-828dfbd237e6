"""
Cloudflare任务消息协议
"""

from enum import Enum
from typing import Dict, Any, ClassVar
from .base import MessageEntity, MessageType


class CloudflareTaskMessage(MessageEntity):
    """Cloudflare任务消息"""
    message_type: ClassVar[MessageType] = MessageType.QUEUE
    topic: ClassVar[str] = "nebula.cloudflare_tasks"
    
    class ActionEnum(str, Enum):
        """Cloudflare任务动作类型"""
        SAVE_CONFIG = "SAVE_CONFIG"
        CREATE_DNS_RECORD = "CREATE_DNS_RECORD"
        UPDATE_DNS_RECORD = "UPDATE_DNS_RECORD"
        DELETE_DNS_RECORD = "DELETE_DNS_RECORD"
        SYNC_DOMAINS = "SYNC_DOMAINS"
        VALIDATE_API_KEY = "VALIDATE_API_KEY"
    
    action: ActionEnum

    @property
    def data(self) -> Dict[str, Any]:
        """获取消息数据

        Returns:
            消息数据字典
        """
        dt = super().data
        dt.update({"action": self.action})
        return dt