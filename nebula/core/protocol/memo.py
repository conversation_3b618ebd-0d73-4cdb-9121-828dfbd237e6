"""
备忘录任务消息协议
"""

from enum import Enum
from typing import Dict, Any, ClassVar
from .base import MessageEntity, MessageType


class MemoTaskMessage(MessageEntity):
    """备忘录任务消息"""
    message_type: ClassVar[MessageType] = MessageType.QUEUE
    topic: ClassVar[str] = "nebula.memo_tasks"

    class ActionEnum(str, Enum):
        """备忘录任务动作类型"""
        CREATE_TEXT = "CREATE_TEXT"
        CREATE_IMAGE = "CREATE_IMAGE"
        CREATE_FILE = "CREATE_FILE"
        CREATE_SHARE = "CREATE_SHARE"
        UPDATE_MEMO = "UPDATE_MEMO"
        DELETE_MEMO = "DELETE_MEMO"
        ANALYZE_CONTENT = "ANALYZE_CONTENT"

    action: ActionEnum

    @property
    def data(self) -> Dict[str, Any]:
        """获取消息数据

        Returns:
            消息数据字典
        """
        dt = super().data
        dt.update({"action": self.action})
        return dt
