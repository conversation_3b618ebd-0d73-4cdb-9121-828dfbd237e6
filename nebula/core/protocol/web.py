"""
Web推送队列消息协议

用于在消息队列中传递需要推送到Web客户端的内容。
消费者接收到此消息后，会将内容推送给指定的Web客户端。
"""

from typing import Dict, Any, ClassVar, Optional
from pydantic import Field
from .base import MessageEntity, MessageType


class WebNotification(MessageEntity):
    """Web推送队列消息
    
    此消息通过消息队列传递，用于指示消费者将特定内容推送给Web客户端。
    消费者会监听此队列，并将消息内容推送到对应的Web连接。
    """
    message_type: ClassVar[MessageType] = MessageType.QUEUE
    topic: ClassVar[str] = "nebula.notifications"
    from_id: Optional[str] = None
    to_web_topic: str  # 目标Web主题
    failed: Optional[bool] = None
    event: str
    text: str


    @property
    def data(self) -> Dict[str, Any]:
        ret = {
            "response_mode": self.response_mode,
            "to_web_topic": self.to_web_topic,
            "event": self.event,
            "text": self.text,
        } 
        if self.failed is not None:
            ret["failed"] = self.failed
        if self.from_id is not None:
            ret["from_id"] = self.from_id
        if self.metadata is not None:
            ret["metadata"] = self.metadata
        return ret