"""
Telegram任务消息协议
"""

from enum import Enum
from typing import Dict, Any, ClassVar
from .base import MessageEntity, MessageType


class TelegramTaskMessage(MessageEntity):
    """Telegram任务消息"""
    message_type: ClassVar[MessageType] = MessageType.QUEUE
    topic: ClassVar[str] = "nebula.telegram_tasks"

    class ActionEnum(str, Enum):
        """Telegram任务动作类型"""
        # 主动发送消息的操作
        SEND_MESSAGE = "SEND_MESSAGE"  # 发送文本消息
        SEND_PHOTO = "SEND_PHOTO"      # 发送图片
        SEND_DOCUMENT = "SEND_DOCUMENT"  # 发送文档

        # 事件处理操作
        PROCESS_EVENT = "PROCESS_EVENT"  # 处理所有类型的Telegram事件

    action: ActionEnum

    @property
    def data(self) -> Dict[str, Any]:
        """获取消息数据

        Returns:
            消息数据字典
        """
        dt = super().data
        dt.update({"action": self.action})
        return dt
