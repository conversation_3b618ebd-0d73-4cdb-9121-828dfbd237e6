"""
基础消息协议定义
"""

from enum import Enum
from typing import Dict, Any, ClassVar, Optional
from datetime import datetime
from abc import ABC, abstractmethod
import uuid
from pydantic import BaseModel, Field

from enum import Enum

class ResponseMode(Enum):
    WEBSOCKET = "websocket"  # WebSocket推送
    CALLBACK = "callback"  # 回调
    SYNC = "sync"  # 同步模式


class MessageType(str, Enum):
    """消息类型枚举"""
    WEBSOCKET = "websocket"
    TASK = "task"
    NOTIFICATION = "notification"
    SYSTEM = "system"
    QUEUE = "queue"

    def __str__(self) -> str:
        return self.value


class MessageEntity(BaseModel, ABC):
    """统一的消息实体基类"""
    # 类级别的配置
    message_type: ClassVar[MessageType]
    topic: ClassVar[str]

    # 实例属性
    task_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Optional[Dict[str, Any]] = Field(default=None)
    priority: int = Field(default=0, ge=0, le=10)
    response_mode: Optional[ResponseMode] = None
    callback: Optional[str] = Field(default=None)

    # 重试相关字段
    headers: Optional[Dict[str, Any]] = Field(default=None)
    retry_count: int = Field(default=0, description="当前重试次数")
    max_retry_count: int = Field(default=3, description="最大重试次数")
    next_retry_time: Optional[datetime] = Field(default=None, description="下次重试时间")
    last_error: Optional[str] = Field(default=None, description="上次失败原因")
    is_delayed: bool = Field(default=False, description="是否为延迟执行的消息")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

    @property
    def data(self) -> Dict[str, Any]:
        """
        主要用于message_handler从队列消息接收任务时将消息实体去壳转化为与类型相关消息
        注意区分: MessageEntity 作为任务队列消息的载体
        与 message : T做区分
        """
        base_data = {
            "task_id": self.task_id,
            "metadata": self.metadata,
            "priority": self.priority,
            "response_mode": self.response_mode,
            "callback": self.callback
        }
        return base_data

    @classmethod
    def get_topic(cls) -> str:
        """获取消息主题"""
        return cls.topic

    @classmethod
    def get_message_type(cls) -> MessageType:
        """获取消息类型"""
        return cls.message_type

    def to_message_dict(self) -> Dict[str, Any]:
        """生成完整的消息数据字典(这个方法是给队列使用的,包含了任务的壳)"""
        return {
            "type": self.message_type,
            "topic": self.topic,
            "task_id": self.task_id,
            "timestamp": self.timestamp,
            "priority": self.priority,
            "response_mode": self.response_mode,
            "callback": self.callback,
            "retry_count": self.retry_count,
            "max_retry_count": self.max_retry_count,
            "next_retry_time": self.next_retry_time,
            "last_error": self.last_error,
            "is_delayed": self.is_delayed,
            **self.data
        }