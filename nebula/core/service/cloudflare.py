"""
Cloudflare服务模块
"""
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
import aiohttp
import logging
import asyncio

from nebula.core.infrastructure import (
    get_settings,Settings,
    NebulaException, NotFoundError, ValidationError, ExternalServiceError,
    get_logger, log_context, sync_error_boundary, error_boundary
)

from nebula.core.infrastructure.exceptions import BusinessError
from nebula.core.repository.cloudflare import Entity, CloudflareRepository

logger = get_logger(__name__)

class CloudflareService:
    """Cloudflare域名管理服务"""
    
    def __init__(self, settings: Settings = None):
        """
        初始化Cloudflare服务
        
        Args:
            repo: CloudflareRepository实例，如果为None则创建新实例
            settings: 配置对象，如果为None则通过get_settings()获取
        """
        self.api_base = "https://api.cloudflare.com/client/v4"
        # 获取仓储实例
        self.repo = CloudflareRepository()
        self.settings = settings
    
    def get_headers(self, account: Entity.Account) -> Dict:
        """获取API请求头"""
        if not account:
            raise BusinessError("Cloudflare账户未配置")
            
        return {
            "X-Auth-Email": account.account_id,
            "X-Auth-Key": account.api_key,
            "Content-Type": "application/json"
        }
    
    async def get_account(self, account_id: str=None) -> Entity.Account:
        """获取Cloudflare账户"""
        if not account_id:
            account = await self.repo.find_one({})
        else:
            account = await self.repo.find_one({"account_id": account_id})
            
        if not account:
            raise BusinessError("Cloudflare账户不存在")
        return account
    
    async def set_account(self, account: Entity.Account):
        """设置Cloudflare账户"""
        await self.repo.save_account(account)

    async def validate_api_config(self, account: Entity.Account) -> Dict:
        """验证API配置有效性"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.api_base}/user", headers=self.get_headers(account)) as response:
                result = await response.json()
                logger.info(f"发起Cloudflare API请求 -> 验证API配置: {f'{self.api_base}/user'} ({result})")

                if not result.get('success'):
                    raise BusinessError(f"Cloudflare API: {result.get('errors')[0].get('message','未知错误')}",details={"cloudflare_response":result})

    async def update_dns_record(self, account: Entity.Account, domain_name: str, record: Entity.DnsRecord) -> Entity.DnsRecord:
        """更新DNS记录"""
        # 获取域名信息
        domain = account.domain_list.get(domain_name)
        if not domain:
            raise BusinessError(f"不存在域名: {domain_name}",details={"domain_name":domain_name})

        # 更新DNS记录
        url = f"{self.api_base}/zones/{domain.zone_id}/dns_records/{record.record_id}"
        headers = self.get_headers(account)
        data = record.to_dict()
        data["name"] = data.pop("sub")
        async with aiohttp.ClientSession() as session:
            async with session.put(url, headers=headers, json=data) as response:
                result = await response.json()
                logger.info(f"发起Cloudflare API请求 -> 更新DNS记录: {url} ({result})")
                if not result.get('success'):
                    raise BusinessError(f"Cloudflare API: {result.get('errors')[0].get('message','未知错误')}",details={"cloudflare_response":result})
            
        # 更新数据库
        record.updated_at = datetime.now()
        await self.repo.save_dns_record(account,domain_name, record)
        return record

    async def sync_domain_list(self, account: Entity.Account) -> List[Entity.Domain]:
        """同步域名列表"""
        # 获取域名列表
        url = f"{self.api_base}/zones"
        headers = self.get_headers(account)
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                result = await response.json()
                logger.info(f"发起Cloudflare API请求 -> 同步域名列表: {url} ({result})")
                if not result.get('success'):
                    raise BusinessError(f"Cloudflare API: {result.get('errors')[0].get('message','未知错误')}",details={"cloudflare_response":result})

                zones = result.get("result", [])
                synced_domains = []
                
                # 清空现有域名列表
                account.domain_list = {}
                
                # 同步每个域名
                for zone in zones:
                    domain_name = zone["name"]
                    zone_id = zone["id"]
                    
                    # 获取DNS记录
                    dns_url = f"{self.api_base}/zones/{zone_id}/dns_records"
                    
                    async with session.get(dns_url, headers=headers) as dns_response:
                        dns_records = []
                        if dns_response.status == 200:
                            dns_result = await dns_response.json()
                            logger.info(f"发起Cloudflare API请求 -> 获取DNS记录: {dns_url} ({dns_result})")
                            cf_dns_records = dns_result.get("result", [])
                            # 转换为我们的格式
                            for dns in cf_dns_records:
                                dns_record = Entity.DnsRecord(
                                    record_id=dns["id"],
                                    type=dns["type"],
                                    sub=dns["name"].replace(f".{domain_name}", "") if dns["name"].endswith(domain_name) else dns["name"],
                                    content=dns["content"],
                                    ttl=dns["ttl"],
                                    proxied=dns.get("proxied", False),
                                    created_at=datetime.now(),
                                    updated_at=datetime.now()
                                )
                                
                                # 添加priority字段处理（对MX和SRV记录）
                                if dns["type"] in ["MX", "SRV"] and "priority" in dns:
                                    dns_record.priority = dns["priority"]
                                
                                dns_records.append(dns_record)
                    
                    # 添加到域名列表
                    domain = Entity.Domain(
                        zone_id=zone_id,
                        status=zone["status"],
                        dns=dns_records,
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    
                    account.domain_list[domain_name] = domain
                    synced_domains.append(domain_name)
        
        account.updated_at = datetime.now()
        await self.repo.save_account(account)    
            
        return True
                    
    async def create_dns_record(self, account: Entity.Account, domain_name: str, record: Entity.DnsRecord) -> Entity.DnsRecord:
        """创建DNS记录"""
        # 获取域名信息
        domain = account.domain_list.get(domain_name)
        if not domain:
            raise BusinessError(f"不存在域名: {domain_name}",details={"domain_name":domain_name})
        # 创建DNS记录
        url = f"{self.api_base}/zones/{domain.zone_id}/dns_records"
        headers = self.get_headers(account)
        api_data = record.to_dict()
        api_data["name"] = api_data.pop("sub")  #cloudflare api 要求name字段
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=api_data) as response:
                result = await response.json()
                logger.info(f"发起Cloudflare API请求 -> 创建DNS记录: {url} ({result})")
                if response.status != 200:
                    raise BusinessError(f"Cloudflare API: {result.get('errors')[0].get('message','未知错误')}",details={"cloudflare_response":result})
                
                # 保存到数据库
                record.record_id = result["result"]["id"]
                await self.repo.save_dns_record(account, domain_name, record)
                return record

    async def delete_dns_record(self, account: Entity.Account, domain_name: str, record_id: str) -> bool:
        """删除DNS记录"""
        # 获取域名信息
        domain = account.domain_list.get(domain_name)
        if not domain:
            raise BusinessError(f"不存在域名: {domain_name}",details={"domain_name":domain_name})

        # 删除DNS记录
        url = f"{self.api_base}/zones/{domain.zone_id}/dns_records/{record_id}"
        headers = self.get_headers(account)
        
        async with aiohttp.ClientSession() as session:
            async with session.delete(url, headers=headers) as response:
                result = await response.json()
                logger.info(f"发起Cloudflare API请求 -> 删除DNS记录: {url} ({result})")
                if not result.get('success'):
                    if not result.get('errors')[0].get('code') == 81044:
                        # 除记录不存在以外的错误
                        raise BusinessError(f"Cloudflare API: {result.get('errors')[0].get('message','未知错误')}",details={"cloudflare_response":result})
                        
        # 从数据库删除
        return await self.repo.delete_dns_record(account, domain_name, record_id)

# cloudflare_service的实例会在app_context中创建 