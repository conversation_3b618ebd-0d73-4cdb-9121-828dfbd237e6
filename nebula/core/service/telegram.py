"""
Telegram服务模块
提供Telegram机器人功能

此模块实现了Telegram机器人的核心功能，包括：
1. 事件处理：处理来自Telegram的各种事件（命令、消息、图片、文档等）
2. Webhook管理：设置和管理Telegram Webhook
3. 用户状态管理：跟踪用户的当前状态和命令上下文

模块采用面向对象的设计，每种事件类型都有对应的处理类，使代码结构更加清晰和易于维护。
"""

import logging
import asyncio
import re
import uuid
import aiohttp
from typing import Dict, Any, Optional, Tuple
from telegram import Bot, Update
from enum import Enum

from nebula.core.infrastructure import (
    singleton,
    get_settings, Settings,
    get_logger
)
from nebula.core.service import provider

from nebula.core.repository.memo import Entity as MemoEntity

# 获取日志记录器
logger = get_logger(__name__)

#------------------------------------------------------------------------------
# 枚举类型
#------------------------------------------------------------------------------

class CommandType(Enum):
    """
    命令类型枚举

    定义了系统支持的命令类型，用于用户状态管理和命令处理
    """
    FIND = "find"             # 查找备忘录
    MEMO_ADD = "newmemo"      # 添加备忘录
    VIDEO_DOWNLOAD = "videodownload"  # 下载视频

class EventType(str, Enum):
    """
    事件类型枚举

    定义了系统支持的事件类型，用于事件分类和处理
    """
    COMMAND = "command"        # 命令事件
    MESSAGE = "message"        # 消息事件
    PHOTO = "photo"            # 图片事件
    DOCUMENT = "document"      # 文档事件
    CALLBACK_QUERY = "callback_query"  # 回调查询事件
    UNKNOWN = "unknown"        # 未知事件

#------------------------------------------------------------------------------
# 事件处理策略
#------------------------------------------------------------------------------

class TelegramEventHandler:
    """
    Telegram事件处理策略接口

    定义了处理Telegram事件的通用接口，所有具体的处理策略都实现此接口。
    """
    async def handle(self, event: 'TelegramEvent', service: 'TelegramService') -> None:
        """
        处理事件的方法

        Args:
            event: 要处理的事件
            service: Telegram服务实例

        Returns:
            None
        """
        raise NotImplementedError("子类必须实现handle方法")

# 命令事件处理策略
class CommandFindHandler(TelegramEventHandler):
    """处理查找备忘录命令的策略"""
    async def handle(self, event: 'TelegramEvent', service: 'TelegramService') -> None:
        # 设置用户状态
        service._set_user_state(event.user_id, CommandType.FIND)

        await service.bot.send_message(
            chat_id=event.chat_id,
            text="请输入要搜索的关键字!"
        )

class CommandMemoAddHandler(TelegramEventHandler):
    """处理添加备忘录命令的策略"""
    async def handle(self, event: 'TelegramEvent', service: 'TelegramService') -> None:
        # 设置用户状态
        service._set_user_state(event.user_id, CommandType.MEMO_ADD)

        await service.bot.send_message(
            chat_id=event.chat_id,
            text="请输入要录入的备忘内容!"
        )

class CommandVideoDownloadHandler(TelegramEventHandler):
    """处理视频下载命令的策略"""
    async def handle(self, event: 'TelegramEvent', service: 'TelegramService') -> None:
        # 设置用户状态
        service._set_user_state(event.user_id, CommandType.VIDEO_DOWNLOAD)

        await service.bot.send_message(
            chat_id=event.chat_id,
            text="请发送包含视频链接的消息!"
        )

class CommandUnknownHandler(TelegramEventHandler):
    """处理未知命令的策略"""
    async def handle(self, event: 'TelegramEvent', service: 'TelegramService') -> None:
        await service.bot.send_message(
            chat_id=event.chat_id,
            text="未知命令，请使用 /find, /newmemo 或 /videodownload"
        )

# 消息事件处理策略
class MessageFindHandler(TelegramEventHandler):
    """处理查找备忘录消息的策略"""
    async def handle(self, event: 'TelegramEvent', service: 'TelegramService') -> None:
        await service._process_find_memo(event.update)

class MessageMemoAddHandler(TelegramEventHandler):
    """处理添加备忘录消息的策略"""
    async def handle(self, event: 'TelegramEvent', service: 'TelegramService') -> None:
        await service._process_new_memo(event.update)

class MessageVideoDownloadHandler(TelegramEventHandler):
    """处理视频下载消息的策略"""
    async def handle(self, event: 'TelegramEvent', service: 'TelegramService') -> None:
        await service._process_video_download(event.update)

# 图片事件处理策略
class PhotoMemoAddHandler(TelegramEventHandler):
    """处理添加图片备忘录的策略"""
    async def handle(self, event: 'TelegramEvent', service: 'TelegramService') -> None:
        await service._process_new_memo(event.update)

class PhotoDefaultHandler(TelegramEventHandler):
    """处理默认图片消息的策略"""
    async def handle(self, event: 'TelegramEvent', service: 'TelegramService') -> None:
        await service.bot.send_message(
            chat_id=event.chat_id,
            text="请先使用 /newmemo 命令进入备忘录添加模式"
        )

# 文档事件处理策略
class DocumentMemoAddHandler(TelegramEventHandler):
    """处理添加文档备忘录的策略"""
    async def handle(self, event: 'TelegramEvent', service: 'TelegramService') -> None:
        await service._process_new_memo(event.update)

class DocumentDefaultHandler(TelegramEventHandler):
    """处理默认文档消息的策略"""
    async def handle(self, event: 'TelegramEvent', service: 'TelegramService') -> None:
        await service.bot.send_message(
            chat_id=event.chat_id,
            text="请先使用 /newmemo 命令进入备忘录添加模式"
        )

# 回调查询事件处理策略
class CallbackQueryHandler(TelegramEventHandler):
    """处理回调查询的策略"""
    async def handle(self, event: 'TelegramEvent', service: 'TelegramService') -> None:
        await service.bot.answer_callback_query(
            callback_query_id=event.update.callback_query.id
        )

# 未知事件处理策略
class UnknownEventHandler(TelegramEventHandler):
    """处理未知事件的策略"""
    async def handle(self, event: 'TelegramEvent', service: 'TelegramService') -> None:
        if event.chat_id:
            await service.bot.send_message(
                chat_id=event.chat_id,
                text="抱歉，我无法处理这种类型的消息"
            )
        else:
            logger.warning(f"收到未知事件，但无法确定聊天ID: {event.update}")

# 处理策略工厂
class TelegramEventHandlerFactory:
    """
    Telegram事件处理策略工厂

    根据事件类型和用户状态创建适当的处理策略。
    """
    @staticmethod
    def create_handler(event_type: EventType, command: str = None, user_state: CommandType = None) -> TelegramEventHandler:
        """
        创建处理策略

        Args:
            event_type: 事件类型
            command: 命令字符串（对于命令事件）
            user_state: 用户状态（对于非命令事件）

        Returns:
            适当的处理策略
        """
        if event_type == EventType.COMMAND:
            # 命令事件
            if command == CommandType.FIND.value:
                return CommandFindHandler()
            elif command == CommandType.MEMO_ADD.value:
                return CommandMemoAddHandler()
            elif command == CommandType.VIDEO_DOWNLOAD.value:
                return CommandVideoDownloadHandler()
            else:
                return CommandUnknownHandler()
        elif event_type == EventType.MESSAGE:
            # 消息事件
            if user_state == CommandType.FIND:
                return MessageFindHandler()
            elif user_state == CommandType.MEMO_ADD:
                return MessageMemoAddHandler()
            else:
                return MessageVideoDownloadHandler()
        elif event_type == EventType.PHOTO:
            # 图片事件
            if user_state == CommandType.MEMO_ADD:
                return PhotoMemoAddHandler()
            else:
                return PhotoDefaultHandler()
        elif event_type == EventType.DOCUMENT:
            # 文档事件
            if user_state == CommandType.MEMO_ADD:
                return DocumentMemoAddHandler()
            else:
                return DocumentDefaultHandler()
        elif event_type == EventType.CALLBACK_QUERY:
            # 回调查询事件
            return CallbackQueryHandler()
        else:
            # 未知事件
            return UnknownEventHandler()

#------------------------------------------------------------------------------
# 事件基类
#------------------------------------------------------------------------------

class TelegramEvent:
    """
    Telegram事件基类

    所有具体事件类型都继承自此基类，提供了通用的属性和方法。
    每个事件对象在初始化时就确定了处理策略。
    """
    def __init__(self, update: Update, event_type: EventType, handler: TelegramEventHandler):
        """
        初始化事件对象

        Args:
            update: Telegram更新对象
            event_type: 事件类型
            handler: 事件处理策略
        """
        self.update = update
        self.event_type = event_type
        self.chat_id = update.effective_chat.id if update.effective_chat else None
        self.user_id = update.effective_user.id if update.effective_user else None
        self.message = update.message
        self.callback_query = update.callback_query
        self.data = {}  # 附加数据，可用于存储事件处理过程中的临时信息
        self.handler = handler  # 事件处理策略

        # 通知相关属性
        self.notification_needed = False
        self.notification_title = ""

    def mark_for_notification(self, title: str) -> None:
        """
        标记需要通知的事件

        Args:
            title: 通知标题
        """
        self.notification_needed = True
        self.notification_title = title

    async def onProcess(self, service: 'TelegramService') -> None:
        """
        处理事件

        调用预先确定的处理策略来处理事件。

        Args:
            service: Telegram服务实例

        Returns:
            None
        """
        await self.handler.handle(self, service)

#------------------------------------------------------------------------------
# 事件子类 - 命令事件
#------------------------------------------------------------------------------

class CommandEvent(TelegramEvent):
    """
    命令事件

    处理以 / 开头的命令消息，如 /find, /newmemo, /videodownload 等。
    在初始化时就确定处理策略。
    """
    def __init__(self, update: Update, command: str):
        """
        初始化命令事件

        Args:
            update: Telegram更新对象
            command: 命令字符串（不包含/前缀）
        """
        # 创建适当的处理策略
        handler = TelegramEventHandlerFactory.create_handler(EventType.COMMAND, command=command)

        super().__init__(update, EventType.COMMAND, handler)
        self.command = command
        self.args = update.message.text.split()[1:] if update.message and update.message.text else []
        self.data = {"command": command, "args": self.args}

#------------------------------------------------------------------------------
# 事件子类 - 消息事件
#------------------------------------------------------------------------------

class MessageEvent(TelegramEvent):
    """
    消息事件

    处理普通文本消息。在初始化时就确定处理策略。
    """
    def __init__(self, update: Update, user_state: CommandType = None):
        """
        初始化消息事件

        Args:
            update: Telegram更新对象
            user_state: 用户当前状态
        """
        # 创建适当的处理策略
        handler = TelegramEventHandlerFactory.create_handler(EventType.MESSAGE, user_state=user_state)

        super().__init__(update, EventType.MESSAGE, handler)
        self.text = update.message.text if update.message else None
        self.data = {"text": self.text}

        url_match = re.search(r'(https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]', self.text)

        if url_match:
            video_url = url_match.group()
            if video_url:
                self.mark_for_notification(
                    title=f"从分享链接里视频下载: {self.text}"
                )
        # 如果用户状态是 MEMO_ADD，标记为需要通知
        elif user_state == CommandType.MEMO_ADD:
            self.mark_for_notification(
                title=f"从telegram创建文本备忘录: {self.text}"
            )

#------------------------------------------------------------------------------
# 事件子类 - 图片和文档事件
#------------------------------------------------------------------------------

class PhotoEvent(TelegramEvent):
    """
    图片事件

    处理用户发送的图片消息。在初始化时就确定处理策略。
    """
    def __init__(self, update: Update, user_state: CommandType = None):
        """
        初始化图片事件

        Args:
            update: Telegram更新对象
            user_state: 用户当前状态
        """
        # 创建适当的处理策略
        handler = TelegramEventHandlerFactory.create_handler(EventType.PHOTO, user_state=user_state)

        super().__init__(update, EventType.PHOTO, handler)
        self.photo = update.message.photo[-1] if update.message and update.message.photo else None
        self.caption = update.message.caption if update.message else None
        self.data = {"caption": self.caption}
        self.mark_for_notification(
            title=f"从telegram上传图片创建备忘录: {self.caption}"
        )

class DocumentEvent(TelegramEvent):
    """
    文档事件

    处理用户发送的文档消息。在初始化时就确定处理策略。
    """
    def __init__(self, update: Update, user_state: CommandType = None):
        """
        初始化文档事件

        Args:
            update: Telegram更新对象
            user_state: 用户当前状态
        """
        # 创建适当的处理策略
        handler = TelegramEventHandlerFactory.create_handler(EventType.DOCUMENT, user_state=user_state)

        super().__init__(update, EventType.DOCUMENT, handler)
        self.document = update.message.document if update.message else None
        self.file_name = update.message.document.file_name if update.message and update.message.document else None
        self.data = {"file_name": self.file_name}
        self.mark_for_notification(
            title=f"从telegram上传文档创建备忘录: {self.file_name}"
        )

#------------------------------------------------------------------------------
# 事件子类 - 回调查询和未知事件
#------------------------------------------------------------------------------

class CallbackQueryEvent(TelegramEvent):
    """
    回调查询事件

    处理用户与内联键盘交互产生的回调查询。
    在初始化时就确定处理策略。
    """
    def __init__(self, update: Update):
        """
        初始化回调查询事件

        Args:
            update: Telegram更新对象
        """
        # 创建适当的处理策略
        handler = TelegramEventHandlerFactory.create_handler(EventType.CALLBACK_QUERY)

        super().__init__(update, EventType.CALLBACK_QUERY, handler)
        self.callback_data = update.callback_query.data if update.callback_query else None
        self.message = update.callback_query.message if update.callback_query else None
        self.data = {"callback_data": self.callback_data}

class UnknownEvent(TelegramEvent):
    """
    未知事件

    处理系统无法识别的事件类型。
    在初始化时就确定处理策略。
    """
    def __init__(self, update: Update):
        """
        初始化未知事件

        Args:
            update: Telegram更新对象
        """
        # 创建适当的处理策略
        handler = TelegramEventHandlerFactory.create_handler(EventType.UNKNOWN)

        super().__init__(update, EventType.UNKNOWN, handler)

#------------------------------------------------------------------------------
# 解析器类
#------------------------------------------------------------------------------

class TelegramParser:
    """
    Telegram更新解析器

    负责将Telegram的原始更新数据解析为事件对象，以便后续处理。
    根据更新的类型创建不同的事件对象。
    """

    def __init__(self, bot: Bot, telegram_service: 'TelegramService'):
        """
        初始化解析器

        Args:
            bot: Telegram机器人实例
            telegram_service: Telegram服务实例，用于获取用户状态
        """
        self.bot = bot
        self.telegram_service = telegram_service

    def parse(self, update_data: Dict[str, Any]) -> Tuple[Any, Update]:
        """
        解析Telegram更新数据，返回事件对象和原始更新对象

        根据更新的类型创建不同的事件对象，如命令事件、消息事件、图片事件等。

        Args:
            update_data: Telegram更新数据，通常是从webhook接收到的JSON数据

        Returns:
            事件对象和原始更新对象的元组
        """
        # 解析更新数据为Update对象
        update = Update.de_json(update_data, self.bot)

        # 获取用户ID和状态
        user_id = update.effective_user.id if update.effective_user else None
        user_state = self.telegram_service._get_user_state(user_id) if user_id else None

        # 根据更新类型创建不同的事件对象
        if update.message:
            if update.message.text and update.message.text.startswith('/'):
                # 命令消息（以/开头）
                command = update.message.text.split()[0][1:].lower()  # 去掉/并转小写
                return CommandEvent(update, command), update
            elif update.message.photo:
                # 图片消息
                return PhotoEvent(update, user_state), update
            elif update.message.document:
                # 文档消息
                return DocumentEvent(update, user_state), update
            else:
                # 普通文本消息
                return MessageEvent(update, user_state), update
        elif update.callback_query:
            # 回调查询（内联键盘按钮点击）
            return CallbackQueryEvent(update), update
        else:
            # 未知类型的更新
            return UnknownEvent(update), update

#------------------------------------------------------------------------------
# 服务类
#------------------------------------------------------------------------------

@singleton
class TelegramService:
    """
    Telegram服务

    提供Telegram机器人的核心功能，包括：
    1. Webhook管理：设置和获取Telegram Webhook
    2. 事件处理：解析和处理Telegram事件
    3. 用户状态管理：跟踪用户的当前状态和命令上下文

    此类是单例模式，确保整个应用中只有一个实例。
    """

    def __init__(self, settings: Settings = None):
        """
        初始化Telegram服务

        Args:
            settings: 通过依赖注入提供的配置对象，如果为None则使用全局配置
        """
        # 通过依赖注入获取配置
        self.settings = settings or get_settings()

        # 从配置中获取Telegram相关设置
        self.token = self.settings.telegram.token
        self.webhook_url = self.settings.telegram.webhook_url
        self.webhook_path = self.settings.telegram.webhook_path
        self.allowed_user_ids = self.settings.telegram.allowed_user_ids

        # 直接创建Bot实例，不使用Application
        self.bot = Bot(token=self.token)

        # 用户状态管理
        self._user_states: Dict[int, Dict[str, Any]] = {}

        # 创建解析器（注意：这里需要传递self作为telegram_service参数）
        self.parser = TelegramParser(self.bot, self)

        logger.info("Telegram服务初始化完成")

    #--------------------------------------------------------------------------
    # Webhook管理
    #--------------------------------------------------------------------------

    async def setup_webhook(self, drop_pending_updates: bool = True) -> Dict[str, Any]:
        """
        设置Webhook

        注意：此方法只需要调用一次。Telegram服务器会记住webhook URL，直到明确删除或设置新的webhook。
        每次重启应用时，不需要重新设置webhook，除非想更改webhook URL或其他参数。

        Args:
            drop_pending_updates: 是否清除积压的更新

        Returns:
            Webhook信息
        """
        try:
            # 设置Webhook URL
            webhook_url = f"{self.webhook_url}{self.webhook_path}"

            # 设置webhook
            # 这个API调用会告诉Telegram服务器将更新发送到指定的webhook URL
            await self.bot.set_webhook(
                url=webhook_url,
                drop_pending_updates=drop_pending_updates,
                allowed_updates=['message', 'edited_message', 'callback_query'],
                max_connections=40
            )

            logger.info(f"Telegram webhook设置成功: {webhook_url}")
            logger.info(f"参数: drop_pending_updates={drop_pending_updates}, allowed_updates=['message', 'edited_message', 'callback_query'], max_connections=40")

            # 获取Webhook信息
            webhook_info = await self.get_webhook_info()
            return webhook_info
        except Exception as e:
            logger.error(f"设置Webhook失败: {str(e)}")
            raise

    async def get_webhook_info(self) -> Dict[str, Any]:
        """
        获取Webhook信息

        Returns:
            Webhook信息字典
        """
        info = await self.bot.get_webhook_info()
        return info.to_dict()

    #--------------------------------------------------------------------------
    # 事件处理
    #--------------------------------------------------------------------------

    def parse_update(self, update_data: Dict[str, Any]) -> Tuple[Any, Update]:
        """
        解析Telegram更新数据

        将原始JSON更新数据解析为事件对象和Update对象。

        Args:
            update_data: Telegram更新数据，通常是从webhook接收到的JSON数据

        Returns:
            事件对象和原始更新对象的元组
        """
        return self.parser.parse(update_data)

    async def process_event(self, event: TelegramEvent) -> None:
        """
        处理Telegram事件

        这是事件处理的入口点，负责权限检查和调用事件对象的处理方法。

        Args:
            event: Telegram事件对象

        Returns:
            None
        """
        # 记录事件类型
        logger.info(f"处理事件: {event.event_type}")

        # 检查用户权限
        if event.user_id and event.user_id not in self.allowed_user_ids:
            logger.warning(f"未授权用户尝试访问: {event.user_id}")
            # 临时禁用用户ID检查，允许所有用户访问
            logger.info(f"临时允许未授权用户访问: {event.user_id}")

        # 使用事件对象的 onProcess 方法处理事件
        await event.onProcess(self)

    #--------------------------------------------------------------------------
    # 用户状态管理
    #--------------------------------------------------------------------------

    def _set_user_state(self, user_id: int, command_type: CommandType) -> None:
        """
        设置用户状态

        记录用户当前的命令上下文，用于后续消息处理。

        Args:
            user_id: 用户ID
            command_type: 命令类型

        Returns:
            None
        """
        self._user_states[user_id] = {
            "command": command_type,
            "timestamp": asyncio.get_event_loop().time()
        }

    def _get_user_state(self, user_id: int) -> Optional[CommandType]:
        """
        获取用户状态

        获取用户当前的命令上下文，如果状态已过期则返回None。

        Args:
            user_id: 用户ID

        Returns:
            命令类型，如果没有状态或状态已过期则返回None
        """
        if user_id not in self._user_states:
            return None

        state = self._user_states[user_id]
        current_time = asyncio.get_event_loop().time()

        # 状态超过60秒视为过期
        if current_time - state["timestamp"] > 60:
            del self._user_states[user_id]
            return None

        return state["command"]

    #--------------------------------------------------------------------------
    # 消息处理
    #--------------------------------------------------------------------------

    async def _process_find_memo(self, update: Update) -> None:
        """
        处理查找备忘录

        根据用户输入的关键字查询备忘录，并发送查询结果。

        Args:
            update: Telegram更新对象

        Returns:
            None
        """
        keyword = update.message.text

        # 获取备忘录服务
        memo_service = provider.get_memo_service()

        # 查询备忘录
        memos = await memo_service.list_memos(keyword=keyword)

        if not memos:
            await self.bot.send_message(
                chat_id=update.effective_chat.id,
                text="未找到记录"
            )
            return

        # 发送查询结果
        for memo in memos:
            if memo.type == "image":
                # 获取图片URL
                storage_service = provider.get_storage_service()
                url = await storage_service.get_object_url(memo.file_key)

                # 下载图片并发送
                async with aiohttp.ClientSession() as session:
                    async with session.get(url) as response:
                        photo_data = await response.read()
                        await self.bot.send_photo(
                            chat_id=update.effective_chat.id,
                            photo=photo_data
                        )
            else:
                await self.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text=memo.text
                )

    async def _process_new_memo(self, update: Update) -> None:
        """
        处理新建备忘录

        根据消息类型创建不同类型的备忘录（文本、图片、文件）。

        Args:
            update: Telegram更新对象

        Returns:
            None
        """
        # 获取备忘录服务
        memo_service = provider.get_memo_service()

        if update.message.photo:
            # 处理图片
            photo = update.message.photo[-1]  # 获取最大尺寸的图片
            file = await self.bot.get_file(photo.file_id)
            file_data = await file.download_as_bytearray()

            # 创建图片备忘录
            memo = MemoEntity.ImageMemo(
                file_key="x",  # 临时键，将在创建过程中被替换
                text=update.message.caption or ""
            )

            await memo_service.create_file_memo(memo, file_data)

            await self.bot.send_message(
                chat_id=update.effective_chat.id,
                text="图片已收录!"
            )
        elif update.message.document:
            # 处理文档
            doc = update.message.document
            file = await self.bot.get_file(doc.file_id)
            file_data = await file.download_as_bytearray()

            # 获取文件扩展名
            file_name = doc.file_name
            file_ext = f".{file_name.split('.')[-1]}" if '.' in file_name else ""

            # 创建文件备忘录
            memo = MemoEntity.FileMemo(
                file_key="x",  # 临时键，将在创建过程中被替换
                file_type=file_ext,
                file_size=doc.file_size,
                text=file_name
            )

            await memo_service.create_file_memo(memo, file_data)

            await self.bot.send_message(
                chat_id=update.effective_chat.id,
                text=f"文件({file_name})已保存"
            )
        else:
            # 处理文本
            text = update.message.text

            # 创建文本备忘录
            memo = MemoEntity.TextMemo(text=text)

            await memo_service.create_memo(memo)

            await self.bot.send_message(
                chat_id=update.effective_chat.id,
                text="已录入"
            )

    async def _process_video_download(self, update: Update) -> None:
        """
        处理视频下载

        从消息中提取URL，下载视频并保存。

        Args:
            update: Telegram更新对象

        Returns:
            None
        """
        text = update.message.text

        # 提取URL
        url_match = re.search(r'(https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]', text)

        if not url_match:
            await self.bot.send_message(
                chat_id=update.effective_chat.id,
                text="不能理解指令!"
            )
            return

        video_url = url_match.group()

        # 获取视频服务
        video_service = provider.get_video_service()

        try:
            # 发送处理中消息
            await self.bot.send_message(
                chat_id=update.effective_chat.id,
                text=f"正在处理视频链接: {video_url}"
            )

            # 提取视频信息
            info = await video_service.extract_share_info(video_url)

            logger.info(f"视频信息: {info}")

            # 导入视频
            video = await video_service.import_from_share(
                title=info.get("title", ""),
                desc=info.get("desc", ""),
                cover_url=info.get("cover_url", ""),
                player_url=info.get("player_url", ""),
                video_id=info.get("video_id", ""),
                watch_url=info.get("watch_url", ""),
                duration=info.get("duration", 0),
                from_platform=info.get("from", "")
            )

            # 发送成功消息
            await self.bot.send_message(
                chat_id=update.effective_chat.id,
                text=f"视频下载成功: {info.get('title', video_url)} \n "
                     f"视频ID: {video.video_id} \n "
                     f"视频大小: {video.video_size} \n "
                     f"视频路径: {video.video_path} \n "
                     f"封面路径: {video.cover_path} \n "
                     f"视频URL: {video.play_url} \n "
                     f"来源URL: {video.source_url} \n "
                     f"来源平台: {video.platform} \n "
            )

        except Exception as e:
            logger.error(f"视频下载失败: {str(e)}", exc_info=True)
            await self.bot.send_message(
                chat_id=update.effective_chat.id,
                text=f"视频下载失败: {str(e)}"
            )
            raise
