#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通知服务模块

提供通知的创建、查询、标记已读等功能
"""

from typing import List, Dict, Any, Optional
from bson import ObjectId
from nebula.core.repository.notification import Entity, NotificationRepository
from nebula.core.infrastructure import provider, get_logger
from nebula.core.protocol.web import WebNotification
from nebula.core.protocol.base import ResponseMode


logger = get_logger(__name__)

class NotificationService:
    """通知服务类"""

    def __init__(self, settings: Optional[Any] = None):
        """初始化服务"""
        # if not settings.video or not settings.video.youtube_proxy or not settings.video.twitter_proxy:
        #     raise ConfigError("********** Video服务配置项缺失 **********")

        from nebula.core.infrastructure.message import MessageService

        self.repo = NotificationRepository()
        self.message_service = provider.get(MessageService)
        self.settings = settings

    async def create_notification(self, user_id: str, title: str, message: str, event: str,
                                 from_source: str = "系统", type: str = "info",
                                 metadata: Optional[Dict[str, Any]] = None,
                                 task_id: Optional[str] = None,
                                 to_web_topic: Optional[str] = None,
                                 send_websocket: bool = True) -> Entity.Notification:
        """创建通知"""
        # 创建通知实体
        notification = Entity.Notification(
            user_id=user_id,
            title=title,
            message=message,
            event=event,
            from_source=from_source,
            type=type,
            metadata=metadata,
            task_id=task_id
        )

        # 保存到数据库
        notification_id = await self.repo.save(notification)

        # 如果需要，发送WebSocket通知
        if send_websocket and self.message_service:
            web_notification = WebNotification(
                event="message.NOTIFICATION",
                to_web_topic=to_web_topic,
                text="有新通知",
                metadata={
                    "id": notification_id,
                    "from_source": from_source,
                    "type": type,
                    "title": title,
                    "message": message
                }
            )
            await self.message_service.publish_typed_message(web_notification)

        return notification

    async def get_user_notifications(self, user_id: str, skip: int = 0, limit: int = 50,
                                    include_read: bool = True) -> List[Entity.Notification]:
        """获取用户通知列表"""
        query = {"user_id": user_id}
        if not include_read:
            query["is_read"] = False
        return await self.repo.find_many(query, skip, limit, "created_at", -1)

    async def mark_as_read(self, notification_id: str) -> bool:
        """标记通知为已读"""
        # 查找通知
        notification = await self.repo.find_one({"_id": ObjectId(notification_id)})
        if not notification:
            return False

        # 标记为已读
        notification.is_read = True

        # 保存更新
        await self.repo.save(notification)
        return True

    async def mark_all_as_read(self, user_id: str) -> int:
        """标记用户所有通知为已读"""
        data = await self.repo.find_many({"user_id": user_id, "is_read": False})
        for x in data:
            x.is_read = True
            await self.repo.save(x)

    async def count_unread(self, user_id: str) -> int:
        """统计用户未读通知数量"""
        return await self.repo.count({"user_id": user_id, "is_read": False})

    async def get_notification(self, notification_id: str) -> Entity.Notification:
        """获取单个通知"""
        notification = await self.repo.find_one({"_id": ObjectId(notification_id)})
        if not notification:
            from nebula.core.infrastructure import NotFoundError
            raise NotFoundError(f"通知不存在: {notification_id}")
        return notification

    async def delete_notification(self, notification_id: str) -> bool:
        """删除通知"""
        return await self.repo.delete(notification_id)

    async def clear_all_notifications(self, user_id: str) -> int:
        """清空用户所有通知"""
        # 清空用户所有通知
        data = await self.repo.find_many({"user_id": user_id})
        for x in data:
            await self.repo.delete(x.id)
        return len(data)
