from datetime import datetime
from typing import List, Optional, Dict, Type, Any, Union
import json
from bson import ObjectId
from nebula.core.repository.memo import Entity, MemoRepository
from nebula.core.infrastructure import (
    singleton,Settings,
    NebulaException, NotFoundError, ValidationError, NotFoundError,error_boundary,
    get_logger, log_context, control,
    provider
)
from nebula.core.infrastructure.gemini import GeminiService

logger = get_logger(__name__)

class MemoService:
    """备忘录服务"""

    def __init__(self, settings: Settings = None):
        """初始化服务"""
        self.repo = MemoRepository()
        from nebula.core.infrastructure import storage
        self.storage = provider.get(storage.StorageService)
        self.settings = settings
        self.bucket = settings.memo.bucket

    async def get_memo(self, memo_id: str) -> Entity.BaseMemo:
        """获取单个备忘录"""
        memo = await self.repo.find_one({"_id": ObjectId(memo_id)})
        if not memo:
            raise NotFoundError("Memo", f"id={memo_id}")
        return memo

    async def count_memos(self, keyword: str = None, type: str = None) -> int:
        """统计备忘录数量"""
        query = {}
        if keyword:
            query["$or"] = [
                {"text": {"$regex": keyword, "$options": "i"}}
            ]
        if type:
            query["type"] = type
        return await self.repo.count(query)

    async def list_memos(self, keyword: str = None, type: str = None, skip: int = 0, limit: int = 20) -> List[Entity.BaseMemo]:
        """获取备忘录列表"""
        query = {}
        if keyword:
            query["$or"] = [
                {"text": {"$regex": keyword, "$options": "i"}}
            ]
        if type:
            query["type"] = type
        return await self.repo.find_many(query, skip, limit)

    async def create_memo(self, memo: Entity.BaseMemo) -> Entity.BaseMemo:
        """创建备忘录"""
        # 设置创建时间
        # memo.created_at = datetime.now()
        # 保存到数据库
        if type(memo) in (Entity.ImageMemo, Entity.FileMemo):
            memo = await self.process_file_memo(memo)
        await self.repo.save(memo)
        return memo
    
    async def create_file_memo(self, memo: Entity.FileMemo, file_bytes: bytes) -> Entity.BaseMemo:
        """创建备忘录"""
        # 上传文件到存储服务

        # 如果是图片则文件名为jpg,否则为file_type
        ext_name = ".jpeg" if isinstance(memo, Entity.ImageMemo) else memo.file_type

        _,file_key = await self.storage.upload_byte_to_temp(file_bytes, ext_name=ext_name, bucket_name=self.bucket)
        memo.file_key = file_key

        try:
            memo = await self.process_file_memo(memo)
            await self.repo.save(memo)
        except Exception as e:
            await self.storage.delete_object(file_key, bucket_name=self.bucket)
            raise e
        return memo
        
    @log_context
    @control(retry_probability=0, retry_message="故意抛出异常")
    async def update_memo(self, memo_id: str, memo: dict) -> Entity.BaseMemo:
        """更新备忘录"""
        
        # 检查是否存在
        info = await self.repo.find_one({"_id": ObjectId(memo_id)})
        if not info:
            raise NotFoundError("Memo", f"id={memo_id}")
        del memo["created_at"]
        for key, value in memo.items():
            setattr(info, key, value)

        logger.info (f"更新备忘录: {info}")
        # 保存更新
        await self.repo.save(info)
        return await self.get_memo(memo_id)

    async def delete_memo(self, memo_id: str) -> bool:
        """删除备忘录"""
        # 检查是否存在
        info = await self.repo.find_one({"_id": ObjectId(memo_id)})
        if not info:
            raise NotFoundError("Memo", f"id={memo_id}")

        # 如果有关联文件，删除存储的文件
        print (f"备忘录: {info}")
        if isinstance(info, (Entity.ImageMemo, Entity.FileMemo)) and info.file_key:
            try:
                status = await self.storage.delete_object(info.file_key, bucket_name=self.bucket)
                print (f"删除文件: {info.file_key} ({status})")
            except Exception as e:
                # 记录错误但不影响数据库删除
                print(f"删除文件失败: {str(e)}")
        # 从数据库删除
        return await self.repo.delete(memo_id)

    async def process_file_memo(self, memo: Entity.BaseMemo) -> Entity.BaseMemo:
        """处理文件类型的备忘录（图片或文件）"""
        memo_cls = {
            "image": Entity.ImageMemo,
            "file": Entity.FileMemo
        }[memo.type]

        # 从临时存储移动到永久存储
        source_key = memo.file_key
        if not source_key:
            raise ValidationError("临时文件key不能为空")

        # 构造目标路径
        filename = memo.file_key
        # 获取文件扩展名
        ext = ""
        if "." in filename:
            ext = "." + filename.split(".")[-1]

        # 使用uuid生成唯一文件名
        import uuid
        unique_id = uuid.uuid4()

        target_key = self.settings.memo.file_path.format(unique_id=unique_id, ext=ext)
        # 移动文件
        try:
            status = await self.storage.move_object(
                source_key,
                target_key,
                bucket_name=self.settings.memo.bucket
            )
            print (f"移动文件: {source_key} -> {target_key} ({status})")
        except Exception as e:
            raise NebulaException(f"Storage Error: 移动文件失败: {str(e)}")

        memo.file_key = target_key
        return memo

    @log_context
    @control(retry_probability=0.9, retry_message="故意抛出异常")
    async def update_content_analysis(self, id: str) -> Entity.BaseMemo:
        """更新备忘录内容分析结果

        Args:
            id: 备忘录ID

        Returns:
            更新后的备忘录实体
        """
        memo = await self.get_memo(id)
        if not memo:
            raise NotFoundError("Memo", f"id={id}")

        # 获取Gemini服务
        gemini = provider.get(GeminiService)

        # 根据备忘录类型选择不同的分析方法
        if memo.type == "text":
            # 文本备忘录直接分析文本内容
            prompt = """请以专业、客观的方式分析以下文本内容，并提供更丰富、更有深度的描述。
用"中文"生成一段对内容的深入分析和扩展（300字以内）。使用中文输出MarkDown格式
"""
            result = await gemini.generate_content_for_text(
                text=memo.text,
                prompt=prompt
            )

            # 更新备忘录内容
            memo.generated = True
            memo.gen_text = result

        elif memo.type == "image":
            # 图片备忘录分析图片内容
            file_url = await self.storage.get_presigned_url(memo.file_key, expires=600, bucket_name=self.bucket)
            prompt = """请分析图片内容,使用中文输出MarkDown格式.
            如果是聊天记录请输出他们对话的完整内容:
                A说: xxxx
                B说: xxxx
                C说: xxxx
            如果是社交媒体发帖截图,请输出帖子的具体内容:
                发帖人: author
                时间: xxxx
                发帖内容: xxxx
                评论:
                    A: 评论1
                    B: 评论2
            如果是照片:描述照片的内容或意义(讽刺/幽默/搞笑/...)
            如果是数据表:请输出数据表的结构和内容(注意对齐)
            如果是树图:请输出树图的结构和内容(注意对齐)
            如果是统计图:请以数据表的形式输出统计图的结构和内容(注意对齐)
            如果图片是纯文本或者其它类型的图片: 请将图片上的全部内容以OCR的方式完整输出.
"""
            result = await gemini.generate_content_for_file(file_url,prompt)

            # 更新备忘录内容
            memo.generated = True
            memo.gen_text = result

        elif memo.type == "file":
            # 文件备忘录分析文件内容
            file_url = await self.storage.get_presigned_url(memo.file_key, expires=600, bucket_name=self.bucket)
            prompt = """请分析文件内容,使用中文输出MarkDown格式.
            请概括文件核心内容,如果有数据则需整理成结构化数据.
"""
            result = await gemini.generate_content_for_file(file_url,prompt)

            # 更新备忘录内容
            memo.generated = True
            memo.gen_text = result

        elif memo.type == "share":
            # 分享备忘录分析分享内容
            prompt = f"""请以专业、客观的方式分析以下分享链接内容。
链接标题: {memo.text}
链接URL: {memo.link}
链接描述: {memo.desc or ''}

用"中文"生成一段对分享内容的详细分析（200字以内）。
"""
            result = await gemini.generate_content_for_text(
                text=f"标题: {memo.text}\n链接: {memo.link}\n描述: {memo.desc or ''}",
                prompt=prompt
            )

            # 更新备忘录内容
            memo.generated = True
            memo.gen_text = f"{memo.text}\n\nAI分析：\n{result}"

        # 保存更新后的备忘录
        await self.repo.save(memo)

        return memo