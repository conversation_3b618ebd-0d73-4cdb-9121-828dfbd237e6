#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
鉴权服务模块
处理JWT令牌的生成、验证等操作
"""

import jwt
from datetime import datetime, timedelta
from typing import Dict, Optional, List, Any

# 导入新组件
from nebula.core.infrastructure import (
    # 单例模式装饰器
    singleton,
    
    # 配置管理
    get_settings, Settings,
    
    # 异常处理
    NebulaException, ValidationError, error_boundary, AuthenticationError,
    
    # 日志系统
    get_logger, log_context
)
from nebula.core.infrastructure import provider
from nebula.core.infrastructure.cache import RedisCache

# 获取日志记录器
logger = get_logger(__name__)

class TokenData:
    """令牌数据模型"""
    def __init__(self, username: str, scopes: List[str] = None):
        self.username = username
        self.scopes = scopes or []

class UserProfile:
    """用户信息模型"""
    def __init__(self, username: str, email: str, is_admin: bool):
        self.username = username
        self.email = email
        self.is_admin = is_admin

@singleton
class AuthService:
    """鉴权服务，处理JWT令牌生成和验证"""
    
    def __init__(self, settings: Settings = None):
        """
        初始化鉴权服务
        
        Args:
            settings: 通过依赖注入提供的配置对象
        """
        # 通过依赖注入获取配置
        self.settings = settings or get_settings()
        self.cache = provider.get(RedisCache)
        
        # 从配置中获取JWT相关设置
        self.jwt_secret = self.settings.jwt.secret_key
        self.jwt_algorithm = self.settings.jwt.algorithm
        self.jwt_expire_minutes = self.settings.jwt.expire_minutes

    async def create_access_token(self, data: Dict, expires_delta: Optional[timedelta] = None) -> str:
        """
        创建JWT访问令牌
        
        Args:
            data: 要编码到令牌中的数据字典
            expires_delta: 令牌过期时间增量，如果未提供则使用默认值
            
        Returns:
            JWT令牌字符串
            
        Raises:
            ValidationError: 数据验证错误
        """
        async with error_boundary("创建JWT令牌"):
            # 验证必要字段
            if "username" not in data:
                raise ValidationError("缺少令牌主题(username)字段", {"sub": "username"})
                
            to_encode = data.copy()
            if expires_delta:
                expire = datetime.now() + expires_delta
            else:
                expire = datetime.now() + timedelta(minutes=self.jwt_expire_minutes)
            
            to_encode.update({"exp": expire})
            encoded_jwt = jwt.encode(to_encode, self.jwt_secret, algorithm=self.jwt_algorithm)
            
            await self.cache.set(encoded_jwt, data, self.jwt_expire_minutes * 60)
            logger.info(f"创建访问令牌成功: {encoded_jwt}")
            return encoded_jwt

    async def decode_token(self, token: str) -> Dict[str, Any]:
        """
        解码JWT令牌
        
        Args:
            token: JWT令牌字符串
            
        Returns:
            解码后的令牌数据
            
        Raises:
            AuthenticationError: 令牌解码失败
        """
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            logger.info("令牌解码成功", extra={"username": payload.get("username")})
            return payload
        except jwt.ExpiredSignatureError:
            # logger.warning("令牌已过期")
            raise AuthenticationError("令牌已过期")
        except jwt.InvalidTokenError as e:
            logger.warning(f"无效的令牌: {str(e)}")
            raise AuthenticationError(f"无效的令牌: {str(e)}")

    @log_context
    async def authenticate_token(self, token: str) -> Dict[str, Any]:
        """
        验证JWT令牌并返回用户信息
        
        Args:
            token: JWT令牌字符串
            
        Returns:
            包含用户信息的字典
            
        Raises:
            AuthenticationError: 令牌验证失败
        """
        async with error_boundary("验证JWT令牌"):
            # 解码JWT
            v = await self.cache.get(token)
            if v:
                logger.info("redis获取令牌成功", extra={"username": v.get("username")})
                return v    
            
            payload = await self.decode_token(token)
            username: str = payload.get("username")
            if username is None:
                logger.warning("令牌验证失败: 缺少用户标识")
                raise AuthenticationError("令牌验证失败: 缺少用户标识")
            
            # 检查是否是admin
            scopes = payload.get("scopes", [])
            is_admin = "admin" in scopes
            
            user_info = {"username": username, "is_admin": is_admin, "scopes": scopes}
            # logger.info("令牌验证成功", extra={"username": username, "is_admin": is_admin})
            return user_info

    def verify_scopes(self, required_scopes: List[str], token_scopes: List[str]) -> bool:
        """
        验证用户是否拥有所需权限
        
        Args:
            required_scopes: 需要的权限范围列表
            token_scopes: 令牌中包含的权限范围列表
        
        Returns:
            如果用户拥有所需权限则返回True，否则返回False
        """
        has_permission = all(scope in token_scopes for scope in required_scopes)
        logger.debug("权限验证", extra={
            "required_scopes": required_scopes,
            "token_scopes": token_scopes,
            "has_permission": has_permission
        })
        return has_permission

# auth_service的实例会在app_context中创建


