#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
杂项服务模块
提供各种不相关的功能
"""

import re
import hashlib
import datetime
from typing import List, Dict, Any, Optional

import aiohttp

from nebula.core.infrastructure import (
    singleton,
    Settings,
    NebulaException, ExternalServiceError, error_boundary,
    get_logger, log_context, calculate_time
)
from nebula.core.infrastructure import provider


# 获取日志记录器
logger = get_logger(__name__)


class MiscService:
    """杂项服务，提供各种不相关的功能"""

    def __init__(self, settings: Settings = None):
        """
        初始化杂项服务

        Args:
            settings: 通过依赖注入提供的配置对象
        """
        self.settings = settings
        from nebula.core.infrastructure.cache import RedisCache
        self.cache = provider.get(RedisCache)

        # 潮汐服务相关配置
        self.tide_base_url = "https://www.eisk.cn/Tides/815.html"
        self.tide_headers = {
            'cookie': 'AddrStr_ID=815; LaiFang=3',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36',
            'referer': 'https://www.eisk.cn/'
        }

    # 潮汐相关方法
    @log_context
    async def get_tide_data(self, date: str = None) -> List[Dict[str, Any]]:
        """
        获取潮汐数据

        Args:
            date: 日期字符串，格式为YYYY-MM-DD，如果为None则获取当前日期及未来7天的数据

        Returns:
            潮汐数据列表

        Raises:
            NebulaException: 获取或处理数据失败
        """
        @log_context
        async def _fetch_tide_html(day: str, cache_index: int) -> Optional[bytes]:
            """
            获取指定日期的潮汐HTML内容

            Args:
                day: 日期字符串，格式为YYYY-MM-DD
                cache_index: 缓存索引，用于计算缓存时间

            Returns:
                HTML内容，如果获取失败则返回None

            Raises:
                ExternalServiceError: 外部服务请求失败
            """
            async with error_boundary("获取潮汐HTML内容"):
                # 构造URL和缓存键
                url = f"{self.tide_base_url}?date={day}"
                cache_key = f"url:{hashlib.md5(url.encode()).hexdigest()}"

                # 尝试从缓存获取
                html_content = await self.cache.get(cache_key)
                if html_content:
                    logger.info(f"从缓存获取潮汐数据: {day}")
                    return html_content

                # 缓存未命中切日期大于等于今天，从网站获取
                if datetime.datetime.strptime(day, "%Y-%m-%d") < datetime.datetime.today():
                    return None

                try:
                    async with aiohttp.ClientSession() as session:
                        async with calculate_time(f"获取潮汐数据 ({day})", microsecond=False):
                            async with session.get(url, headers=self.tide_headers, timeout=10) as response:
                                if response.status != 200:
                                    logger.error(f"获取潮汐数据失败: HTTP {response.status} ({url})")
                                    return None

                                html_content = await response.read()

                    # 缓存结果，缓存时间根据日期远近设置
                    cache_time = 3600 * 24 * (cache_index + 1)  # 1-8天不等
                    await self.cache.set(cache_key, html_content, cache_time)

                    logger.info(f"成功获取潮汐数据: {day}")
                    return html_content
                except aiohttp.ClientError as e:
                    logger.error(f"请求潮汐数据失败: {str(e)}", exc_info=True)
                    raise ExternalServiceError("潮汐服务", f"请求失败: {str(e)}")

        @log_context
        async def _parse_tide_html(html_content: bytes, day: str) -> Optional[Dict[str, Any]]:
            """
            解析潮汐HTML内容，提取潮汐信息

            Args:
                html_content: HTML内容
                day: 日期字符串，格式为YYYY-MM-DD

            Returns:
                解析后的潮汐数据，如果解析失败则返回None
            """
            async with error_boundary("解析潮汐HTML内容"):
                try:
                    # 使用正则表达式提取数据
                    pattern = r'<div class="column column1">.*<div class="hour".*?>(?P<date>.*?)</div>\s*<div class="day".*?>\s*(?P<day>.*?)</div>\s*</div>\s*<div class="column column2">.*?<div class="humidity"( style="color: #.*?")?>\s*(?P<tide_type>.+?)</div>.+?<div class="description">\s*<span style="color: #.*?">\s*(?P<time1_name>.+?)时间：\s*(?P<time1_range>.*?)\s*</span>.*?<span style="color: #aaaaff">\s*(?P<time2_name>.+?)时间：\s*(?P<time2_range>.*?)\s*</span>'

                    # 确保html_content是字符串
                    if isinstance(html_content, bytes):
                        html_content_str = html_content.decode('utf-8')
                    else:
                        html_content_str = html_content

                    match = re.search(pattern, html_content_str, re.DOTALL)

                    if not match:
                        logger.warning(f"未找到潮汐数据: {day}")
                        return None

                    # 提取数据
                    tide_type = match.group("tide_type")
                    t1 = match.group("time1_range").split("；")
                    t2 = match.group("time2_range").split("；")

                    if t1 == ['']:
                        logger.warning(f"潮汐数据格式错误: {day}")
                        return None

                    # 处理时间和水位数据
                    format_str = "%Y-%m-%d %H:%M:%S"
                    md = datetime.datetime.strptime(f"{day} 00:00:00", format_str)
                    sec = 60 * 60 * 3 + 3600  # 时间偏移量

                    tf = []

                    # 处理第一组时间
                    bt = datetime.datetime.strptime(f"{day} {t1[0][0:5]}:00", format_str) + datetime.timedelta(seconds=sec)
                    et = datetime.datetime.strptime(f"{day} {t1[0][8:13]}:00", format_str) + datetime.timedelta(seconds=sec)
                    wl = float(t1[0].replace("(", "").replace("米)", "")[13:].replace("??", "-100")) - 0.3
                    tf.append({"direction": "涨潮", "text": t1[0][0:13], "wlevel": wl, "bt": bt, "et": et})

                    # 处理第一组时间的第二个时段（如果有）
                    if len(t1) >= 2 and t1[1]:
                        bt = datetime.datetime.strptime(f"{day} {t1[1][0:5]}:00", format_str) + datetime.timedelta(seconds=sec)
                        et = datetime.datetime.strptime(f"{day} {t1[1][8:13]}:00", format_str) + datetime.timedelta(seconds=sec)
                        if bt.day == md.day:
                            wl = float(t1[1].replace("(", "").replace("米)", "")[13:].replace("??", "-100.00")) - 0.3
                            tf.append({"direction": "涨潮", "text": t1[1][0:13], "wlevel": wl, "bt": bt, "et": et})

                    # 处理第二组时间
                    bt = datetime.datetime.strptime(f"{day} {t2[0][0:5]}:00", format_str) + datetime.timedelta(seconds=sec)
                    et = datetime.datetime.strptime(f"{day} {t2[0][8:13]}:00", format_str) + datetime.timedelta(seconds=sec)
                    if bt.day == md.day:
                        wl = float(t2[0].replace("(", "").replace("米)", "").replace("分)", "")[13:].replace("??", "-100")) + 0.1
                        tf.append({"direction": "退潮", "text": t2[0][0:13], "wlevel": wl, "bt": bt, "et": et})

                    # 处理第二组时间的第二个时段（如果有）
                    if len(t2) >= 2 and t2[1]:
                        wl = float(t2[1].replace("(", "").replace("米)", "").replace("分)", "")[13:].replace("??", "-100")) + 0.1
                        bt = datetime.datetime.strptime(f"{day} {t2[1][0:5]}:00", format_str) + datetime.timedelta(seconds=sec)
                        et = datetime.datetime.strptime(f"{day} {t2[1][8:13]}:00", format_str) + datetime.timedelta(seconds=sec)
                        if bt.day == md.day:
                            tf.append({"direction": "退潮", "text": t2[1][0:13], "wlevel": wl, "bt": bt, "et": et})

                    # 按时间排序
                    tf = sorted(tf, key=lambda x: x["bt"])

                    # 构造返回数据
                    data = {
                        'date': datetime.datetime.strptime(f"{day} 00:00:00", format_str),
                        'dayofweek': f"星期{{'1':'一','2':'二','3':'三','4':'四','5':'五','6':'六','0':'日'}}[{datetime.datetime.strptime(f'{day} 00:00:00', format_str).strftime('%w')}]",
                        'tide_type': tide_type,
                        'tf': tf
                    }

                    return data
                except Exception as e:
                    logger.error(f"解析潮汐数据失败: {str(e)}", exc_info=True)
                    return None

        @log_context
        async def _process_tide_data(data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
            """
            处理潮汐数据，填充缺失的时间段

            Args:
                data_list: 原始潮汐数据列表

            Returns:
                处理后的潮汐数据列表
            """
            async with error_boundary("处理潮汐数据"):
                # 处理数据，填充缺失的时间段
                for i, x in enumerate(data_list):
                    # 增加尾部
                    if x["tf"] and x["tf"][-1]["et"].strftime("%H:%M") == "03:29":
                        if i + 1 < len(data_list) and data_list[i+1]["tf"]:
                            x["tf"][-1]["et"] = data_list[i+1]["tf"][0]["bt"]

                for i, x in enumerate(data_list):
                    # 增加头部
                    if len(x["tf"]) == 3 and i > 0 and data_list[i-1]["tf"]:
                        if x["tf"][0]["bt"] != data_list[i-1]["tf"][-1]["et"]:
                            direction = {"涨潮": "退潮", "退潮": "涨潮"}.get(data_list[i-1]["tf"][-1]["direction"])
                            bt = data_list[i-1]["tf"][-1]["et"]
                            et = x["tf"][0]["bt"]
                            x["tf"].insert(0, {"bt": bt, "et": et, "direction": direction, "text": "", "wlevel": -100})

                return data_list


        async with error_boundary("获取潮汐数据"):
            data_list = []

            # 确定日期范围
            if date:
                # 如果指定了日期，只获取该日期的数据
                dates = [date]
            else:
                # 否则获取当前日期及未来7天的数据
                dates = []
                for i in range(8):
                    day = (datetime.datetime.today() + datetime.timedelta(days=i-1)).strftime("%Y-%m-%d")
                    dates.append(day)

            # 获取每个日期的数据
            for i, day in enumerate(dates):
                html_content = await _fetch_tide_html(day, i)
                # print(f'获取 {day} 的潮汐数据: {html_content}')
                if not html_content:
                    continue

                tide_data = await _parse_tide_html(html_content, day)
                if tide_data:
                    data_list.append(tide_data)

            # 处理数据，填充缺失的时间段
            processed_data = await _process_tide_data(data_list)

            # 如果指定日期没有数据，返回空列表
            if date and not processed_data:
                return []

            return processed_data

