#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
任务信息服务模块

提供任务状态管理的业务逻辑。
"""

import logging
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from nebula.core.infrastructure import provider
from nebula.core.infrastructure.config import Settings
from nebula.core.infrastructure.exceptions import NotFoundError
from nebula.core.repository.taskinfo import Entity as TaskEntity, TaskInfoRepository
from nebula.core.tasks.interface.task import Task
from nebula.core.infrastructure.exceptions import DuplicateError

logger = logging.getLogger(__name__)
print ('*')
print (__name__)
logger.info(f"test: {__name__}")

class TaskService:
    """任务服务，提供任务状态管理功能"""

    def __init__(self, settings: Settings = None):
        """初始化服务"""
        self.repo = TaskInfoRepository()
        from nebula.core.infrastructure.cache import RedisCache
        self.cache = provider.get(RedisCache)
        self.settings = settings
        self.redis_key_prefix = "task:status:"

    async def create_task_info(self, task: Task, params: Dict[str, Any] = None) -> TaskEntity.TaskInfo:
        """创建任务信息

        Args:
            task: 任务对象
            params: 任务参数
            description: 任务描述模板，可以包含{param_name}形式的占位符，例如："将域名 {domain} 的 {type} 记录 {record} 指向 {ip}"

        Returns:
            创建的任务信息
        """
        # 检查是否已存在
        exists = await self.repo.find_one({"task_id": task.task_id})
        if exists:
            logger.info(f"任务信息已存在: {task.task_id}")
            raise DuplicateError("TaskInfo", f"task_id={task.task_id}")

        # 创建任务信息
        task_info = TaskEntity.TaskInfo(
            task_id=task.task_id,
            task_name=task.task_name,
            task_type=task.task_type,
            status=TaskEntity.TaskStatus.PENDING,
            params=params,
            labels=task.labels,
            attempts=[TaskEntity.TaskAttempt(
                retry_index=0,
                started_at=datetime.now(),
                status=TaskEntity.TaskStatus.PENDING,
                error_message=None
            )]
        )

        # 保存到数据库
        await self.repo.save(task_info)

        # 保存到Redis
        await self._update_redis_status(task.task_id, TaskEntity.TaskStatus.PENDING)

        logger.info(f"已创建任务信息: {task.task_id}")
        return task_info

    async def update_task_status(
        self,
        task_id: str,
        status: TaskEntity.TaskStatus,
        error_message: str = None,
        result: Dict[str, Any] = None
    ) -> TaskEntity.TaskInfo:
        """更新任务状态

        Args:
            task_id: 任务ID
            status: 新状态
            error_message: 错误信息
            result: 执行结果

        Returns:
            更新后的任务信息

        Raises:
            NotFoundError: 任务信息不存在
        """
        # 查找任务信息
        task_info = await self.repo.find_one({"task_id": task_id})
        if not task_info:
            # 如果信息不存在，创建一个新信息
            logger.warning(f"任务信息不存在，创建新信息: {task_id}")
            raise NotFoundError("TaskInfo", f"task_id={task_id}")

        # 更新状态
        task_info.status = status
        task_info.updated_at = datetime.now()

        # 更新现有尝试记录
        attempt = next((a for a in task_info.attempts if a.retry_index == task_info.retry_count), None)
        attempt.status = status
        attempt.error_message = error_message
        # 根据状态设置其他字段
        if status == TaskEntity.TaskStatus.RUNNING:
            task_info.next_retry_at = None
            if task_info.retry_count > 0:
                task_info.status = TaskEntity.TaskStatus.RETRYING
            else:
                task_info.started_at = datetime.now()
        elif status in (TaskEntity.TaskStatus.SUCCESS, TaskEntity.TaskStatus.FAILED):
            task_info.finished_at = datetime.now()
            attempt.finished_at = datetime.now()
            attempt.elapsed_ms = int((attempt.finished_at.timestamp() - attempt.started_at.timestamp()) * 1000)

        # 设置错误信息和结果
        if error_message:
            task_info.error_message = error_message

        if result:
            task_info.result = result
            task_info.error_message = None


        # 保存到数据库
        await self.repo.save(task_info)

        # 更新Redis状态
        await self._update_redis_status(task_id, status)

        logger.info(f"已更新任务状态: {task_id} -> {status}")
        return task_info

    async def increment_retry_count(self, task_id: str, error: Exception, next_retry_at: datetime) -> TaskEntity.TaskInfo:
        """增加任务重试次数

        Args:
            task_id: 任务ID

        Returns:
            更新后的任务信息

        Raises:
            NotFoundError: 任务信息不存在
        """
        # 查找任务信息
        task_info = await self.repo.find_one({"task_id": task_id})
        if not task_info:
            raise NotFoundError("TaskInfo", f"task_id={task_id}")

        # 将当前尝试记录设置为失败
        attempt = next((a for a in task_info.attempts if a.retry_index == task_info.retry_count), None)
        attempt.status = TaskEntity.TaskStatus.FAILED
        attempt.finished_at = datetime.now()
        attempt.elapsed_ms = int((attempt.finished_at.timestamp() - attempt.started_at.timestamp()) * 1000)
        attempt.error_message = str(error)


        # 增加重试次数
        task_info.retry_count += 1
        task_info.status = TaskEntity.TaskStatus.WAITRETRY
        task_info.updated_at = datetime.now()
        task_info.next_retry_at = next_retry_at

        # 创建任务尝试记录
        attempt = TaskEntity.TaskAttempt(
            retry_index=task_info.retry_count,
            started_at=next_retry_at,
            status=TaskEntity.TaskStatus.PENDING,   # 新纪录是pending状态,区别与主记录
            error_message=None
        )
        task_info.attempts.append(attempt)

        # 日志记录由外部系统处理，不在应用中直接写入MongoDB

        # 保存到数据库
        await self.repo.save(task_info)

        # 更新Redis状态
        await self._update_redis_status(task_id, TaskEntity.TaskStatus.RETRYING)

        logger.info(f"已增加任务重试次数: {task_id} -> {task_info.retry_count}")
        return task_info

    # 日志记录由外部系统处理，不在应用中直接写入MongoDB

    async def get_task_info(self, task_id: str, include_logs: bool = False) -> Optional[Union[TaskEntity.TaskInfo, TaskEntity.TaskInfoWithLogs]]:
        """获取任务信息

        Args:
            task_id: 任务ID
            include_logs: 是否包含日志信息

        Returns:
            任务信息或None，当include_logs=True时返回TaskInfoWithLogs
        """
        print ("get_task_info")
        info = await self.repo.find_one({"task_id": task_id}, include_logs=include_logs)
        if not info:
            raise NotFoundError("TaskInfo", f"task_id={task_id}")

        return info

    async def get_task_status(self, task_id: str) -> Optional[TaskEntity.TaskStatus]:
        """获取任务状态

        首先尝试从Redis获取，如果不存在则从数据库获取

        Args:
            task_id: 任务ID

        Returns:
            任务状态或None
        """
        # 尝试从Redis获取
        redis_key = f"{self.redis_key_prefix}{task_id}"
        status = await self.cache.get(redis_key)

        if status:
            return TaskEntity.TaskStatus(status)

        # 从数据库获取
        task_info = await self.get_task_info(task_id)
        if task_info:
            return task_info.status

        return None

    async def _build_filter_query(self, status: Optional[TaskEntity.TaskStatus] = None, keywords: Optional[str] = None) -> Dict[str, Any]:
        """构建过滤查询条件

        Args:
            status: 任务状态
            keywords: 搜索关键词

        Returns:
            过滤查询条件
        """
        query = {}
        if status:
            query["status"] = status
        if keywords:
            query["$or"] = [
                {"task_id": {"$regex": keywords, "$options": "i"}},
                {"task_name": {"$regex": keywords, "$options": "i"}},
                {"labels": {"$regex": keywords, "$options": "i"}},
                {"params": {"$regex": keywords, "$options": "i"}},
                {"error_message": {"$regex": keywords, "$options": "i"}},
                {"result": {"$regex": keywords, "$options": "i"}},
            ]

        return query

    async def list_tasks(
        self,
        status: Optional[TaskEntity.TaskStatus] = None,
        keywords: Optional[str] = None,
        skip: int = 0,
        limit: int = 20
    ) -> List[TaskEntity.TaskInfo]:
        """获取任务列表

        Args:
            status: 过滤的状态
            keywords: 过滤的关键词
            skip: 跳过数量
            limit: 限制数量

        Returns:
            任务信息列表
        """
        query = await self._build_filter_query(status, keywords)

        return await self.repo.find_many(query, skip, limit, "created_at", -1)

    async def count_tasks(
        self,
        status: Optional[TaskEntity.TaskStatus] = None,
        keywords: Optional[str] = None
    ) -> int:
        """统计任务数量

        Args:
            status: 过滤的状态
            keywords: 过滤的关键词

        Returns:
            任务数量
        """
        query = await self._build_filter_query(status, keywords)

        return await self.repo.count(query)

    async def _update_redis_status(self, task_id: str, status: TaskEntity.TaskStatus) -> None:
        """更新Redis中的任务状态

        Args:
            task_id: 任务ID
            status: 任务状态
        """
        redis_key = f"{self.redis_key_prefix}{task_id}"
        await self.cache.set(redis_key, status.value, ex=86400)  # 24小时过期

    async def get_task_logs(self, task_id: str, skip: int = 0, limit: int = 100) -> List[TaskEntity.TaskLog]:
        """获取任务日志

        Args:
            task_id: 任务ID
            skip: 跳过数量
            limit: 限制数量

        Returns:
            日志列表
        """
        return await self.repo.find_logs(task_id, skip, limit)
