"""
服务层辅助函数
提供类型安全的服务访问函数
"""
from typing import Optional, Any
import logging
from nebula.core.infrastructure import provider as infra_provider

# 获取日志记录器
logger = logging.getLogger(__name__)

def get_redis_service():
    """获取Redis服务"""
    from nebula.core.infrastructure import cache
    return infra_provider.get(cache.RedisCache)

def get_storage_service():
    """获取存储服务"""
    from nebula.core.infrastructure import storage
    return infra_provider.get(storage.StorageService)

def get_message_service():
    """获取消息服务"""
    from nebula.core.infrastructure import message
    return infra_provider.get(message.MessageService)

def get_auth_service():
    """获取认证服务"""
    from nebula.core.service import auth
    return infra_provider.get(auth.AuthService)

def get_cloudflare_service():
    """获取Cloudflare服务"""
    from nebula.core.service import cloudflare
    return infra_provider.get(cloudflare.CloudflareService)

def get_memo_service():
    """获取备忘录服务"""
    from nebula.core.service import memo
    return infra_provider.get(memo.MemoService)

def get_video_service():
    """获取视频服务"""
    from nebula.core.service import video
    return infra_provider.get(video.VideoService)

def get_gemini_service():
    """获取Gemini服务"""
    from nebula.core.infrastructure import gemini
    return infra_provider.get(gemini.GeminiService)

def get_notification_service():
    """获取通知服务"""
    from nebula.core.service import notification
    return infra_provider.get(notification.NotificationService)

def get_telegram_service():
    """获取Telegram服务"""
    from nebula.core.service import telegram
    return infra_provider.get(telegram.TelegramService)

def get_misc_service():
    """获取杂项服务"""
    from nebula.core.service import misc
    return infra_provider.get(misc.MiscService)

def get_task_service():
    """获取任务服务"""
    from nebula.core.service import taskman
    return infra_provider.get(taskman.TaskService)