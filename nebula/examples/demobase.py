import asyncio
import logging

from nebula.core.infrastructure.browser import BrowserService
from nebula.core.infrastructure.config import get_settings
from nebula.core.infrastructure.logging import get_logger, setup_logging
import nebula.core.service as service
import nebula.core.context as context

logger = logging.getLogger("api")

COMPONENTS = ["database","cache","auth","cloudflare","message","storage", "memo","video","gemini","browser"]

async def core():
    app_ctx = await context.init_application(
        app_name="Nebula API",
        debug=True,
        components=COMPONENTS
    )
    # 日志配置已由上下文完成
    logger.info("应用初始化完成")
    return app_ctx

def loadcore():
    app_ctx = asyncio.run(core())
    return app_ctx


