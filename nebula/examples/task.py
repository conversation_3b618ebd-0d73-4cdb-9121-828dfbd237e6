from typing import Any, Coroutine, Union
from taskiq import TaskiqDepends
from taskiq_redis import RedisAsyncResultBackend
from taskiq_aio_pika import AioPikaBroker
import asyncio
from urllib.parse import quote
import logging
from taskiq import TaskiqMessage, TaskiqMiddleware
import json

logger = logging.getLogger(__name__)

# def get_broker(pwd, host, port):
#     redis_url = f"redis://{host}:{port}/0"
#     print (redis_url)
#     result_backend = RedisAsyncResultBackend(
#         redis_url=redis_url,
#         )
#     return result_backend

# get_broker("yi3K_6UyCZDh-2*Bu7.v", "nas.fee.red", 6369)






class ClientMiddleware(TaskiqMiddleware):
    def __init__(self):

        print ("____ClientMiddleware_____")


    def pre_send(
        self,
        message: "TaskiqMessage",
    ) -> "Union[TaskiqMessage, Coroutine[Any, Any, TaskiqMessage]]":
        print ("pre_send"*10)
        return message
    
    def post_send(
        self,
        message: "TaskiqMessage",
    ) -> Union[None, Coroutine[Any, Any, None], "CoroutineType[Any, Any, None]"]:
        print ("post_send"*10)
        return None



def get_broker():
    from nebula.core.infrastructure.config import get_settings
    settings = get_settings()
    broker_url = settings.taskiq.broker_url
    result_backend_url = settings.taskiq.result_backend_url
    queue_name = settings.taskiq.queue_name
    routing_key = settings.taskiq.routing_key

    logger.info(f"使用AioPikaBroker: {broker_url}")
    # 使用支持Redis Streams的结果后端

    result_backend = RedisAsyncResultBackend(
        redis_url=result_backend_url
    )

    # 创建Broker
    broker = AioPikaBroker(
        broker_url,
        queue_name=queue_name,
        routing_key=routing_key
    ).with_result_backend(result_backend)

    return broker


broker = get_broker()
broker = broker.with_middlewares(ClientMiddleware())
print(f'来自生产者调度器, 附加ClientMiddleware')
from taskiq import BrokerMessage
message = BrokerMessage(
    task_id="SS",
    task_name="hello",
    message=json.dumps({"aaa":"xxx"}).encode("utf-8"),
    labels={"aaa":"xxx"}
)

@broker.task
async def hello(name: str):
    print(f"Hello, {name}")

import asyncio
async def go():
    await broker.startup()
    # await broker.kick(message)
    await hello.kiq(name="World")
    message = BrokerMessage(
        task_id="manual-task-id",
        task_name="hello",  # 与 @broker.task 定义的一致
        message=json.dumps(["World"]).encode("utf-8"),  # 注意：这里是参数列表序列化后的字节
        labels={"aaa":"xxx"}
    )

    await broker.kick(message)
    await asyncio.sleep(10)
    await broker.shutdown()


if __name__ == "__main__":
    print ("send...")
    asyncio.run(
        go()
    )
    print ("--end---")