curl -X POST "http://192.168.16.160/api/gemini/generate/file/stream" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsInNjb3BlcyI6WyJhZG1pbiJdLCJleHAiOjE3NDQ1NjQwMDB9.2WeSP2dGXGFD8yk57_kqiK5uERlN3XLPOjljORp8-O0" \
  -d '{
    "file_url": "https://z.fee.red/nebula/video/douyin/a17ed7ce-ffa7-11ef-9b66-0242c0a81064.mp4",
    "prompt": "描述这个视频的内容"
  }'

curl -X POST "http://192.168.16.160/api/gemini/generate/file/stream" -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN" -d '{"file_url":"https://z.fee.red/nebula/video/douyin/a17ed7ce-ffa7-11ef-9b66-0242c0a81064.mp4","prompt":"请以专业、客观的方式分析以下视频标签。用中文生成内容：一个简洁且描述性强的标题，字数不超过80个字,一段客观的内容概述（800字以内）,3-5个相关的关键词标签,输出不包含时间戳的字幕: 如果非中文,请翻译成中文(注意:每3~5句话合并成一个chunk，这样既不会过短影响语义理解，也不会过长影响向量检索性能)","use_structured_response":true,"response_format":{"title":"标题","overview":"概述","highlights":["核心摘要或关键亮点1","核心摘要或关键亮点2","核心摘要或关键亮点3"],"location":"地点","category":"分类","sub_category":"子分类","keywords":["关键词1","关键词2"],"subtitles_chunks":["字幕1","字幕2","字幕3","字幕4"]}}'

  TOKEN=$(curl -s -X POST "http://192.168.16.160/api/auth" -H "Content-Type: application/json" -d '{"username": "admin", "password": "admin"}' | grep -o '"token":"[^"]*' | cut -d'"' -f4) && curl -s -N -X POST "http://192.168.16.160/api/gemini/generate/text/stream" -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN" -d '{"text": "我想了解人工智能的发展历史", "prompt": "请用简洁的中文总结这个主题的关键点，并列出5个重要的里程碑事件"}'



  curl -X POST "http://192.168.16.160/api/gemini/generate/file" -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsInNjb3BlcyI6WyJhZG1pbiJdLCJleHAiOjE3NDQ1NjQwMDB9.2WeSP2dGXGFD8yk57_kqiK5uERlN3XLPOjljORp8-O0" -d '{"file_url":"https://z.fee.red/nebula/video/douyin/a17ed7ce-ffa7-11ef-9b66-0242c0a81064.mp4","prompt":"请以专业、客观的方式分析以下视频标签。用中文生成内容：一个简洁且描述性强的标题，字数不超过80个字,一段客观的内容概述（800字以内）,3-5个相关的关键词标签,输出不包含时间戳的字幕: 如果非中文,请翻译成中文(注意:每3~5句话合并成一个chunk，这样既不会过短影响语义理解，也不会过长影响向量检索性能)","use_structured_response":true,"response_format":{"title":"标题","overview":"概述","highlights":["核心摘要或关键亮点1","核心摘要或关键亮点2","核心摘要或关键亮点3"],"location":"地点","category":"分类","sub_category":"子分类","keywords":["关键词1","关键词2"],"subtitles_chunks":["字幕1","字幕2","字幕3","字幕4"]}}'

https://z.fee.red/nebula/video/douyin/a17ed7ce-ffa7-11ef-9b66-0242c0a81064.mp4


请以专业、客观的方式分析以下视频标签。用中文生成内容：一个简洁且描述性强的标题，字数不超过80个字,一段客观的内容概述（800字以内）,3-5个相关的关键词标签,输出不包含时间戳的字幕: 如果非中文,请翻译成中文(注意:每3~5句话合并成一个chunk，这样既不会过短影响语义理解，也不会过长影响向量检索性能)


 {"title":"标题","overview":"概述","highlights":["核心摘要或关键亮点1","核心摘要或关键亮点2","核心摘要或关键亮点3"],"location":"地点","category":"分类","sub_category":"子分类","keywords":["关键词1","关键词2"],"subtitles_chunks":["字幕1","字幕2","字幕3","字幕4"]} 