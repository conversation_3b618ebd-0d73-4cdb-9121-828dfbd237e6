[program:worker]
directory=/nebula/nebula/worker
command=taskiq worker nebula.core.tasks.broker:broker nebula.core.tasks.definitions --worker=1
numprocs=1
autostart=true
autorestart=unexpected
startretries=3
stopasgroup=true
killasgroup=true
stdout_logfile=/tmp/worker.log
stdout_logfile_maxbytes=1MB
stdout_logfile_backups=1
redirect_stderr=true
environment=PYTHONWARNINGS="ignore::UserWarning:pydantic._internal._generate_schema"

[program:scheduler]
directory=/nebula/nebula/worker
command=taskiq scheduler nebula.core.tasks.scheduler:scheduler nebula.core.tasks.definitions --skip-first-run
numprocs=1
autostart=true
autorestart=unexpected
startretries=3
stopasgroup=true
killasgroup=true
stdout_logfile=/tmp/scheduler.log
stdout_logfile_maxbytes=1MB
stdout_logfile_backups=1
redirect_stderr=true
environment=PYTHONWARNINGS="ignore::UserWarning:pydantic._internal._generate_schema"