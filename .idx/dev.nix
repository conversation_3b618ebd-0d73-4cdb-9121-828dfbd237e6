{ pkgs, ... }: {
  # Use the unstable channel for newer packages
  channel = "unstable";

  # Packages needed for Python (FastAPI), Node.js, Docker, and general development
  packages = [
    pkgs.python311
    pkgs.python311Packages.pip
    pkgs.python311Packages.uvicorn # ASGI server for FastAPI
    pkgs.nodejs_20              # Node.js for frontend
    pkgs.nodePackages.npm         # npm for frontend package management
    pkgs.docker                 # Docker CLI
    pkgs.docker-compose         # Docker Compose
    pkgs.git                    # Version control
    pkgs.curl                   # Command-line URL transfer
    pkgs.jq                     # JSON processor
    pkgs.gcc                    # Needed for some Python packages with C extensions
  ];

  # Environment variables can be set here if needed
  env = {};

  idx = {
    # Recommended VS Code extensions
    extensions = [
      "ms-python.python"             # Python support
      "ms-azuretools.vscode-docker"  # Docker integration
      "esbenp.prettier-vscode"       # Code formatter (optional, good for web)
    ];

    # Configure previews for the backend API and frontend app
    previews = {
      enable = true;
      previews = {
        # Preview for the FastAPI backend running on port 8000
        api = {
          command = ["uvicorn", "nebula.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"];
          manager = "web";
          # Use IDX's dynamic port mapping for the preview URL, but the app runs on 8000 inside
          env = { PORT = "8000"; };
        };
        # Preview for the frontend development server (assuming it runs on 8080)
        frontend = {
          # Assuming a 'dev' script in frontend-web/package.json that starts the server
          # Adjust the command if your start script or port is different
          command = ["npm", "run", "dev", "--prefix", "frontend-web", "--", "--port", "8080"];
          manager = "web";
          # Use IDX's dynamic port mapping for the preview URL, but the app runs on 8080 inside
          env = { PORT = "8080"; };
        };
      };
    };

    # Workspace lifecycle hooks
    workspace = {
      # Runs when a workspace is first created
      onCreate = {
        # Install Python dependencies
        pip-install-core = "pip install --user -e ."; # Install the 'nebula' core package
        pip-install-reqs = "pip install --user -r requirements.txt && pip install --user -r nebula/api/requirements.txt && pip install --user -r nebula/consumer/requirements.txt"; # Install requirements

        # Install frontend dependencies
        npm-install = "npm install --prefix frontend-web";

        # Open relevant files by default
        default.openFiles = [
           ".idx/dev.nix"
           "README.md"
           "docker-compose.yml"
           "nebula/api/main.py"
           "nebula/consumer/main.py"
           "frontend-web/index.html"
           "frontend-web/js/app.js" # Added app.js as likely relevant
        ];
      };

      # Runs when the workspace is (re)started (can add commands here if needed)
      onStart = {
         # Example: Echo Python version
         # check-python = "python --version";
         # Example: Echo Node version
         # check-node = "node --version";
      };
    };
  };
}
