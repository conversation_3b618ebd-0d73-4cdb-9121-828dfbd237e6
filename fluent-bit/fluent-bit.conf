[SERVICE]
    Flush        1
    Log_Level    info
    Parsers_File parsers.conf

[INPUT]
    Name             tail
    Path             /tmp/worker.log
    Tag              nebula.worker
    Refresh_Interval 5
    Mem_Buf_Limit    5MB
    Skip_Long_Lines  Off
    multiline.parser nebula_multiline

[FILTER]
    Name    grep
    Match   nebula.worker
    Regex   log TASK-LOG >>

[FILTER]
    Name    parser
    Match   nebula.worker
    Key_Name    log
    Parser  nebula_log
    Reserve_Data    On
    Preserve_Key    Off

[OUTPUT]
    Name   stdout
    Match  nebula.worker
    Format json_lines


[OUTPUT]
    Name            forward
    Match           nebula.worker
    Host            fluentd
    Port            24224