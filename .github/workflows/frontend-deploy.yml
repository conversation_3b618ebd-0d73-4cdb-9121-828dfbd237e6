# GitHub Actions workflow for deploying frontend nginx to Cloud Run (updated)
name: Frontend Nginx Deploy

on:
  push:
    branches:
      - main
    paths:
      - 'nginx/cloudrun/**'
      - '.github/workflows/frontend-deploy.yml'

jobs:
  build-and-deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest

    # 这些权限是工作负载身份联合所必需的
    permissions:
      contents: 'read'
      id-token: 'write'
      packages: 'write'

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      # 使用工作负载身份联合进行身份验证
      - name: Authenticate to Google Cloud
        id: auth
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.SERVICE_ACCOUNT }}
          token_format: 'access_token'

      # 如果上面的身份验证方法失败，可以取消注释以下备选方法
      # - name: Authenticate to Google Cloud (Alternative)
      #   id: auth-alt
      #   uses: google-github-actions/auth@v2
      #   with:
      #     credentials_json: ${{ secrets.GCP_SA_KEY }}
      #     token_format: 'access_token'

      - name: Set up Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      # 使用 Docker 登录到 Google Artifact Registry
      - name: Docker Login to Google Artifact Registry
        uses: docker/login-action@v3
        with:
          registry: asia-east1-docker.pkg.dev
          username: oauth2accesstoken
          password: ${{ steps.auth.outputs.access_token }}
          # 如果使用备选身份验证方法，请取消注释以下行并注释上面的password行
          # password: ${{ steps.auth-alt.outputs.access_token }}

      # 设置 Docker Buildx
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          install: true
          driver-opts: |
            image=moby/buildkit:latest

      # 构建和推送 Docker 镜像
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./nginx/cloudrun
          push: true
          tags: asia-east1-docker.pkg.dev/${{ secrets.GCP_PROJECT_ID }}/nebula/frontend-nginx:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      # 部署到 Cloud Run
      - name: Deploy to Cloud Run
        id: deploy
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: frontend-nginx
          region: asia-east1
          image: asia-east1-docker.pkg.dev/${{ secrets.GCP_PROJECT_ID }}/nebula/frontend-nginx:${{ github.sha }}
          flags: --allow-unauthenticated

      - name: Show Output
        run: echo ${{ steps.deploy.outputs.url }}
